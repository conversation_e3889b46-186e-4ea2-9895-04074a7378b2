import type { CollectionConfig } from 'payload'

import { authenticated } from '../../access/authenticated'

// using for logging in into the payloadcms admin
export const Admin: CollectionConfig = {
  slug: 'admin',
  access: {
    admin: authenticated,
    create: authenticated,
    delete: authenticated,
    read: authenticated,
    update: authenticated,
  },
  admin: {
    defaultColumns: ['name', 'email'],
    useAsTitle: 'name',
  },
  auth: true,
  fields: [
    {
      name: 'name',
      type: 'text',
    },
  ],
  timestamps: true,
}
