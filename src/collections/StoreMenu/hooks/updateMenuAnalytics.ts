import type { CollectionBefore<PERSON><PERSON>eHook } from 'payload'

// Hook to update menu analytics when certain actions occur
export const updateMenuAnalytics: CollectionBeforeChangeHook = async ({ data, operation, req }) => {
  // Only update analytics on update operations
  if (operation !== 'update') {
    return data
  }

  // This hook can be extended to update analytics based on various triggers
  // For now, it's a placeholder for future analytics logic
  
  return data
}

// Utility function to increment menu views
export const incrementMenuViews = async (menuId: string, payload: any) => {
  try {
    const menu = await payload.findByID({
      collection: 'store-menus',
      id: menuId,
    })

    if (menu) {
      await payload.update({
        collection: 'store-menus',
        id: menuId,
        data: {
          analytics: {
            ...menu.analytics,
            views: (menu.analytics?.views || 0) + 1,
          },
        },
        context: {
          disableRevalidate: true, // Prevent revalidation for analytics updates
        },
      })
    }
  } catch (error) {
    payload.logger.error(`Failed to increment menu views for ${menuId}:`, error)
  }
}

// Utility function to increment menu favorites
export const incrementMenuFavorites = async (menuId: string, payload: any) => {
  try {
    const menu = await payload.findByID({
      collection: 'store-menus',
      id: menuId,
    })

    if (menu) {
      await payload.update({
        collection: 'store-menus',
        id: menuId,
        data: {
          favoritesCount: (menu.favoritesCount || 0) + 1,
        },
        context: {
          disableRevalidate: true,
        },
      })
    }
  } catch (error) {
    payload.logger.error(`Failed to increment menu favorites for ${menuId}:`, error)
  }
}

// Utility function to decrement menu favorites
export const decrementMenuFavorites = async (menuId: string, payload: any) => {
  try {
    const menu = await payload.findByID({
      collection: 'store-menus',
      id: menuId,
    })

    if (menu) {
      await payload.update({
        collection: 'store-menus',
        id: menuId,
        data: {
          favoritesCount: Math.max((menu.favoritesCount || 0) - 1, 0),
        },
        context: {
          disableRevalidate: true,
        },
      })
    }
  } catch (error) {
    payload.logger.error(`Failed to decrement menu favorites for ${menuId}:`, error)
  }
}

// Utility function to update menu rating
export const updateMenuRating = async (menuId: string, newRating: number, payload: any) => {
  try {
    const menu = await payload.findByID({
      collection: 'store-menus',
      id: menuId,
    })

    if (menu) {
      const currentAverage = menu.rating?.average || 0
      const currentCount = menu.rating?.count || 0
      
      // Calculate new average rating
      const newCount = currentCount + 1
      const newAverage = ((currentAverage * currentCount) + newRating) / newCount

      await payload.update({
        collection: 'store-menus',
        id: menuId,
        data: {
          rating: {
            average: Math.round(newAverage * 10) / 10, // Round to 1 decimal place
            count: newCount,
          },
        },
        context: {
          disableRevalidate: true,
        },
      })
    }
  } catch (error) {
    payload.logger.error(`Failed to update menu rating for ${menuId}:`, error)
  }
}
