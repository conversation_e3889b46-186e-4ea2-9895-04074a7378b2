import type { CollectionConfig } from 'payload'

import { authenticated } from '../../access/authenticated'
import { anyone } from '../../access/anyone'
import { slugField } from '@/fields/slug'

export const StoreMenu: CollectionConfig = {
  slug: 'store-menus',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'store', 'isActive', 'rating', 'favoritesCount', 'updatedAt'],
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      index: true,
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
    },
    {
      name: 'store',
      type: 'relationship',
      relationTo: 'stores',
      required: true,
      index: true,
    },
    {
      type: 'tabs',
      tabs: [
        {
          label: 'Basic Information',
          fields: [
            {
              name: 'images',
              type: 'array',
              required: true,
              minRows: 1,
              maxRows: 5,
              fields: [
                {
                  name: 'image',
                  type: 'upload',
                  relationTo: 'media',
                  required: true,
                },
                {
                  name: 'alt',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'isPrimary',
                  type: 'checkbox',
                  defaultValue: false,
                },
              ],
            },
            {
              name: 'category',
              type: 'relationship',
              relationTo: 'store-categories',
              required: false,
            },
            {
              name: 'price',
              type: 'group',
              fields: [
                {
                  name: 'amount',
                  type: 'number',
                  required: true,
                  min: 0,
                },
                {
                  name: 'currency',
                  type: 'text',
                  defaultValue: 'USD',
                  required: true,
                },
                {
                  name: 'originalPrice',
                  type: 'number',
                  required: false,
                  admin: {
                    description: 'Original price if on sale',
                  },
                },
              ],
            },
            {
              name: 'tags',
              type: 'array',
              fields: [
                {
                  name: 'tag',
                  type: 'text',
                  required: true,
                },
              ],
            },
          ],
        },
        {
          label: 'Menu Details',
          fields: [
            {
              name: 'items',
              type: 'array',
              required: true,
              minRows: 1,
              fields: [
                {
                  name: 'name',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'description',
                  type: 'textarea',
                  required: false,
                },
                {
                  name: 'price',
                  type: 'number',
                  required: true,
                  min: 0,
                },
                {
                  name: 'image',
                  type: 'upload',
                  relationTo: 'media',
                  required: false,
                },
                {
                  name: 'dietary',
                  type: 'array',
                  fields: [
                    {
                      name: 'type',
                      type: 'select',
                      options: [
                        { label: 'Vegetarian', value: 'vegetarian' },
                        { label: 'Vegan', value: 'vegan' },
                        { label: 'Gluten-Free', value: 'gluten-free' },
                        { label: 'Dairy-Free', value: 'dairy-free' },
                        { label: 'Nut-Free', value: 'nut-free' },
                        { label: 'Keto', value: 'keto' },
                        { label: 'Low-Carb', value: 'low-carb' },
                        { label: 'Halal', value: 'halal' },
                        { label: 'Kosher', value: 'kosher' },
                      ],
                    },
                  ],
                },
                {
                  name: 'allergens',
                  type: 'array',
                  fields: [
                    {
                      name: 'allergen',
                      type: 'select',
                      options: [
                        { label: 'Nuts', value: 'nuts' },
                        { label: 'Dairy', value: 'dairy' },
                        { label: 'Eggs', value: 'eggs' },
                        { label: 'Soy', value: 'soy' },
                        { label: 'Wheat', value: 'wheat' },
                        { label: 'Fish', value: 'fish' },
                        { label: 'Shellfish', value: 'shellfish' },
                      ],
                    },
                  ],
                },
                {
                  name: 'isAvailable',
                  type: 'checkbox',
                  defaultValue: true,
                },
                {
                  name: 'isPopular',
                  type: 'checkbox',
                  defaultValue: false,
                },
              ],
            },
          ],
        },
        {
          label: 'Availability & Scheduling',
          fields: [
            {
              name: 'availability',
              type: 'group',
              fields: [
                {
                  name: 'isActive',
                  type: 'checkbox',
                  defaultValue: true,
                },
                {
                  name: 'isTimeLimited',
                  type: 'checkbox',
                  defaultValue: false,
                },
                {
                  name: 'availableFrom',
                  type: 'date',
                  required: false,
                  admin: {
                    condition: (_, siblingData) => siblingData?.isTimeLimited,
                    date: {
                      pickerAppearance: 'dayAndTime',
                    },
                  },
                },
                {
                  name: 'availableUntil',
                  type: 'date',
                  required: false,
                  admin: {
                    condition: (_, siblingData) => siblingData?.isTimeLimited,
                    date: {
                      pickerAppearance: 'dayAndTime',
                    },
                  },
                },
                {
                  name: 'daysAvailable',
                  type: 'array',
                  fields: [
                    {
                      name: 'day',
                      type: 'select',
                      options: [
                        { label: 'Monday', value: 'monday' },
                        { label: 'Tuesday', value: 'tuesday' },
                        { label: 'Wednesday', value: 'wednesday' },
                        { label: 'Thursday', value: 'thursday' },
                        { label: 'Friday', value: 'friday' },
                        { label: 'Saturday', value: 'saturday' },
                        { label: 'Sunday', value: 'sunday' },
                      ],
                    },
                  ],
                },
                {
                  name: 'timeSlots',
                  type: 'array',
                  fields: [
                    {
                      name: 'startTime',
                      type: 'text',
                      required: true,
                    },
                    {
                      name: 'endTime',
                      type: 'text',
                      required: true,
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          label: 'Cross-selling & Recommendations',
          fields: [
            {
              name: 'relatedMenus',
              type: 'relationship',
              relationTo: 'store-menus',
              hasMany: true,
              filterOptions: ({ id }) => {
                return {
                  id: {
                    not_in: [id],
                  },
                }
              },
            },
            {
              name: 'suggestedItems',
              type: 'array',
              fields: [
                {
                  name: 'name',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'description',
                  type: 'textarea',
                  required: false,
                },
                {
                  name: 'price',
                  type: 'number',
                  required: true,
                  min: 0,
                },
              ],
            },
          ],
        },
        {
          label: 'Reviews & Analytics',
          fields: [
            {
              name: 'rating',
              type: 'group',
              admin: {
                position: 'sidebar',
              },
              fields: [
                {
                  name: 'average',
                  type: 'number',
                  defaultValue: 0,
                  min: 0,
                  max: 5,
                  admin: {
                    readOnly: true,
                    description: 'Calculated automatically',
                  },
                },
                {
                  name: 'count',
                  type: 'number',
                  defaultValue: 0,
                  admin: {
                    readOnly: true,
                    description: 'Total number of ratings',
                  },
                },
              ],
            },
            {
              name: 'favoritesCount',
              type: 'number',
              defaultValue: 0,
              admin: {
                readOnly: true,
                position: 'sidebar',
                description: 'Number of users who saved this menu',
              },
            },
            {
              name: 'analytics',
              type: 'group',
              admin: {
                position: 'sidebar',
              },
              fields: [
                {
                  name: 'views',
                  type: 'number',
                  defaultValue: 0,
                  admin: {
                    readOnly: true,
                  },
                },
                {
                  name: 'orders',
                  type: 'number',
                  defaultValue: 0,
                  admin: {
                    readOnly: true,
                  },
                },
                {
                  name: 'revenue',
                  type: 'number',
                  defaultValue: 0,
                  admin: {
                    readOnly: true,
                    description: 'Total revenue from this menu',
                  },
                },
                {
                  name: 'conversionRate',
                  type: 'number',
                  defaultValue: 0,
                  admin: {
                    readOnly: true,
                    description: 'Percentage of views that resulted in orders',
                  },
                },
              ],
            },
          ],
        },
      ],
    },
    ...slugField(),
  ],
}
