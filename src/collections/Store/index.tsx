import type { CollectionConfig } from 'payload'

import { authenticated } from '../../access/authenticated'
import { anyone } from '../../access/anyone'
import { slugField } from '@/fields/slug'
import { revalidateStore, revalidateDeleteStore } from './hooks/revalidateStore'
import { updateStoreAnalytics } from './hooks/updateAnalytics'

export const Store: CollectionConfig = {
  slug: 'stores',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'category', 'isActive', 'rating', 'updatedAt'],
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      index: true,
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
    },
    {
      name: 'category',
      type: 'select',
      required: true,
      options: [
        { label: 'Restaurant', value: 'restaurant' },
        { label: 'Cafe', value: 'cafe' },
        { label: 'Fast Food', value: 'fast-food' },
        { label: 'Bakery', value: 'bakery' },
        { label: 'Bar', value: 'bar' },
        { label: 'Food Truck', value: 'food-truck' },
        { label: 'Grocery', value: 'grocery' },
        { label: 'Other', value: 'other' },
      ],
      index: true,
    },
    {
      type: 'tabs',
      tabs: [
        {
          label: 'Basic Information',
          fields: [
            {
              name: 'images',
              type: 'array',
              required: true,
              minRows: 1,
              maxRows: 10,
              fields: [
                {
                  name: 'image',
                  type: 'upload',
                  relationTo: 'media',
                  required: true,
                },
                {
                  name: 'alt',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'isPrimary',
                  type: 'checkbox',
                  defaultValue: false,
                },
              ],
            },
            {
              name: 'priceRange',
              type: 'select',
              required: true,
              options: [
                { label: '$ - Budget Friendly', value: '$' },
                { label: '$$ - Moderate', value: '$$' },
                { label: '$$$ - Expensive', value: '$$$' },
                { label: '$$$$ - Very Expensive', value: '$$$$' },
              ],
            },
            {
              name: 'cuisine',
              type: 'array',
              fields: [
                {
                  name: 'type',
                  type: 'text',
                  required: true,
                },
              ],
            },
            {
              name: 'tags',
              type: 'array',
              fields: [
                {
                  name: 'tag',
                  type: 'text',
                  required: true,
                },
              ],
            },
          ],
        },
        {
          label: 'Contact & Location',
          fields: [
            {
              name: 'address',
              type: 'group',
              fields: [
                {
                  name: 'street',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'city',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'state',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'zipCode',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'country',
                  type: 'text',
                  required: true,
                  defaultValue: 'United States',
                },
                {
                  name: 'coordinates',
                  type: 'group',
                  fields: [
                    {
                      name: 'latitude',
                      type: 'number',
                      required: false,
                    },
                    {
                      name: 'longitude',
                      type: 'number',
                      required: false,
                    },
                  ],
                },
              ],
            },
            {
              name: 'contact',
              type: 'group',
              fields: [
                {
                  name: 'phone',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'email',
                  type: 'email',
                  required: false,
                },
                {
                  name: 'website',
                  type: 'text',
                  required: false,
                },
                {
                  name: 'socialMedia',
                  type: 'group',
                  fields: [
                    {
                      name: 'facebook',
                      type: 'text',
                    },
                    {
                      name: 'instagram',
                      type: 'text',
                    },
                    {
                      name: 'twitter',
                      type: 'text',
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          label: 'Operations',
          fields: [
            {
              name: 'operatingHours',
              type: 'array',
              required: true,
              fields: [
                {
                  name: 'day',
                  type: 'select',
                  required: true,
                  options: [
                    { label: 'Monday', value: 'monday' },
                    { label: 'Tuesday', value: 'tuesday' },
                    { label: 'Wednesday', value: 'wednesday' },
                    { label: 'Thursday', value: 'thursday' },
                    { label: 'Friday', value: 'friday' },
                    { label: 'Saturday', value: 'saturday' },
                    { label: 'Sunday', value: 'sunday' },
                  ],
                },
                {
                  name: 'isOpen',
                  type: 'checkbox',
                  defaultValue: true,
                },
                {
                  name: 'openTime',
                  type: 'text',
                  required: false,
                  admin: {
                    condition: (_, siblingData) => siblingData?.isOpen,
                  },
                },
                {
                  name: 'closeTime',
                  type: 'text',
                  required: false,
                  admin: {
                    condition: (_, siblingData) => siblingData?.isOpen,
                  },
                },
                {
                  name: 'breakStart',
                  type: 'text',
                  required: false,
                  admin: {
                    condition: (_, siblingData) => siblingData?.isOpen,
                  },
                },
                {
                  name: 'breakEnd',
                  type: 'text',
                  required: false,
                  admin: {
                    condition: (_, siblingData) => siblingData?.isOpen,
                  },
                },
              ],
            },
            {
              name: 'deliveryOptions',
              type: 'group',
              fields: [
                {
                  name: 'delivery',
                  type: 'checkbox',
                  defaultValue: false,
                },
                {
                  name: 'pickup',
                  type: 'checkbox',
                  defaultValue: true,
                },
                {
                  name: 'dineIn',
                  type: 'checkbox',
                  defaultValue: true,
                },
                {
                  name: 'deliveryRadius',
                  type: 'number',
                  required: false,
                  admin: {
                    condition: (_, siblingData) => siblingData?.delivery,
                    description: 'Delivery radius in miles',
                  },
                },
                {
                  name: 'deliveryFee',
                  type: 'number',
                  required: false,
                  admin: {
                    condition: (_, siblingData) => siblingData?.delivery,
                    description: 'Base delivery fee in dollars',
                  },
                },
                {
                  name: 'minimumOrder',
                  type: 'number',
                  required: false,
                  admin: {
                    condition: (_, siblingData) => siblingData?.delivery,
                    description: 'Minimum order amount for delivery',
                  },
                },
                {
                  name: 'estimatedDeliveryTime',
                  type: 'text',
                  required: false,
                  admin: {
                    condition: (_, siblingData) => siblingData?.delivery,
                    description: 'e.g., "30-45 minutes"',
                  },
                },
              ],
            },
          ],
        },
        {
          label: 'Status & Analytics',
          fields: [
            {
              name: 'isActive',
              type: 'checkbox',
              defaultValue: true,
              admin: {
                position: 'sidebar',
              },
            },
            {
              name: 'isFeatured',
              type: 'checkbox',
              defaultValue: false,
              admin: {
                position: 'sidebar',
              },
            },
            {
              name: 'rating',
              type: 'group',
              admin: {
                position: 'sidebar',
              },
              fields: [
                {
                  name: 'average',
                  type: 'number',
                  defaultValue: 0,
                  min: 0,
                  max: 5,
                  admin: {
                    readOnly: true,
                    description: 'Calculated automatically',
                  },
                },
                {
                  name: 'count',
                  type: 'number',
                  defaultValue: 0,
                  admin: {
                    readOnly: true,
                    description: 'Total number of ratings',
                  },
                },
              ],
            },
            {
              name: 'analytics',
              type: 'group',
              admin: {
                position: 'sidebar',
              },
              fields: [
                {
                  name: 'views',
                  type: 'number',
                  defaultValue: 0,
                  admin: {
                    readOnly: true,
                  },
                },
                {
                  name: 'favorites',
                  type: 'number',
                  defaultValue: 0,
                  admin: {
                    readOnly: true,
                  },
                },
                {
                  name: 'orders',
                  type: 'number',
                  defaultValue: 0,
                  admin: {
                    readOnly: true,
                  },
                },
              ],
            },
          ],
        },
      ],
    },
    {
      name: 'owner',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'categories',
      type: 'relationship',
      relationTo: 'store-categories',
      hasMany: true,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'menus',
      type: 'relationship',
      relationTo: 'store-menus',
      hasMany: true,
      admin: {
        position: 'sidebar',
      },
    },
    ...slugField(),
  ],
  hooks: {
    beforeChange: [updateStoreAnalytics],
    afterChange: [revalidateStore],
    afterDelete: [revalidateDeleteStore],
  },
}
