import type { CollectionBefore<PERSON><PERSON><PERSON>Hook } from 'payload'

// Hook to update store analytics when certain actions occur
export const updateStoreAnalytics: CollectionBeforeChangeHook = async ({ data, operation, req }) => {
  // Only update analytics on update operations
  if (operation !== 'update') {
    return data
  }

  // This hook can be extended to update analytics based on various triggers
  // For now, it's a placeholder for future analytics logic
  
  return data
}

// Utility function to increment store views
export const incrementStoreViews = async (storeId: string, payload: any) => {
  try {
    const store = await payload.findByID({
      collection: 'stores',
      id: storeId,
    })

    if (store) {
      await payload.update({
        collection: 'stores',
        id: storeId,
        data: {
          analytics: {
            ...store.analytics,
            views: (store.analytics?.views || 0) + 1,
          },
        },
        context: {
          disableRevalidate: true, // Prevent revalidation for analytics updates
        },
      })
    }
  } catch (error) {
    payload.logger.error(`Failed to increment store views for ${storeId}:`, error)
  }
}

// Utility function to increment store favorites
export const incrementStoreFavorites = async (storeId: string, payload: any) => {
  try {
    const store = await payload.findByID({
      collection: 'stores',
      id: storeId,
    })

    if (store) {
      await payload.update({
        collection: 'stores',
        id: storeId,
        data: {
          analytics: {
            ...store.analytics,
            favorites: (store.analytics?.favorites || 0) + 1,
          },
        },
        context: {
          disableRevalidate: true,
        },
      })
    }
  } catch (error) {
    payload.logger.error(`Failed to increment store favorites for ${storeId}:`, error)
  }
}

// Utility function to decrement store favorites
export const decrementStoreFavorites = async (storeId: string, payload: any) => {
  try {
    const store = await payload.findByID({
      collection: 'stores',
      id: storeId,
    })

    if (store) {
      await payload.update({
        collection: 'stores',
        id: storeId,
        data: {
          analytics: {
            ...store.analytics,
            favorites: Math.max((store.analytics?.favorites || 0) - 1, 0),
          },
        },
        context: {
          disableRevalidate: true,
        },
      })
    }
  } catch (error) {
    payload.logger.error(`Failed to decrement store favorites for ${storeId}:`, error)
  }
}
