import type { CollectionAfter<PERSON>hangeHook, CollectionAfterDeleteHook } from 'payload'

import { revalidatePath, revalidateTag } from 'next/cache'

import type { Store } from '../../../payload-types'

export const revalidateStore: CollectionAfterChangeHook<Store> = ({
  doc,
  previousDoc,
  req: { payload, context },
}) => {
  if (!context.disableRevalidate) {
    if (doc.isActive) {
      const path = `/stores/${doc.slug}`

      payload.logger.info(`Revalidating store at path: ${path}`)

      revalidatePath(path)
      revalidateTag('stores-sitemap')
    }

    // If the store was previously active, we need to revalidate the old path
    if (previousDoc?.isActive && !doc.isActive) {
      const oldPath = `/stores/${previousDoc.slug}`

      payload.logger.info(`Revalidating old store at path: ${oldPath}`)

      revalidatePath(oldPath)
      revalidateTag('stores-sitemap')
    }
  }
  return doc
}

export const revalidateDeleteStore: CollectionAfterDeleteHook<Store> = ({ doc, req: { context } }) => {
  if (!context.disableRevalidate) {
    const path = `/stores/${doc?.slug}`

    revalidatePath(path)
    revalidateTag('stores-sitemap')
  }

  return doc
}
