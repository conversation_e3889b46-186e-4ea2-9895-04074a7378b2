import { normalizeVietnamesePhoneNumber } from '@/utilities/normalizeVietnamesePhoneNumber'
import { APIError } from 'payload'

function trimString(value: any) {
  return typeof value === 'string' ? value.trim() : value
}

export const validateUser = async (args: any) => {
  const { data = {}, operation, req } = args
  if (!data) return data

  // Trim and normalize fields
  if (data.email) {
    data.email = trimString(data.email).toLowerCase()
  }
  if (data.phoneNumber) {
    data.phoneNumber = normalizeVietnamesePhoneNumber(trimString(data.phoneNumber))
  }
  if (data.firstName) {
    data.firstName = trimString(data.firstName)
  }
  if (data.lastName) {
    data.lastName = trimString(data.lastName)
  }

  data.fullName = `${data.firstName || ''} ${data.lastName || ''}`.trim()

  // Ensure phoneNumbers array exists
  if (!Array.isArray(data.phoneNumbers)) {
    data.phoneNumbers = []
  }

  // Normalize all phone numbers in the array
  data.phoneNumbers = (data.phoneNumbers as { phone: string }[])
    .map((item) => ({
      ...item,
      phone: normalizeVietnamesePhoneNumber(trimString(item.phone)),
    }))
    .filter((item, index, arr) => arr.findIndex((obj) => obj.phone === item.phone) === index)

  // Email format validation
  if (data.email && !/^\S+@\S+\.\S+$/.test(data.email)) {
    throw new APIError('Invalid email format.', 400, {}, true)
  }

  // Duplication checks only on create
  if (operation === 'create') {
    // Check duplicate email
    if (data.email) {
      const existing = await req.payload.find({
        collection: 'users',
        where: { email: { equals: data.email } },
        limit: 1,
      })
      if (existing.docs.length > 0) {
        throw new APIError('Email already exists.', 400, {}, true)
      }
    }

    // Check duplicate phone numbers
  }

  return data
}
