import type { CollectionConfig } from 'payload'
import { USER_ROLES, USER_ROLE } from './constants'
import { validateUser } from './hooks/validateUser'

export const Users: CollectionConfig = {
  slug: 'users',
  admin: {
    useAsTitle: 'email',
  },

  fields: [
    {
      name: 'email',
      type: 'email',
      required: true,
      index: true,
    },
    {
      name: 'salt',
      type: 'text',
      hidden: true,
    },
    {
      name: 'hash',
      type: 'text',
      hidden: true,
    },
    {
      name: 'resetPasswordToken',
      type: 'text',
      hidden: true,
    },
    {
      name: 'resetPasswordExpiration',
      type: 'date',
      hidden: true,
    },
    // Email added by default
    {
      name: 'phoneNumbers', //support multi phone numbers
      type: 'array',
      required: false,
      fields: [
        {
          name: 'phone',
          type: 'text',
        },
        {
          name: 'addedAt',
          type: 'date',
          admin: {
            date: {
              pickerAppearance: 'dayAndTime',
              timeFormat: 'HH:mm a',
            },
            readOnly: true,
          },
          defaultValue: () => new Date().toISOString(),
        },
        {
          name: 'isUsing',
          type: 'checkbox',
          admin: {
            readOnly: true,
          },
          defaultValue: false,
        },
      ],
    },
    {
      name: 'username',
      type: 'text',
      required: false,
    },
    {
      name: 'firstName',
      type: 'text',
      required: false,
    },
    {
      name: 'lastName',
      type: 'text',
      required: false,
    },
    {
      name: 'fullName',
      type: 'text',
      required: false,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'lastActive',
      type: 'date',
      required: false,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'role',
      type: 'select',
      options: USER_ROLES,
      defaultValue: USER_ROLE.user.value,
      admin: {
        readOnly: true,
      },
    },
  ],
  hooks: {
    beforeValidate: [validateUser],
  },
}
