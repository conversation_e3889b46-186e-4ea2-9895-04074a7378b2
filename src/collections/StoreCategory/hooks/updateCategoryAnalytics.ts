import type { CollectionBefore<PERSON><PERSON><PERSON>Hook } from 'payload'

// Hook to update category analytics when certain actions occur
export const updateCategoryAnalytics: CollectionBeforeChangeHook = async ({ data, operation, req }) => {
  // Only update analytics on update operations
  if (operation !== 'update') {
    return data
  }

  // This hook can be extended to update analytics based on various triggers
  // For now, it's a placeholder for future analytics logic
  
  return data
}

// Utility function to increment category views
export const incrementCategoryViews = async (categoryId: string, payload: any) => {
  try {
    const category = await payload.findByID({
      collection: 'store-categories',
      id: categoryId,
    })

    if (category) {
      await payload.update({
        collection: 'store-categories',
        id: categoryId,
        data: {
          analytics: {
            ...category.analytics,
            views: (category.analytics?.views || 0) + 1,
          },
        },
        context: {
          disableRevalidate: true, // Prevent revalidation for analytics updates
        },
      })
    }
  } catch (error) {
    payload.logger.error(`Failed to increment category views for ${categoryId}:`, error)
  }
}

// Utility function to increment category clicks
export const incrementCategoryClicks = async (categoryId: string, payload: any) => {
  try {
    const category = await payload.findByID({
      collection: 'store-categories',
      id: categoryId,
    })

    if (category) {
      await payload.update({
        collection: 'store-categories',
        id: categoryId,
        data: {
          analytics: {
            ...category.analytics,
            clicks: (category.analytics?.clicks || 0) + 1,
          },
        },
        context: {
          disableRevalidate: true,
        },
      })
    }
  } catch (error) {
    payload.logger.error(`Failed to increment category clicks for ${categoryId}:`, error)
  }
}

// Utility function to update category item count
export const updateCategoryItemCount = async (categoryId: string, payload: any) => {
  try {
    // Count the number of menu items in this category
    const { totalDocs } = await payload.find({
      collection: 'store-menus',
      where: {
        category: {
          equals: categoryId,
        },
      },
      limit: 0, // Just get the count
    })

    await payload.update({
      collection: 'store-categories',
      id: categoryId,
      data: {
        itemCount: totalDocs,
      },
      context: {
        disableRevalidate: true,
      },
    })
  } catch (error) {
    payload.logger.error(`Failed to update category item count for ${categoryId}:`, error)
  }
}
