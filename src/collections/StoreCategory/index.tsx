import type { CollectionConfig } from 'payload'

import { authenticated } from '../../access/authenticated'
import { anyone } from '../../access/anyone'
import { slugField } from '@/fields/slug'

export const StoreCategory: CollectionConfig = {
  slug: 'store-categories',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'store', 'isActive', 'itemCount', 'updatedAt'],
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      index: true,
    },
    {
      name: 'description',
      type: 'textarea',
      required: false,
    },
    {
      name: 'store',
      type: 'relationship',
      relationTo: 'stores',
      required: true,
      index: true,
    },
    {
      type: 'tabs',
      tabs: [
        {
          label: 'Basic Information',
          fields: [
            {
              name: 'image',
              type: 'upload',
              relationTo: 'media',
              required: false,
            },
            {
              name: 'icon',
              type: 'text',
              required: false,
              admin: {
                description: 'Icon name or emoji for the category',
              },
            },
            {
              name: 'color',
              type: 'text',
              required: false,
              admin: {
                description: 'Hex color code for category theming',
              },
            },
            {
              name: 'sortOrder',
              type: 'number',
              defaultValue: 0,
              admin: {
                description: 'Lower numbers appear first',
              },
            },
          ],
        },
        {
          label: 'Search & Discovery',
          fields: [
            {
              name: 'tags',
              type: 'array',
              fields: [
                {
                  name: 'tag',
                  type: 'text',
                  required: true,
                },
              ],
            },
            {
              name: 'keywords',
              type: 'array',
              fields: [
                {
                  name: 'keyword',
                  type: 'text',
                  required: true,
                },
              ],
            },
            {
              name: 'filters',
              type: 'group',
              fields: [
                {
                  name: 'dietary',
                  type: 'array',
                  fields: [
                    {
                      name: 'type',
                      type: 'select',
                      options: [
                        { label: 'Vegetarian', value: 'vegetarian' },
                        { label: 'Vegan', value: 'vegan' },
                        { label: 'Gluten-Free', value: 'gluten-free' },
                        { label: 'Dairy-Free', value: 'dairy-free' },
                        { label: 'Nut-Free', value: 'nut-free' },
                        { label: 'Keto', value: 'keto' },
                        { label: 'Low-Carb', value: 'low-carb' },
                        { label: 'Halal', value: 'halal' },
                        { label: 'Kosher', value: 'kosher' },
                      ],
                    },
                  ],
                },
                {
                  name: 'spiceLevel',
                  type: 'select',
                  options: [
                    { label: 'Mild', value: 'mild' },
                    { label: 'Medium', value: 'medium' },
                    { label: 'Hot', value: 'hot' },
                    { label: 'Extra Hot', value: 'extra-hot' },
                  ],
                },
                {
                  name: 'allergens',
                  type: 'array',
                  fields: [
                    {
                      name: 'allergen',
                      type: 'select',
                      options: [
                        { label: 'Nuts', value: 'nuts' },
                        { label: 'Dairy', value: 'dairy' },
                        { label: 'Eggs', value: 'eggs' },
                        { label: 'Soy', value: 'soy' },
                        { label: 'Wheat', value: 'wheat' },
                        { label: 'Fish', value: 'fish' },
                        { label: 'Shellfish', value: 'shellfish' },
                      ],
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          label: 'Promotions',
          fields: [
            {
              name: 'promotions',
              type: 'array',
              fields: [
                {
                  name: 'title',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'description',
                  type: 'textarea',
                  required: false,
                },
                {
                  name: 'type',
                  type: 'select',
                  required: true,
                  options: [
                    { label: 'Discount Percentage', value: 'percentage' },
                    { label: 'Fixed Amount Off', value: 'fixed' },
                    { label: 'Buy One Get One', value: 'bogo' },
                    { label: 'Featured Items', value: 'featured' },
                  ],
                },
                {
                  name: 'value',
                  type: 'number',
                  required: false,
                  admin: {
                    description: 'Discount percentage or fixed amount',
                  },
                },
                {
                  name: 'startDate',
                  type: 'date',
                  required: true,
                  admin: {
                    date: {
                      pickerAppearance: 'dayAndTime',
                    },
                  },
                },
                {
                  name: 'endDate',
                  type: 'date',
                  required: true,
                  admin: {
                    date: {
                      pickerAppearance: 'dayAndTime',
                    },
                  },
                },
                {
                  name: 'isActive',
                  type: 'checkbox',
                  defaultValue: true,
                },
              ],
            },
          ],
        },
        {
          label: 'Status & Analytics',
          fields: [
            {
              name: 'isActive',
              type: 'checkbox',
              defaultValue: true,
              admin: {
                position: 'sidebar',
              },
            },
            {
              name: 'isFeatured',
              type: 'checkbox',
              defaultValue: false,
              admin: {
                position: 'sidebar',
              },
            },
            {
              name: 'itemCount',
              type: 'number',
              defaultValue: 0,
              admin: {
                readOnly: true,
                position: 'sidebar',
                description: 'Number of items in this category',
              },
            },
            {
              name: 'analytics',
              type: 'group',
              admin: {
                position: 'sidebar',
              },
              fields: [
                {
                  name: 'views',
                  type: 'number',
                  defaultValue: 0,
                  admin: {
                    readOnly: true,
                  },
                },
                {
                  name: 'clicks',
                  type: 'number',
                  defaultValue: 0,
                  admin: {
                    readOnly: true,
                  },
                },
                {
                  name: 'orders',
                  type: 'number',
                  defaultValue: 0,
                  admin: {
                    readOnly: true,
                  },
                },
                {
                  name: 'revenue',
                  type: 'number',
                  defaultValue: 0,
                  admin: {
                    readOnly: true,
                    description: 'Total revenue from this category',
                  },
                },
              ],
            },
          ],
        },
      ],
    },
    ...slugField(),
  ],
}
