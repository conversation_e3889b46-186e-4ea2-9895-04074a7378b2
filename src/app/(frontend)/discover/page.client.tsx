'use client'

import { useState } from 'react'
import { Search, Filter, MapPin, Star, Clock, Heart } from 'lucide-react'
import { SearchInput } from '@/components/ui/search-input'
import { Button } from '@/components/ui/button'
import { BusinessCard } from '@/components/ui/business-card'
import { CategoriesSection } from '@/components/discovery/categories-section'
import burgerImage from '@/assets/food-burger.jpg'
import sushiImage from '@/assets/food-sushi.jpg'
import coffeeImage from '@/assets/food-coffee.jpg'

const mockBusinesses = [
  {
    id: '1',
    name: 'Bella Vista Restaurant',
    category: 'Italian • Fine Dining',
    image: burgerImage.src,
    rating: 4.8,
    reviewCount: 324,
    distance: '0.3 km',
    priceRange: '$$$$',
    isOpen: true,
    deliveryTime: '25-35 min',
    tags: ['Popular', 'Highly Rated'],
  },
  {
    id: '2',
    name: 'Sakura Sushi Bar',
    category: 'Japanese • Sushi',
    image: sushiImage.src,
    rating: 4.6,
    reviewCount: 189,
    distance: '0.8 km',
    priceRange: '$$$',
    isOpen: true,
    deliveryTime: '30-40 min',
    tags: ['Fresh', 'Authentic'],
  },
  {
    id: '3',
    name: 'Morning Brew Coffee',
    category: 'Cafe • Coffee Shop',
    image: coffeeImage.src,
    rating: 4.4,
    reviewCount: 156,
    distance: '0.2 km',
    priceRange: '$$',
    isOpen: false,
    deliveryTime: '15-25 min',
    tags: ['Local Favorite'],
  },
]

const filterOptions = [
  { label: 'Open Now', value: 'open' },
  { label: 'Delivery', value: 'delivery' },
  { label: 'Highly Rated', value: 'rated' },
  { label: 'Fast Delivery', value: 'fast' },
]

export default function Discover() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedFilters, setSelectedFilters] = useState<string[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('')

  const handleFilterToggle = (filter: string) => {
    setSelectedFilters((prev) =>
      prev.includes(filter) ? prev.filter((f) => f !== filter) : [...prev, filter],
    )
  }

  const handleBusinessClick = (businessId: string) => {
    console.log('Business clicked:', businessId)
  }

  const handleFavoriteToggle = (businessId: string) => {
    console.log('Favorite toggled:', businessId)
  }

  return (
    <div className="min-h-screen pb-20 md:pb-8">
      {/* Header */}
      <div className="sticky top-0 z-40 glass-nav px-4 py-4 border-b border-white/20">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-display font-bold text-foreground">Discover</h1>
              <p className="text-sm text-muted-foreground">Find amazing places near you</p>
            </div>
            <Button variant="outline" size="icon" className="rounded-full">
              <MapPin className="h-4 w-4 text-primary" />
            </Button>
          </div>

          {/* Search */}
          <SearchInput
            placeholder="Search restaurants, cuisines, dishes..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="glass"
          />

          {/* Quick Filters */}
          <div className="flex gap-2 overflow-x-auto pb-2">
            {filterOptions.map((filter) => (
              <Button
                key={filter.value}
                variant={selectedFilters.includes(filter.value) ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleFilterToggle(filter.value)}
                className="whitespace-nowrap rounded-full"
              >
                {filter.label}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="space-y-6">
        {/* Categories */}
        <div className="px-4 pt-6">
          <CategoriesSection
            selectedCategory={selectedCategory}
            onCategorySelect={setSelectedCategory}
          />
        </div>

        {/* Results */}
        <div className="px-4 space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-foreground">
              {searchQuery ? `Results for "${searchQuery}"` : 'Popular Near You'}
            </h2>
            <Button variant="ghost" size="sm" className="text-primary">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
          </div>

          {/* Business Grid */}
          <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {mockBusinesses.map((business) => (
              <div key={business.id} className="glass-card hover-lift">
                <BusinessCard
                  {...business}
                  onClick={() => handleBusinessClick(business.id)}
                  onFavoriteToggle={() => handleFavoriteToggle(business.id)}
                  isFavorite={false}
                />
              </div>
            ))}
          </div>

          {/* Load More */}
          <div className="text-center pt-6">
            <Button size="lg" className="rounded-full">
              Load More Results
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
