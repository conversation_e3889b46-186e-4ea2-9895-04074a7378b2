'use client'
import { useState } from 'react'
import { Heart, Star, MapPin, Filter, Grid3X3, List } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { BusinessCard } from '@/components/ui/business-card'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import burgerImage from '@/assets/food-burger.jpg'
import sushiImage from '@/assets/food-sushi.jpg'
import coffeeImage from '@/assets/food-coffee.jpg'

const favoriteRestaurants = [
  {
    id: '1',
    name: 'Bella Vista Restaurant',
    category: 'Italian • Fine Dining',
    image: burgerImage.src,
    rating: 4.8,
    reviewCount: 324,
    distance: '0.3 km',
    priceRange: '$$$$',
    isOpen: true,
    lastVisited: '2 days ago',
    tags: ['Anniversary Spot'],
  },
  {
    id: '2',
    name: 'Morning Brew Coffee',
    category: 'Cafe • Coffee Shop',
    image: coffeeImage.src,
    rating: 4.4,
    reviewCount: 156,
    distance: '0.2 km',
    priceRange: '$$',
    isOpen: true,
    lastVisited: '1 week ago',
    tags: ['Work Favorite'],
  },
]

const favoriteCafes = [
  {
    id: '3',
    name: 'Artisan Coffee Roasters',
    category: 'Coffee Shop • Specialty',
    image: coffeeImage.src,
    rating: 4.7,
    reviewCount: 89,
    distance: '0.5 km',
    priceRange: '$$$',
    isOpen: true,
    lastVisited: '3 days ago',
    tags: ['Third Wave Coffee'],
  },
]

const favoriteCollections = [
  {
    id: 'weekend-brunch',
    name: 'Weekend Brunch Spots',
    count: 5,
    image: burgerImage.src,
    description: 'Perfect places for lazy Sunday brunches',
  },
  {
    id: 'date-night',
    name: 'Romantic Date Night',
    count: 3,
    image: sushiImage.src,
    description: 'Intimate restaurants for special occasions',
  },
  {
    id: 'quick-lunch',
    name: 'Quick Lunch Options',
    count: 8,
    image: coffeeImage.src,
    description: 'Fast and delicious weekday lunch spots',
  },
]

export default function FavoritesPageClient() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [activeTab, setActiveTab] = useState('all')

  const handleBusinessClick = (businessId: string) => {
    console.log('Business clicked:', businessId)
  }

  const handleFavoriteToggle = (businessId: string) => {
    console.log('Favorite toggled:', businessId)
  }

  const handleCollectionClick = (collectionId: string) => {
    console.log('Collection clicked:', collectionId)
  }

  const allFavorites = [...favoriteRestaurants, ...favoriteCafes]

  return (
    <div className="min-h-screen pb-20 md:pb-8">
      {/* Header */}
      <div className="glass-nav px-4 py-4 border-b border-white/20">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-display font-bold text-foreground">Favorites</h1>
              <p className="text-sm text-muted-foreground">Your saved places and collections</p>
            </div>

            <div className="flex gap-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="rounded-full"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="rounded-full"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <Badge variant="secondary" className="bg-primary/10 text-primary">
              <Heart className="h-3 w-3 mr-1 fill-current" />
              {allFavorites.length} Favorites
            </Badge>
            <Button variant="outline" size="sm" className="rounded-full">
              <Filter className="h-4 w-4 mr-2" />
              Sort
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 glass">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="restaurants">Restaurants</TabsTrigger>
            <TabsTrigger value="cafes">Cafes</TabsTrigger>
            <TabsTrigger value="collections">Collections</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-6">
            {allFavorites.length === 0 ? (
              <div className="glass-card rounded-2xl p-12 text-center">
                <Heart className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-foreground mb-2">No favorites yet</h3>
                <p className="text-muted-foreground mb-4">
                  Start exploring and save your favorite places
                </p>
                <Button>Discover Places</Button>
              </div>
            ) : (
              <div
                className={
                  viewMode === 'grid' ? 'grid gap-4 sm:grid-cols-2 lg:grid-cols-3' : 'space-y-4'
                }
              >
                {allFavorites.map((business) => (
                  <div key={business.id} className="glass-card hover-lift">
                    <BusinessCard
                      {...business}
                      onClick={() => handleBusinessClick(business.id)}
                      onFavoriteToggle={() => handleFavoriteToggle(business.id)}
                      isFavorite={true}
                    />
                    <div className="px-4 pb-4">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">
                          Last visited: {business.lastVisited}
                        </span>
                        {business.tags?.map((tag) => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="restaurants" className="space-y-6">
            <div
              className={
                viewMode === 'grid' ? 'grid gap-4 sm:grid-cols-2 lg:grid-cols-3' : 'space-y-4'
              }
            >
              {favoriteRestaurants.map((business) => (
                <div key={business.id} className="glass-card hover-lift">
                  <BusinessCard
                    {...business}
                    onClick={() => handleBusinessClick(business.id)}
                    onFavoriteToggle={() => handleFavoriteToggle(business.id)}
                    isFavorite={true}
                  />
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="cafes" className="space-y-6">
            <div
              className={
                viewMode === 'grid' ? 'grid gap-4 sm:grid-cols-2 lg:grid-cols-3' : 'space-y-4'
              }
            >
              {favoriteCafes.map((business) => (
                <div key={business.id} className="glass-card hover-lift">
                  <BusinessCard
                    {...business}
                    onClick={() => handleBusinessClick(business.id)}
                    onFavoriteToggle={() => handleFavoriteToggle(business.id)}
                    isFavorite={true}
                  />
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="collections" className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-foreground">My Collections</h3>
                <Button size="sm" className="rounded-full">
                  Create Collection
                </Button>
              </div>

              <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                {favoriteCollections.map((collection) => (
                  <div
                    key={collection.id}
                    className="glass-card hover-lift cursor-pointer"
                    onClick={() => handleCollectionClick(collection.id)}
                  >
                    <div className="relative">
                      <img
                        src={collection.image}
                        alt={collection.name}
                        className="w-full h-32 object-cover rounded-t-2xl"
                      />
                      <div className="absolute top-2 right-2">
                        <Badge variant="secondary" className="bg-black/50 text-white">
                          {collection.count} places
                        </Badge>
                      </div>
                    </div>
                    <div className="p-4">
                      <h4 className="font-semibold text-foreground mb-1">{collection.name}</h4>
                      <p className="text-sm text-muted-foreground">{collection.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
