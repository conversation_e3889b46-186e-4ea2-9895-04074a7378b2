'use client'

import { useState } from 'react'
import { MapPin, Navigation, Filter, List, Map } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { BusinessCard } from '@/components/ui/business-card'
import { Badge } from '@/components/ui/badge'
import burgerImage from '@/assets/food-burger.jpg'
import sushiImage from '@/assets/food-sushi.jpg'
import coffeeImage from '@/assets/food-coffee.jpg'

const nearbyBusinesses = [
  {
    id: '1',
    name: 'Corner Deli',
    category: 'Sandwich Shop • Deli',
    image: burgerImage.src,
    rating: 4.3,
    reviewCount: 89,
    distance: '0.1 km',
    priceRange: '$$',
    isOpen: true,
    walkTime: '2 min walk',
  },
  {
    id: '2',
    name: 'Morning Brew Coffee',
    category: 'Cafe • Coffee Shop',
    image: coffeeImage.src,
    rating: 4.4,
    reviewCount: 156,
    distance: '0.2 km',
    priceRange: '$$',
    isOpen: true,
    walkTime: '3 min walk',
  },
  {
    id: '3',
    name: 'Bella Vista Restaurant',
    category: 'Italian • Fine Dining',
    image: sushiImage.src,
    rating: 4.8,
    reviewCount: 324,
    distance: '0.3 km',
    priceRange: '$$$$',
    isOpen: true,
    walkTime: '5 min walk',
  },
  {
    id: '4',
    name: 'Tokyo Ramen House',
    category: 'Japanese • Ramen',
    image: sushiImage.src,
    rating: 4.6,
    reviewCount: 203,
    distance: '0.4 km',
    priceRange: '$$$',
    isOpen: false,
    walkTime: '6 min walk',
  },
]

const distanceFilters = [
  { label: 'Within 0.5km', value: '0.5', active: true },
  { label: 'Within 1km', value: '1', active: false },
  { label: 'Within 2km', value: '2', active: false },
]

export default function NearbyPageClient() {
  const [viewMode, setViewMode] = useState<'list' | 'map'>('list')
  const [selectedDistance, setSelectedDistance] = useState('0.5')

  const handleBusinessClick = (businessId: string) => {
    console.log('Business clicked:', businessId)
  }

  const handleFavoriteToggle = (businessId: string) => {
    console.log('Favorite toggled:', businessId)
  }

  const handleGetDirections = () => {
    console.log('Getting directions...')
  }

  return (
    <div className="min-h-screen pb-20 md:pb-8">
      {/* Header */}
      <div className="glass-nav px-4 py-4 border-b border-white/20">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-display font-bold text-foreground">Nearby</h1>
              <div className="flex items-center gap-2 mt-1">
                <MapPin className="h-4 w-4 text-primary" />
                <p className="text-sm text-muted-foreground">Downtown San Francisco</p>
              </div>
            </div>

            <div className="flex gap-2">
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="rounded-full"
              >
                <List className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'map' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('map')}
                className="rounded-full"
              >
                <Map className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Location Actions */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              className="rounded-full flex-1"
              onClick={handleGetDirections}
            >
              <Navigation className="h-4 w-4 mr-2" />
              Update Location
            </Button>
            <Button variant="outline" size="sm" className="rounded-full">
              <Filter className="h-4 w-4" />
            </Button>
          </div>

          {/* Distance Filters */}
          <div className="flex gap-2 overflow-x-auto pb-2">
            {distanceFilters.map((filter) => (
              <Button
                key={filter.value}
                variant={selectedDistance === filter.value ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedDistance(filter.value)}
                className="whitespace-nowrap rounded-full"
              >
                {filter.label}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-6">
        {viewMode === 'map' ? (
          /* Map View Placeholder */
          <div className="glass-card rounded-2xl h-96 flex items-center justify-center">
            <div className="text-center space-y-2">
              <Map className="h-12 w-12 text-primary mx-auto" />
              <h3 className="text-lg font-semibold text-foreground">Map View</h3>
              <p className="text-sm text-muted-foreground">Interactive map coming soon</p>
            </div>
          </div>
        ) : (
          /* List View */
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold text-foreground">
                  {nearbyBusinesses.length} places nearby
                </h2>
                <p className="text-sm text-muted-foreground">
                  Within {selectedDistance}km of your location
                </p>
              </div>
              <Badge variant="secondary" className="bg-primary/10 text-primary">
                <MapPin className="h-3 w-3 mr-1" />
                Live Location
              </Badge>
            </div>

            {/* Businesses List */}
            <div className="space-y-4">
              {nearbyBusinesses
                .filter((business) => parseFloat(business.distance) <= parseFloat(selectedDistance))
                .map((business) => (
                  <div key={business.id} className="glass-card hover-lift">
                    <div className="flex items-start gap-4 p-4">
                      <img
                        src={business.image}
                        alt={business.name}
                        className="w-20 h-20 rounded-xl object-cover"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div>
                            <h3 className="font-semibold text-foreground line-clamp-1">
                              {business.name}
                            </h3>
                            <p className="text-sm text-muted-foreground">{business.category}</p>
                            <div className="flex items-center gap-4 mt-2">
                              <div className="flex items-center gap-1">
                                <span className="text-sm font-medium text-foreground">
                                  {business.rating}
                                </span>
                                <div className="flex">
                                  {[...Array(5)].map((_, i) => (
                                    <div
                                      key={i}
                                      className={`h-3 w-3 ${
                                        i < Math.floor(business.rating)
                                          ? 'text-amber-400 fill-current'
                                          : 'text-gray-300'
                                      }`}
                                    >
                                      ⭐
                                    </div>
                                  ))}
                                </div>
                                <span className="text-xs text-muted-foreground">
                                  ({business.reviewCount})
                                </span>
                              </div>
                            </div>
                          </div>
                          <div className="text-right flex-shrink-0">
                            <div className="text-sm font-medium text-foreground">
                              {business.distance}
                            </div>
                            <div className="text-xs text-muted-foreground">{business.walkTime}</div>
                            <Badge
                              variant={business.isOpen ? 'default' : 'secondary'}
                              className="mt-1 text-xs"
                            >
                              {business.isOpen ? 'Open' : 'Closed'}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="px-4 pb-4 flex gap-2">
                      <Button
                        size="sm"
                        className="flex-1 rounded-full"
                        onClick={() => handleBusinessClick(business.id)}
                      >
                        View Details
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="rounded-full"
                        onClick={handleGetDirections}
                      >
                        <Navigation className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
