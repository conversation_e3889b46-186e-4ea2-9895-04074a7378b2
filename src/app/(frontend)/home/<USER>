'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { HeroSection } from '@/components/discovery/hero-section'
import { CategoriesSection } from '@/components/discovery/categories-section'
import { FeaturedSection } from '@/components/discovery/featured-section'

const HomePageClient = () => {
  const navigate = useRouter()
  const [selectedCategory, setSelectedCategory] = useState<string>()

  const handleSearch = (query: string) => {
    if (query.trim()) {
      navigate.push(`/discover?q=${encodeURIComponent(query)}`)
    }
  }

  const handleFilterClick = () => {
    navigate.push('/discover')
  }

  const handleBusinessClick = (id: string) => {
    console.log('Opening business:', id)
    // Navigate to business detail page when implemented
  }

  const handleFavoriteToggle = (id: string) => {
    console.log('Toggling favorite:', id)
  }

  const handleCategorySelect = (categoryId: string) => {
    const newCategory = categoryId === selectedCategory ? undefined : categoryId
    setSelectedCategory(newCategory)
    if (newCategory) {
      navigate.push(`/discover?category=${encodeURIComponent(newCategory)}`)
    }
  }

  return (
    <div className="min-h-screen bg-background">
      <main className="pb-20 md:pb-0">
        {/* Hero Section */}
        <HeroSection onSearch={handleSearch} onFilterClick={handleFilterClick} />

        {/* Content Sections */}
        <div className="px-4 space-y-8 py-6 relative">
          {/* Background blur overlay for content sections */}
          <div className="absolute inset-0 backdrop-blur-sm bg-white/5 rounded-3xl -mx-4 -my-6" />

          <div className="relative z-10 space-y-8">
            {/* Categories */}
            <CategoriesSection
              onCategorySelect={handleCategorySelect}
              selectedCategory={selectedCategory}
            />

            {/* Featured Businesses */}
            <FeaturedSection
              onBusinessClick={handleBusinessClick}
              onFavoriteToggle={handleFavoriteToggle}
            />
          </div>
        </div>
      </main>
    </div>
  )
}

export default HomePageClient
