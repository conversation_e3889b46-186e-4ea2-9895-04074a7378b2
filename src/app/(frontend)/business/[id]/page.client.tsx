'use client'

import { BusinessDetail, BusinessDetailType } from '@/components/ui/business-detail'

interface BusinessPageDetailClientProps {
  business: BusinessDetailType
}

export default function BusinessPageDetailClient({ business }: BusinessPageDetailClientProps) {
  const handleFavoriteToggle = (id: string) => {
    // TODO: Implement favorite toggle functionality
    console.log('Toggle favorite for business:', id)
  }

  const handleBack = () => {
    // TODO: Implement back navigation
    window.history.back()
  }

  const handleCall = (phone: string) => {
    // TODO: Implement call functionality
    window.open(`tel:${phone}`)
  }

  const handleNavigate = (address: string) => {
    // TODO: Implement navigation functionality
    const encodedAddress = encodeURIComponent(address)
    window.open(`https://maps.google.com/maps?q=${encodedAddress}`)
  }

  const handleShare = (business: { name: string; category: string }) => {
    // TODO: Implement share functionality
    if (navigator.share) {
      navigator.share({
        title: business.name,
        text: `Check out ${business.name} - ${business.category}`,
        url: window.location.href,
      })
    } else {
      // Fallback to copying URL
      navigator.clipboard.writeText(window.location.href)
    }
  }

  return (
    <BusinessDetail
      business={business}
      onFavoriteToggle={handleFavoriteToggle}
      onBack={handleBack}
      onCall={handleCall}
      onNavigate={handleNavigate}
      onShare={handleShare}
    />
  )
}
