import { notFound } from 'next/navigation'
import burgerImage from '@/assets/food-burger.jpg'
import sushiImage from '@/assets/food-sushi.jpg'
import coffeeImage from '@/assets/food-coffee.jpg'
import BusinessPageDetailClient from './page.client'

// Mock data for business details
const businessData = {
  '1': {
    id: '1',
    name: 'Artisan Burger Co.',
    category: 'American Cuisine',
    image: burgerImage.src,
    rating: 4.8,
    reviewCount: 234,
    distance: '0.5 km',
    isOpen: true,
    priceRange: '$$',
    isFavorite: false,
    description:
      'Crafting the finest gourmet burgers with locally sourced ingredients. Our signature blend of premium beef, fresh vegetables, and artisanal buns creates an unforgettable dining experience.',
    address: '123 Main Street, Downtown District, City 12345',
    phone: '+****************',
    hours: {
      Monday: '11:00 AM - 10:00 PM',
      Tuesday: '11:00 AM - 10:00 PM',
      Wednesday: '11:00 AM - 10:00 PM',
      Thursday: '11:00 AM - 10:00 PM',
      Friday: '11:00 AM - 11:00 PM',
      Saturday: '10:00 AM - 11:00 PM',
      Sunday: '10:00 AM - 9:00 PM',
    },
    amenities: ['WiFi', 'Parking', 'Delivery', 'Credit Cards', 'Family Friendly'],
    features: ['Trending', 'Most Popular', 'Local Favorite'],
    menu: [
      {
        category: 'Signature Burgers',
        items: [
          {
            name: 'Classic Artisan Burger',
            description: 'Premium beef patty with lettuce, tomato, onion, and our signature sauce',
            price: '$14.99',
            image: burgerImage.src,
            isPopular: true,
          },
          {
            name: 'Bacon Cheeseburger Deluxe',
            description: 'Double beef patty with crispy bacon, cheddar cheese, and special sauce',
            price: '$17.99',
            image: burgerImage.src,
          },
          {
            name: 'Mushroom Swiss Burger',
            description: 'Grilled portobello mushrooms with Swiss cheese and garlic aioli',
            price: '$16.99',
            image: burgerImage.src,
          },
        ],
      },
      {
        category: 'Sides & Appetizers',
        items: [
          {
            name: 'Crispy Sweet Potato Fries',
            description: 'Hand-cut sweet potato fries with sea salt and herbs',
            price: '$6.99',
          },
          {
            name: 'Onion Rings',
            description: 'Beer-battered onion rings with ranch dipping sauce',
            price: '$5.99',
          },
          {
            name: 'Buffalo Wings',
            description: 'Spicy buffalo wings with blue cheese dip',
            price: '$9.99',
          },
        ],
      },
      {
        category: 'Beverages',
        items: [
          {
            name: 'Craft Beer Selection',
            description: 'Local and imported craft beers on tap',
            price: '$5.99 - $8.99',
          },
          {
            name: 'Fresh Lemonade',
            description: 'House-made lemonade with fresh mint',
            price: '$3.99',
          },
          {
            name: 'Milkshakes',
            description: 'Vanilla, chocolate, or strawberry milkshakes',
            price: '$4.99',
          },
        ],
      },
    ],
    reviews: [
      {
        id: '1',
        user: 'Sarah Johnson',
        rating: 5,
        comment:
          'Absolutely amazing burgers! The quality of ingredients is outstanding and the service is top-notch. Will definitely be back!',
        date: '2 days ago',
      },
      {
        id: '2',
        user: 'Mike Chen',
        rating: 4,
        comment:
          'Great atmosphere and delicious food. The bacon cheeseburger was incredible. Only minor issue was the wait time, but worth it.',
        date: '1 week ago',
      },
      {
        id: '3',
        user: 'Emily Rodriguez',
        rating: 5,
        comment:
          'Best burger place in town! The mushroom Swiss burger is my favorite. Staff is friendly and the place is always clean.',
        date: '2 weeks ago',
      },
      {
        id: '4',
        user: 'David Kim',
        rating: 4,
        comment:
          'Solid burgers with good portion sizes. The sweet potato fries are a must-try. Parking can be a bit tight during peak hours.',
        date: '3 weeks ago',
      },
    ],
  },
  '2': {
    id: '2',
    name: 'Sakura Sushi Bar',
    category: 'Japanese',
    image: sushiImage.src,
    rating: 4.9,
    reviewCount: 189,
    distance: '0.8 km',
    isOpen: true,
    priceRange: '$$$',
    isFavorite: true,
    description:
      'Authentic Japanese sushi experience with fresh fish flown in daily from Tokyo. Our master chefs create beautiful, delicious sushi using traditional techniques.',
    address: '456 Cherry Blossom Lane, East District, City 12345',
    phone: '+****************',
    hours: {
      Monday: 'Closed',
      Tuesday: '5:00 PM - 10:00 PM',
      Wednesday: '5:00 PM - 10:00 PM',
      Thursday: '5:00 PM - 10:00 PM',
      Friday: '5:00 PM - 11:00 PM',
      Saturday: '12:00 PM - 11:00 PM',
      Sunday: '12:00 PM - 9:00 PM',
    },
    amenities: ['WiFi', 'Credit Cards', 'Family Friendly'],
    features: ['Most Popular', 'Fresh Daily', 'Authentic'],
    menu: [
      {
        category: 'Sushi Rolls',
        items: [
          {
            name: 'Dragon Roll',
            description: 'Eel, cucumber, and avocado topped with eel sauce',
            price: '$16.99',
            image: sushiImage.src,
            isPopular: true,
          },
          {
            name: 'Spicy Tuna Roll',
            description: 'Fresh tuna with spicy mayo and cucumber',
            price: '$12.99',
            image: sushiImage.src,
          },
          {
            name: 'California Roll',
            description: 'Crab, avocado, and cucumber with sesame seeds',
            price: '$10.99',
            image: sushiImage.src,
          },
        ],
      },
      {
        category: 'Sashimi',
        items: [
          {
            name: 'Salmon Sashimi',
            description: 'Fresh Atlantic salmon, 6 pieces',
            price: '$14.99',
          },
          {
            name: 'Tuna Sashimi',
            description: 'Premium bluefin tuna, 6 pieces',
            price: '$18.99',
          },
          {
            name: 'Mixed Sashimi Platter',
            description: 'Assorted fresh fish, 12 pieces',
            price: '$24.99',
          },
        ],
      },
    ],
    reviews: [
      {
        id: '1',
        user: 'Yuki Tanaka',
        rating: 5,
        comment:
          'Authentic Japanese sushi experience! The fish is incredibly fresh and the presentation is beautiful. Highly recommend the Dragon Roll.',
        date: '1 day ago',
      },
      {
        id: '2',
        user: 'Alex Park',
        rating: 5,
        comment:
          'Best sushi in the city! The quality is outstanding and the chefs are true masters. The atmosphere is perfect for a date night.',
        date: '3 days ago',
      },
    ],
  },
  '3': {
    id: '3',
    name: 'Roast & Bean Coffee',
    category: 'Cafe & Coffee',
    image: coffeeImage.src,
    rating: 4.7,
    reviewCount: 156,
    distance: '0.3 km',
    isOpen: true,
    priceRange: '$',
    isFavorite: false,
    description:
      'Cozy neighborhood coffee shop serving artisanal coffee, fresh pastries, and light meals. Perfect for work, study, or catching up with friends.',
    address: '789 Coffee Street, West District, City 12345',
    phone: '+****************',
    hours: {
      Monday: '6:00 AM - 8:00 PM',
      Tuesday: '6:00 AM - 8:00 PM',
      Wednesday: '6:00 AM - 8:00 PM',
      Thursday: '6:00 AM - 8:00 PM',
      Friday: '6:00 AM - 9:00 PM',
      Saturday: '7:00 AM - 9:00 PM',
      Sunday: '7:00 AM - 7:00 PM',
    },
    amenities: ['WiFi', 'Credit Cards', 'Family Friendly', 'Pet Friendly'],
    features: ['New', 'Cozy Atmosphere', 'Great for Work'],
    menu: [
      {
        category: 'Coffee & Espresso',
        items: [
          {
            name: 'Signature Latte',
            description: 'Rich espresso with steamed milk and latte art',
            price: '$4.50',
            image: coffeeImage.src,
            isPopular: true,
          },
          {
            name: 'Cold Brew',
            description: 'Smooth cold-brewed coffee served over ice',
            price: '$3.99',
            image: coffeeImage.src,
          },
          {
            name: 'Cappuccino',
            description: 'Classic cappuccino with perfect foam',
            price: '$4.25',
            image: coffeeImage.src,
          },
        ],
      },
      {
        category: 'Pastries & Light Meals',
        items: [
          {
            name: 'Croissant',
            description: 'Buttery, flaky croissant served warm',
            price: '$2.99',
          },
          {
            name: 'Avocado Toast',
            description: 'Sourdough toast with smashed avocado and seasonings',
            price: '$8.99',
          },
          {
            name: 'Quiche Lorraine',
            description: 'Traditional quiche with bacon and cheese',
            price: '$6.99',
          },
        ],
      },
    ],
    reviews: [
      {
        id: '1',
        user: 'Jessica Lee',
        rating: 5,
        comment:
          'Love this place! Great coffee, friendly staff, and perfect atmosphere for working. The WiFi is fast and the pastries are delicious.',
        date: '1 day ago',
      },
      {
        id: '2',
        user: 'Tom Wilson',
        rating: 4,
        comment:
          'Solid coffee shop with good vibes. The cold brew is excellent and the space is comfortable for studying.',
        date: '4 days ago',
      },
    ],
  },
}

interface BusinessPageProps {
  params: {
    id: string
  }
}

export default async function BusinessPage({ params }: BusinessPageProps) {
  const { id } = await params

  const business = businessData[id as keyof typeof businessData]

  if (!business) {
    notFound()
  }

  return <BusinessPageDetailClient business={business} />
}
