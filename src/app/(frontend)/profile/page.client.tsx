'use client'

import { useState } from 'react'
import {
  User,
  MapPin,
  Heart,
  Star,
  Settings,
  CreditCard,
  Bell,
  HelpCircle,
  Camera,
  Edit3,
  Award,
  Calendar,
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'

const profileStats = [
  { label: 'Reviews', value: '47', icon: Star },
  { label: 'Photos', value: '123', icon: Camera },
  { label: 'Favorites', value: '29', icon: Heart },
  { label: 'Check-ins', value: '86', icon: MapPin },
]

const recentReviews = [
  {
    id: '1',
    restaurant: 'Bella Vista Restaurant',
    rating: 5,
    comment:
      'Amazing pasta and excellent service! The ambiance was perfect for our anniversary dinner.',
    date: '2 days ago',
    likes: 12,
  },
  {
    id: '2',
    restaurant: 'Morning Brew Coffee',
    rating: 4,
    comment: 'Great coffee and cozy atmosphere. Perfect spot for working remotely.',
    date: '1 week ago',
    likes: 8,
  },
]

const achievements = [
  { title: 'Food Explorer', description: 'Tried 5 different cuisines', icon: '🍽️' },
  { title: 'Review Master', description: 'Posted 25+ reviews', icon: '⭐' },
  { title: 'Local Guide', description: 'Helped 100+ people', icon: '🏆' },
]

export default function Profile() {
  const [user] = useState({
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    location: 'San Francisco, CA',
    joinDate: 'March 2023',
    avatar: '',
  })

  return (
    <div className="min-h-screen pb-20 md:pb-8">
      {/* Header */}
      <div className="glass-nav px-4 py-6 border-b border-white/20">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-display font-bold text-foreground">Profile</h1>
          <Button variant="outline" size="icon" className="rounded-full">
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="px-4 py-6 space-y-6">
        {/* Profile Header */}
        <Card className="glass-card">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center text-center space-y-4">
              <div className="relative">
                <Avatar className="h-24 w-24 border-4 border-primary/20">
                  <AvatarImage src={user.avatar} alt={user.name} />
                  <AvatarFallback className="text-2xl font-semibold bg-gradient-primary text-white">
                    SJ
                  </AvatarFallback>
                </Avatar>
                <Button
                  size="icon"
                  className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full shadow-lg"
                >
                  <Edit3 className="h-4 w-4" />
                </Button>
              </div>

              <div>
                <h2 className="text-2xl font-bold text-foreground">{user.name}</h2>
                <p className="text-muted-foreground">{user.email}</p>
                <div className="flex items-center justify-center gap-2 mt-2">
                  <MapPin className="h-4 w-4 text-primary" />
                  <span className="text-sm text-muted-foreground">{user.location}</span>
                </div>
                <div className="flex items-center justify-center gap-2 mt-1">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">
                    Member since {user.joinDate}
                  </span>
                </div>
              </div>

              <div className="flex gap-2">
                <Badge variant="secondary" className="font-medium">
                  <Award className="h-3 w-3 mr-1" />
                  Elite Reviewer
                </Badge>
                <Badge variant="outline">Local Guide</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {profileStats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <Card key={index} className="glass-card text-center hover-lift">
                <CardContent className="pt-4">
                  <Icon className="h-6 w-6 text-primary mx-auto mb-2" />
                  <div className="text-2xl font-bold text-foreground">{stat.value}</div>
                  <div className="text-sm text-muted-foreground">{stat.label}</div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Achievements */}
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5 text-primary" />
              Achievements
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3">
              {achievements.map((achievement, index) => (
                <div key={index} className="flex items-center gap-3 p-3 rounded-lg bg-secondary/50">
                  <span className="text-2xl">{achievement.icon}</span>
                  <div>
                    <h4 className="font-semibold text-foreground">{achievement.title}</h4>
                    <p className="text-sm text-muted-foreground">{achievement.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Reviews */}
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5 text-primary" />
              Recent Reviews
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentReviews.map((review) => (
                <div key={review.id} className="space-y-2">
                  <div className="flex items-start justify-between">
                    <div>
                      <h4 className="font-semibold text-foreground">{review.restaurant}</h4>
                      <div className="flex items-center gap-1">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${
                              i < review.rating ? 'fill-amber-400 text-amber-400' : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                    <span className="text-sm text-muted-foreground">{review.date}</span>
                  </div>
                  <p className="text-sm text-muted-foreground">{review.comment}</p>
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="sm" className="h-8 px-2">
                      <Heart className="h-3 w-3 mr-1" />
                      {review.likes}
                    </Button>
                  </div>
                  {recentReviews.indexOf(review) < recentReviews.length - 1 && (
                    <Separator className="mt-4" />
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 gap-4">
          <Button variant="outline" className="glass h-12">
            <CreditCard className="h-4 w-4 mr-2" />
            Payment Methods
          </Button>
          <Button variant="outline" className="glass h-12">
            <Bell className="h-4 w-4 mr-2" />
            Notifications
          </Button>
          <Button variant="outline" className="glass h-12">
            <HelpCircle className="h-4 w-4 mr-2" />
            Help & Support
          </Button>
          <Button variant="outline" className="glass h-12">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>
    </div>
  )
}
