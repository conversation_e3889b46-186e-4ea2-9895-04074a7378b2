'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { Layout } from '@/components/Merchant/Layout'
import {
  BarChart3,
  TrendingUp,
  Users,
  DollarSign,
  PieChart,
  Calendar,
  ArrowRight,
  Sparkles,
  Star,
  Zap,
  Target,
  Award,
} from 'lucide-react'

const ReportsPageClient = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('week')

  const reportCards = [
    {
      title: 'Sales Reports',
      description: 'Daily, weekly, and monthly sales analytics',
      icon: BarChart3,
      href: '/merchant/reports/sales',
      color: 'from-emerald-400 to-green-600',
      stats: { value: '$12,450', change: '+15.2%', period: 'This Week' },
      features: ['Revenue tracking', 'Sales trends', 'Period comparisons'],
    },
    {
      title: 'Menu Performance',
      description: 'Best-selling items and menu analytics',
      icon: TrendingUp,
      href: '/merchant/reports/menu-performance',
      color: 'from-amber-400 to-orange-600',
      stats: { value: '24', change: '+8.5%', period: 'Top Items' },
      features: ['Item popularity', 'Profit margins', 'Menu optimization'],
    },
    {
      title: 'Customer Analytics',
      description: 'Customer insights and behavior patterns',
      icon: Users,
      href: '/merchant/reports/customer-analytics',
      color: 'from-blue-400 to-indigo-600',
      stats: { value: '1,247', change: '+12.3%', period: 'Active Customers' },
      features: ['Customer segments', 'Retention rates', 'Order patterns'],
    },
    {
      title: 'Commission Reports',
      description: 'Commission deductions and payout tracking',
      icon: DollarSign,
      href: '/merchant/reports/commission',
      color: 'from-purple-400 to-pink-600',
      stats: { value: '$1,890', change: '+5.7%', period: 'This Month' },
      features: ['Payout history', 'Commission rates', 'Payment tracking'],
    },
  ]

  const quickStats = [
    {
      label: 'Total Revenue',
      value: '$45,230',
      change: '+18.2%',
      icon: DollarSign,
      color: 'from-emerald-400 to-green-600',
    },
    {
      label: 'Orders Today',
      value: '127',
      change: '+8.5%',
      icon: Target,
      color: 'from-amber-400 to-orange-600',
    },
    {
      label: 'Avg Order Value',
      value: '$28.50',
      change: '+12.1%',
      icon: Award,
      color: 'from-blue-400 to-indigo-600',
    },
    {
      label: 'Customer Satisfaction',
      value: '4.8/5',
      change: '+0.3',
      icon: Star,
      color: 'from-purple-400 to-pink-600',
    },
  ]

  return (
    <Layout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-amber-500/10 via-orange-500/10 to-red-500/10 backdrop-blur-xl rounded-2xl border border-amber-200/30 shadow-xl" />
          <div className="relative p-6 lg:p-8">
            <div className="flex items-center space-x-3 mb-2">
              <div className="w-12 h-12 bg-gradient-to-br from-amber-400 via-orange-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25">
                <BarChart3 className="h-6 w-6 text-white drop-shadow-lg" />
              </div>
              <div>
                <h1 className="text-4xl font-black bg-gradient-to-r from-amber-600 via-orange-600 to-red-600 bg-clip-text text-transparent drop-shadow-sm">
                  Reports & Analytics
                </h1>
                <p className="text-gray-600 mt-1">
                  Comprehensive insights for your restaurant business
                </p>
              </div>
            </div>

            {/* Period Selector */}
            <div className="flex items-center space-x-2 mt-4">
              <Calendar className="h-4 w-4 text-amber-600" />
              <span className="text-sm font-medium text-gray-700">View Period:</span>
              <div className="flex bg-white/80 backdrop-blur-sm rounded-lg border border-amber-200/50 p-1">
                {['day', 'week', 'month', 'year'].map((period) => (
                  <button
                    key={period}
                    onClick={() => setSelectedPeriod(period)}
                    className={`px-3 py-1 text-xs font-bold rounded-md transition-all duration-200 ${
                      selectedPeriod === period
                        ? 'bg-gradient-to-r from-amber-400 to-orange-500 text-white shadow-lg'
                        : 'text-gray-600 hover:text-amber-600 hover:bg-amber-50/50'
                    }`}
                  >
                    {period.charAt(0).toUpperCase() + period.slice(1)}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
          {quickStats.map((stat, _index) => (
            <div key={stat.label} className="relative group">
              <div
                className={`absolute inset-0 bg-gradient-to-br ${stat.color}/20 to-${stat.color.split('-')[1]}-500/20 backdrop-blur-xl rounded-2xl border border-${stat.color.split('-')[1]}-200/50 shadow-xl group-hover:shadow-2xl transition-all duration-300`}
              />
              <div className="relative p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">
                      {stat.label}
                    </p>
                    <div className="flex items-center mt-1">
                      <Sparkles className="h-3 w-3 text-amber-500 mr-1" />
                      <span className="text-xs text-amber-600 font-medium">Live Data</span>
                    </div>
                  </div>
                  <div
                    className={`relative w-12 h-12 bg-gradient-to-br ${stat.color} rounded-xl flex items-center justify-center shadow-lg shadow-${stat.color.split('-')[1]}-500/25 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <stat.icon className="h-6 w-6 text-white drop-shadow-lg" />
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full animate-pulse" />
                  </div>
                </div>
                <div className="text-3xl font-black text-gray-800 mb-2">{stat.value}</div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <TrendingUp className="h-4 w-4 text-emerald-600 mr-2" />
                    <span className="text-sm text-emerald-600 font-bold">{stat.change}</span>
                  </div>
                  <Zap className="h-4 w-4 text-amber-500 animate-pulse" />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Report Cards Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {reportCards.map((card, _index) => (
            <Link key={card.title} href={card.href} className="group">
              <div className="relative group">
                <div
                  className={`absolute inset-0 bg-gradient-to-br ${card.color}/10 via-${card.color.split('-')[1]}-500/10 to-${card.color.split('-')[2]}-500/10 backdrop-blur-xl rounded-2xl border border-${card.color.split('-')[1]}-200/30 shadow-xl group-hover:shadow-2xl transition-all duration-300`}
                />
                <div className="relative p-6 lg:p-8">
                  <div className="flex items-start justify-between mb-6">
                    <div className="flex items-center space-x-4">
                      <div
                        className={`w-14 h-14 bg-gradient-to-br ${card.color} rounded-xl flex items-center justify-center shadow-lg shadow-${card.color.split('-')[1]}-500/25 group-hover:scale-110 transition-transform duration-300`}
                      >
                        <card.icon className="h-7 w-7 text-white drop-shadow-lg" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-800 group-hover:text-gray-900 transition-colors">
                          {card.title}
                        </h3>
                        <p className="text-gray-600 text-sm mt-1">{card.description}</p>
                      </div>
                    </div>
                    <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-amber-600 group-hover:translate-x-1 transition-all duration-300" />
                  </div>

                  {/* Stats */}
                  <div className="mb-6">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-3xl font-black text-gray-800">{card.stats.value}</span>
                      <div className="flex items-center space-x-2">
                        <TrendingUp className="h-4 w-4 text-emerald-600" />
                        <span className="text-sm text-emerald-600 font-bold">
                          {card.stats.change}
                        </span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600">{card.stats.period}</p>
                  </div>

                  {/* Features */}
                  <div className="space-y-2">
                    {card.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-gradient-to-r from-amber-400 to-orange-500 rounded-full" />
                        <span className="text-sm text-gray-600">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* Hover Effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-amber-400/5 to-orange-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Recent Activity */}
        <div className="relative group">
          <div className="absolute inset-0 bg-gradient-to-br from-gray-50/80 to-amber-50/60 backdrop-blur-xl rounded-2xl border border-amber-200/30 shadow-xl" />
          <div className="relative p-6 lg:p-8">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-amber-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg shadow-amber-500/25">
                <PieChart className="h-5 w-5 text-white drop-shadow-lg" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-800">Recent Activity</h3>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse" />
                  <span className="text-xs text-emerald-600 font-medium">Live Updates</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              {[
                { action: 'New order received', time: '2 minutes ago', amount: '$24.50' },
                { action: 'Payment processed', time: '5 minutes ago', amount: '$18.75' },
                { action: 'Menu item updated', time: '12 minutes ago', amount: 'Pizza Margherita' },
                { action: 'Customer review added', time: '18 minutes ago', amount: '5 stars' },
                { action: 'Daily report generated', time: '1 hour ago', amount: '$1,247' },
              ].map((activity, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-amber-100/50 hover:bg-white/80 transition-colors duration-200"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-gradient-to-r from-amber-400 to-orange-500 rounded-full" />
                    <span className="text-sm text-gray-700">{activity.action}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-amber-600">{activity.amount}</span>
                    <span className="text-xs text-gray-500">{activity.time}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}

export default ReportsPageClient
