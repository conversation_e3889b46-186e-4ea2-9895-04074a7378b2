'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { Layout } from '@/components/Merchant/Layout'
import {
  TrendingUp,
  TrendingDown,
  Star,
  Clock,
  ArrowLeft,
  Filter,
  Search,
  MoreVertical,
  ChefHat,
  Award,
  Target,
  DollarSign,
  Sparkles,
} from 'lucide-react'

const MenuPerformancePageClient = () => {
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [sortBy, setSortBy] = useState('revenue')

  const categories = [
    { value: 'all', label: 'All Items', count: 24 },
    { value: 'appetizers', label: 'Appetizers', count: 6 },
    { value: 'mains', label: 'Main Courses', count: 12 },
    { value: 'desserts', label: 'Desserts', count: 4 },
    { value: 'beverages', label: 'Beverages', count: 8 },
  ]

  const menuItems = [
    {
      id: 1,
      name: 'Pizza Margherita',
      category: 'mains',
      price: 18.99,
      cost: 8.5,
      revenue: 1240.5,
      orders: 65,
      rating: 4.8,
      prepTime: 15,
      profitMargin: 55.2,
      trend: 'up',
      image: '/api/placeholder/80/80',
    },
    {
      id: 2,
      name: 'Chicken Burger',
      category: 'mains',
      price: 16.5,
      cost: 7.2,
      revenue: 980.25,
      orders: 59,
      rating: 4.6,
      prepTime: 12,
      profitMargin: 56.4,
      trend: 'up',
      image: '/api/placeholder/80/80',
    },
    {
      id: 3,
      name: 'Caesar Salad',
      category: 'appetizers',
      price: 12.99,
      cost: 4.8,
      revenue: 720.15,
      orders: 55,
      rating: 4.7,
      prepTime: 8,
      profitMargin: 63.1,
      trend: 'up',
      image: '/api/placeholder/80/80',
    },
    {
      id: 4,
      name: 'Pasta Carbonara',
      category: 'mains',
      price: 19.99,
      cost: 9.2,
      revenue: 650.4,
      orders: 32,
      rating: 4.5,
      prepTime: 18,
      profitMargin: 54.0,
      trend: 'down',
      image: '/api/placeholder/80/80',
    },
    {
      id: 5,
      name: 'Fish & Chips',
      category: 'mains',
      price: 17.5,
      cost: 8.9,
      revenue: 580.75,
      orders: 33,
      rating: 4.4,
      prepTime: 20,
      profitMargin: 49.1,
      trend: 'up',
      image: '/api/placeholder/80/80',
    },
    {
      id: 6,
      name: 'Chocolate Cake',
      category: 'desserts',
      price: 8.99,
      cost: 3.2,
      revenue: 450.3,
      orders: 50,
      rating: 4.9,
      prepTime: 5,
      profitMargin: 64.4,
      trend: 'up',
      image: '/api/placeholder/80/80',
    },
    {
      id: 7,
      name: 'Coca Cola',
      category: 'beverages',
      price: 3.99,
      cost: 1.2,
      revenue: 320.15,
      orders: 80,
      rating: 4.2,
      prepTime: 2,
      profitMargin: 69.9,
      trend: 'up',
      image: '/api/placeholder/80/80',
    },
    {
      id: 8,
      name: 'Garlic Bread',
      category: 'appetizers',
      price: 6.99,
      cost: 2.1,
      revenue: 280.5,
      orders: 40,
      rating: 4.3,
      prepTime: 6,
      profitMargin: 69.9,
      trend: 'down',
      image: '/api/placeholder/80/80',
    },
  ]

  const filteredItems =
    selectedCategory === 'all'
      ? menuItems
      : menuItems.filter((item) => item.category === selectedCategory)

  const sortedItems = [...filteredItems].sort((a, b) => {
    switch (sortBy) {
      case 'revenue':
        return b.revenue - a.revenue
      case 'orders':
        return b.orders - a.orders
      case 'rating':
        return b.rating - a.rating
      case 'profit':
        return b.profitMargin - a.profitMargin
      default:
        return 0
    }
  })

  const performanceMetrics = [
    {
      label: 'Total Items',
      value: '24',
      change: '+2',
      icon: ChefHat,
      color: 'from-amber-400 to-orange-600',
      description: 'Active menu items',
    },
    {
      label: 'Avg Rating',
      value: '4.6',
      change: '+0.2',
      icon: Star,
      color: 'from-yellow-400 to-amber-500',
      description: 'Customer satisfaction',
    },
    {
      label: 'Top Performer',
      value: 'Pizza Margherita',
      change: '+15.2%',
      icon: Award,
      color: 'from-emerald-400 to-green-600',
      description: 'Highest revenue item',
    },
    {
      label: 'Avg Prep Time',
      value: '12 min',
      change: '-2 min',
      icon: Clock,
      color: 'from-blue-400 to-indigo-600',
      description: 'Average preparation time',
    },
  ]

  const categoryStats = [
    { name: 'Main Courses', revenue: 2450.9, orders: 189, percentage: 45.2 },
    { name: 'Appetizers', revenue: 1000.65, orders: 95, percentage: 18.4 },
    { name: 'Desserts', revenue: 450.3, orders: 50, percentage: 8.3 },
    { name: 'Beverages', revenue: 1520.75, orders: 380, percentage: 28.1 },
  ]

  return (
    <Layout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-amber-500/10 via-orange-500/10 to-red-500/10 backdrop-blur-xl rounded-2xl border border-amber-200/30 shadow-xl" />
          <div className="relative p-6 lg:p-8">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4">
                <Link
                  href="/merchant/reports"
                  className="p-2 hover:bg-white/50 rounded-lg transition-colors"
                >
                  <ArrowLeft className="h-5 w-5 text-gray-600" />
                </Link>
                <div className="w-12 h-12 bg-gradient-to-br from-amber-400 to-orange-600 rounded-xl flex items-center justify-center shadow-lg shadow-amber-500/25">
                  <TrendingUp className="h-6 w-6 text-white drop-shadow-lg" />
                </div>
                <div>
                  <h1 className="text-4xl font-black bg-gradient-to-r from-amber-600 via-orange-600 to-red-600 bg-clip-text text-transparent drop-shadow-sm">
                    Menu Performance
                  </h1>
                  <p className="text-gray-600 mt-1">Analyze and optimize your menu offerings</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search items..."
                    className="pl-10 pr-4 py-2 w-64 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 text-sm"
                  />
                </div>
                <button className="group flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-lg hover:bg-white/90 hover:border-amber-300/70 transition-all duration-300 shadow-lg hover:shadow-xl">
                  <Filter className="h-4 w-4 mr-2 text-amber-600 group-hover:text-orange-600 transition-colors" />
                  <span className="font-semibold text-gray-700 group-hover:text-gray-800">
                    Filter
                  </span>
                </button>
              </div>
            </div>

            {/* Category Tabs */}
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700">Categories:</span>
              <div className="flex bg-white/80 backdrop-blur-sm rounded-lg border border-amber-200/50 p-1">
                {categories.map((category) => (
                  <button
                    key={category.value}
                    onClick={() => setSelectedCategory(category.value)}
                    className={`px-4 py-2 text-sm font-bold rounded-md transition-all duration-200 ${
                      selectedCategory === category.value
                        ? 'bg-gradient-to-r from-amber-400 to-orange-500 text-white shadow-lg'
                        : 'text-gray-600 hover:text-amber-600 hover:bg-amber-50/50'
                    }`}
                  >
                    {category.label} ({category.count})
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Performance Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
          {performanceMetrics.map((metric, _index) => (
            <div key={metric.label} className="relative group">
              <div
                className={`absolute inset-0 bg-gradient-to-br ${metric.color}/20 to-${metric.color.split('-')[1]}-500/20 backdrop-blur-xl rounded-2xl border border-${metric.color.split('-')[1]}-200/50 shadow-xl group-hover:shadow-2xl transition-all duration-300`}
              />
              <div className="relative p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">
                      {metric.label}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">{metric.description}</p>
                  </div>
                  <div
                    className={`relative w-12 h-12 bg-gradient-to-br ${metric.color} rounded-xl flex items-center justify-center shadow-lg shadow-${metric.color.split('-')[1]}-500/25 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <metric.icon className="h-6 w-6 text-white drop-shadow-lg" />
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full animate-pulse" />
                  </div>
                </div>
                <div className="text-3xl font-black text-gray-800 mb-2">{metric.value}</div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <TrendingUp className="h-4 w-4 text-emerald-600 mr-2" />
                    <span className="text-sm text-emerald-600 font-bold">{metric.change}</span>
                  </div>
                  <Sparkles className="h-4 w-4 text-amber-500 animate-pulse" />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Menu Items Table */}
        <div className="relative group">
          <div className="absolute inset-0 bg-gradient-to-br from-white/80 to-amber-50/60 backdrop-blur-xl rounded-2xl border border-amber-200/30 shadow-xl" />
          <div className="relative p-6 lg:p-8">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-amber-400 to-orange-600 rounded-xl flex items-center justify-center shadow-lg shadow-amber-500/25">
                  <ChefHat className="h-5 w-5 text-white drop-shadow-lg" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-800">Menu Items Performance</h3>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse" />
                    <span className="text-xs text-emerald-600 font-medium">Live Data</span>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <span className="text-sm font-medium text-gray-700">Sort by:</span>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-3 py-2 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-400/50 text-sm"
                >
                  <option value="revenue">Revenue</option>
                  <option value="orders">Orders</option>
                  <option value="rating">Rating</option>
                  <option value="profit">Profit Margin</option>
                </select>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-amber-200/50">
                    <th className="text-left py-3 px-4 font-bold text-gray-700">Item</th>
                    <th className="text-left py-3 px-4 font-bold text-gray-700">Price</th>
                    <th className="text-left py-3 px-4 font-bold text-gray-700">Revenue</th>
                    <th className="text-left py-3 px-4 font-bold text-gray-700">Orders</th>
                    <th className="text-left py-3 px-4 font-bold text-gray-700">Rating</th>
                    <th className="text-left py-3 px-4 font-bold text-gray-700">Profit</th>
                    <th className="text-left py-3 px-4 font-bold text-gray-700">Prep Time</th>
                    <th className="text-left py-3 px-4 font-bold text-gray-700">Trend</th>
                    <th className="text-left py-3 px-4 font-bold text-gray-700">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {sortedItems.map((item) => (
                    <tr
                      key={item.id}
                      className="border-b border-amber-100/50 hover:bg-amber-50/30 transition-colors duration-200"
                    >
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-12 h-12 bg-gradient-to-br from-amber-400 to-orange-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                            {item.name.charAt(0)}
                          </div>
                          <div>
                            <p className="font-semibold text-gray-800">{item.name}</p>
                            <p className="text-sm text-gray-600 capitalize">{item.category}</p>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <span className="font-bold text-gray-800">${item.price}</span>
                      </td>
                      <td className="py-4 px-4">
                        <span className="font-bold text-gray-800">${item.revenue.toFixed(2)}</span>
                      </td>
                      <td className="py-4 px-4">
                        <span className="font-bold text-gray-800">{item.orders}</span>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-1">
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                          <span className="font-bold text-gray-800">{item.rating}</span>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <span className="font-bold text-gray-800">
                          {item.profitMargin.toFixed(1)}%
                        </span>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-1">
                          <Clock className="h-4 w-4 text-blue-500" />
                          <span className="font-bold text-gray-800">{item.prepTime} min</span>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-1">
                          {item.trend === 'up' ? (
                            <TrendingUp className="h-4 w-4 text-emerald-600" />
                          ) : (
                            <TrendingDown className="h-4 w-4 text-red-600" />
                          )}
                          <span
                            className={`text-sm font-bold ${item.trend === 'up' ? 'text-emerald-600' : 'text-red-600'}`}
                          >
                            {item.trend === 'up' ? '+' : '-'}12.5%
                          </span>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <button className="p-2 hover:bg-amber-100 rounded-lg transition-colors">
                          <MoreVertical className="h-4 w-4 text-gray-600" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Category Performance */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-indigo-500/10 to-purple-500/10 backdrop-blur-xl rounded-2xl border border-blue-200/30 shadow-xl" />
            <div className="relative p-6 lg:p-8">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25">
                  <Target className="h-5 w-5 text-white drop-shadow-lg" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-800">Category Performance</h3>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse" />
                    <span className="text-xs text-emerald-600 font-medium">This Week</span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                {categoryStats.map((category, _index) => (
                  <div key={category.name} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-semibold text-gray-800">{category.name}</span>
                      <span className="text-sm text-gray-600">{category.percentage}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                      <div
                        className="h-full bg-gradient-to-r from-blue-400 to-indigo-500 rounded-full transition-all duration-1000 ease-out"
                        style={{ width: `${category.percentage}%` }}
                      />
                    </div>
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <span>${category.revenue.toFixed(2)}</span>
                      <span>{category.orders} orders</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/10 via-green-500/10 to-teal-500/10 backdrop-blur-xl rounded-2xl border border-emerald-200/30 shadow-xl" />
            <div className="relative p-6 lg:p-8">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-emerald-400 to-green-600 rounded-xl flex items-center justify-center shadow-lg shadow-emerald-500/25">
                  <DollarSign className="h-5 w-5 text-white drop-shadow-lg" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-800">Profit Analysis</h3>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse" />
                    <span className="text-xs text-emerald-600 font-medium">Live Data</span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-white/60 backdrop-blur-sm rounded-lg border border-emerald-100/50">
                  <div>
                    <p className="font-semibold text-gray-800">Total Revenue</p>
                    <p className="text-sm text-gray-600">This week</p>
                  </div>
                  <span className="text-2xl font-bold text-emerald-600">$5,421.50</span>
                </div>
                <div className="flex items-center justify-between p-4 bg-white/60 backdrop-blur-sm rounded-lg border border-emerald-100/50">
                  <div>
                    <p className="font-semibold text-gray-800">Total Costs</p>
                    <p className="text-sm text-gray-600">Ingredients & labor</p>
                  </div>
                  <span className="text-2xl font-bold text-red-600">$2,168.60</span>
                </div>
                <div className="flex items-center justify-between p-4 bg-gradient-to-r from-emerald-400/20 to-green-500/20 backdrop-blur-sm rounded-lg border border-emerald-200/50">
                  <div>
                    <p className="font-semibold text-gray-800">Net Profit</p>
                    <p className="text-sm text-gray-600">Profit margin</p>
                  </div>
                  <span className="text-2xl font-bold text-emerald-600">$3,252.90</span>
                </div>
                <div className="text-center">
                  <span className="text-sm text-gray-600">Profit Margin: </span>
                  <span className="text-lg font-bold text-emerald-600">60.0%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}

export default MenuPerformancePageClient
