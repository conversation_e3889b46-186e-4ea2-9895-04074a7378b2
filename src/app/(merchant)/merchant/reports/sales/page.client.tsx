'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { Layout } from '@/components/Merchant/Layout'
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Calendar,
  ArrowLeft,
  Download,
  Filter,
  RefreshCw,
  Target,
  Award,
  Sparkles,
} from 'lucide-react'

const SalesReportsPageClient = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('week')
  const [_selectedMetric, _setSelectedMetric] = useState('revenue')

  const periods = [
    { value: 'day', label: 'Today', data: { revenue: 1247, orders: 45, avgOrder: 27.7 } },
    { value: 'week', label: 'This Week', data: { revenue: 8450, orders: 312, avgOrder: 27.08 } },
    {
      value: 'month',
      label: 'This Month',
      data: { revenue: 34200, orders: 1247, avgOrder: 27.42 },
    },
    {
      value: 'year',
      label: 'This Year',
      data: { revenue: 412500, orders: 15230, avgOrder: 27.05 },
    },
  ]

  const currentPeriod = periods.find((p) => p.value === selectedPeriod) || periods[1]

  const salesMetrics = [
    {
      label: 'Total Revenue',
      value: `$${currentPeriod.data.revenue.toLocaleString()}`,
      change: '+15.2%',
      trend: 'up',
      icon: DollarSign,
      color: 'from-emerald-400 to-green-600',
      description: 'Total sales revenue',
    },
    {
      label: 'Total Orders',
      value: currentPeriod.data.orders.toLocaleString(),
      change: '+8.5%',
      trend: 'up',
      icon: Target,
      color: 'from-amber-400 to-orange-600',
      description: 'Number of orders placed',
    },
    {
      label: 'Average Order Value',
      value: `$${currentPeriod.data.avgOrder.toFixed(2)}`,
      change: '+12.1%',
      trend: 'up',
      icon: Award,
      color: 'from-blue-400 to-indigo-600',
      description: 'Average value per order',
    },
    {
      label: 'Growth Rate',
      value: '+18.2%',
      change: '+3.1%',
      trend: 'up',
      icon: TrendingUp,
      color: 'from-purple-400 to-pink-600',
      description: 'Period-over-period growth',
    },
  ]

  const hourlyData = [
    { hour: '6 AM', revenue: 120, orders: 4 },
    { hour: '7 AM', revenue: 280, orders: 8 },
    { hour: '8 AM', revenue: 450, orders: 12 },
    { hour: '9 AM', revenue: 320, orders: 9 },
    { hour: '10 AM', revenue: 180, orders: 5 },
    { hour: '11 AM', revenue: 240, orders: 7 },
    { hour: '12 PM', revenue: 850, orders: 24 },
    { hour: '1 PM', revenue: 920, orders: 26 },
    { hour: '2 PM', revenue: 680, orders: 19 },
    { hour: '3 PM', revenue: 420, orders: 12 },
    { hour: '4 PM', revenue: 380, orders: 11 },
    { hour: '5 PM', revenue: 520, orders: 15 },
    { hour: '6 PM', revenue: 780, orders: 22 },
    { hour: '7 PM', revenue: 950, orders: 27 },
    { hour: '8 PM', revenue: 720, orders: 20 },
    { hour: '9 PM', revenue: 480, orders: 14 },
    { hour: '10 PM', revenue: 320, orders: 9 },
    { hour: '11 PM', revenue: 180, orders: 5 },
  ]

  const topSellingItems = [
    { name: 'Pizza Margherita', revenue: '$1,240', orders: 45, growth: '+12.5%' },
    { name: 'Chicken Burger', revenue: '$980', orders: 38, growth: '+8.2%' },
    { name: 'Caesar Salad', revenue: '$720', orders: 28, growth: '+15.3%' },
    { name: 'Pasta Carbonara', revenue: '$650', orders: 25, growth: '+6.7%' },
    { name: 'Fish & Chips', revenue: '$580', orders: 22, growth: '+9.1%' },
  ]

  const recentOrders = [
    { id: '#1234', customer: 'John Doe', amount: '$24.50', time: '2 min ago', status: 'completed' },
    {
      id: '#1235',
      customer: 'Jane Smith',
      amount: '$18.75',
      time: '5 min ago',
      status: 'processing',
    },
    {
      id: '#1236',
      customer: 'Mike Johnson',
      amount: '$32.20',
      time: '8 min ago',
      status: 'completed',
    },
    {
      id: '#1237',
      customer: 'Sarah Wilson',
      amount: '$15.80',
      time: '12 min ago',
      status: 'completed',
    },
    {
      id: '#1238',
      customer: 'David Brown',
      amount: '$28.90',
      time: '15 min ago',
      status: 'pending',
    },
  ]

  return (
    <Layout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/10 via-green-500/10 to-teal-500/10 backdrop-blur-xl rounded-2xl border border-emerald-200/30 shadow-xl" />
          <div className="relative p-6 lg:p-8">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4">
                <Link
                  href="/merchant/reports"
                  className="p-2 hover:bg-white/50 rounded-lg transition-colors"
                >
                  <ArrowLeft className="h-5 w-5 text-gray-600" />
                </Link>
                <div className="w-12 h-12 bg-gradient-to-br from-emerald-400 to-green-600 rounded-xl flex items-center justify-center shadow-lg shadow-emerald-500/25">
                  <BarChart3 className="h-6 w-6 text-white drop-shadow-lg" />
                </div>
                <div>
                  <h1 className="text-4xl font-black bg-gradient-to-r from-emerald-600 via-green-600 to-teal-600 bg-clip-text text-transparent drop-shadow-sm">
                    Sales Reports
                  </h1>
                  <p className="text-gray-600 mt-1">
                    Track your sales performance and revenue trends
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <button className="group flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm border border-emerald-200/50 rounded-lg hover:bg-white/90 hover:border-emerald-300/70 transition-all duration-300 shadow-lg hover:shadow-xl">
                  <Download className="h-4 w-4 mr-2 text-emerald-600 group-hover:text-green-600 transition-colors" />
                  <span className="font-semibold text-gray-700 group-hover:text-gray-800">
                    Export
                  </span>
                </button>
                <button className="group flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm border border-emerald-200/50 rounded-lg hover:bg-white/90 hover:border-emerald-300/70 transition-all duration-300 shadow-lg hover:shadow-xl">
                  <RefreshCw className="h-4 w-4 mr-2 text-emerald-600 group-hover:text-green-600 transition-colors" />
                  <span className="font-semibold text-gray-700 group-hover:text-gray-800">
                    Refresh
                  </span>
                </button>
              </div>
            </div>

            {/* Period Selector */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-emerald-600" />
                <span className="text-sm font-medium text-gray-700">Time Period:</span>
              </div>
              <div className="flex bg-white/80 backdrop-blur-sm rounded-lg border border-emerald-200/50 p-1">
                {periods.map((period) => (
                  <button
                    key={period.value}
                    onClick={() => setSelectedPeriod(period.value)}
                    className={`px-4 py-2 text-sm font-bold rounded-md transition-all duration-200 ${
                      selectedPeriod === period.value
                        ? 'bg-gradient-to-r from-emerald-400 to-green-500 text-white shadow-lg'
                        : 'text-gray-600 hover:text-emerald-600 hover:bg-emerald-50/50'
                    }`}
                  >
                    {period.label}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Sales Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
          {salesMetrics.map((metric, _index) => (
            <div key={metric.label} className="relative group">
              <div
                className={`absolute inset-0 bg-gradient-to-br ${metric.color}/20 to-${metric.color.split('-')[1]}-500/20 backdrop-blur-xl rounded-2xl border border-${metric.color.split('-')[1]}-200/50 shadow-xl group-hover:shadow-2xl transition-all duration-300`}
              />
              <div className="relative p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">
                      {metric.label}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">{metric.description}</p>
                  </div>
                  <div
                    className={`relative w-12 h-12 bg-gradient-to-br ${metric.color} rounded-xl flex items-center justify-center shadow-lg shadow-${metric.color.split('-')[1]}-500/25 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <metric.icon className="h-6 w-6 text-white drop-shadow-lg" />
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full animate-pulse" />
                  </div>
                </div>
                <div className="text-3xl font-black text-gray-800 mb-2">{metric.value}</div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {metric.trend === 'up' ? (
                      <TrendingUp className="h-4 w-4 text-emerald-600 mr-2" />
                    ) : (
                      <TrendingDown className="h-4 w-4 text-red-600 mr-2" />
                    )}
                    <span
                      className={`text-sm font-bold ${metric.trend === 'up' ? 'text-emerald-600' : 'text-red-600'}`}
                    >
                      {metric.change}
                    </span>
                  </div>
                  <Sparkles className="h-4 w-4 text-amber-500 animate-pulse" />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Hourly Sales Chart */}
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-indigo-500/10 to-purple-500/10 backdrop-blur-xl rounded-2xl border border-blue-200/30 shadow-xl" />
            <div className="relative p-6 lg:p-8">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25">
                    <BarChart3 className="h-5 w-5 text-white drop-shadow-lg" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-800">Hourly Sales</h3>
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse" />
                      <span className="text-xs text-emerald-600 font-medium">Live Data</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Filter className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Last 24 hours</span>
                </div>
              </div>

              {/* Simple Bar Chart */}
              <div className="space-y-3">
                {hourlyData.slice(6, 18).map((data, _index) => (
                  <div key={data.hour} className="flex items-center space-x-3">
                    <div className="w-12 text-xs text-gray-600 font-medium">{data.hour}</div>
                    <div className="flex-1 bg-gray-100 rounded-full h-6 overflow-hidden">
                      <div
                        className="h-full bg-gradient-to-r from-blue-400 to-indigo-500 rounded-full transition-all duration-1000 ease-out"
                        style={{ width: `${(data.revenue / 1000) * 100}%` }}
                      />
                    </div>
                    <div className="w-16 text-xs text-gray-600 font-bold text-right">
                      ${data.revenue}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Top Selling Items */}
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-amber-500/10 via-orange-500/10 to-red-500/10 backdrop-blur-xl rounded-2xl border border-amber-200/30 shadow-xl" />
            <div className="relative p-6 lg:p-8">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-amber-400 to-orange-600 rounded-xl flex items-center justify-center shadow-lg shadow-amber-500/25">
                  <Award className="h-5 w-5 text-white drop-shadow-lg" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-800">Top Selling Items</h3>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse" />
                    <span className="text-xs text-emerald-600 font-medium">This Week</span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                {topSellingItems.map((item, _index) => (
                  <div
                    key={item.name}
                    className="flex items-center justify-between p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-amber-100/50 hover:bg-white/80 transition-colors duration-200"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-amber-400 to-orange-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                        {_index + 1}
                      </div>
                      <div>
                        <p className="font-semibold text-gray-800">{item.name}</p>
                        <p className="text-sm text-gray-600">{item.orders} orders</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-gray-800">{item.revenue}</p>
                      <div className="flex items-center space-x-1">
                        <TrendingUp className="h-3 w-3 text-emerald-600" />
                        <span className="text-xs text-emerald-600 font-medium">{item.growth}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Recent Orders */}
        <div className="relative group">
          <div className="absolute inset-0 bg-gradient-to-br from-gray-50/80 to-emerald-50/60 backdrop-blur-xl rounded-2xl border border-emerald-200/30 shadow-xl" />
          <div className="relative p-6 lg:p-8">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-emerald-400 to-green-600 rounded-xl flex items-center justify-center shadow-lg shadow-emerald-500/25">
                  <Target className="h-5 w-5 text-white drop-shadow-lg" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-800">Recent Orders</h3>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse" />
                    <span className="text-xs text-emerald-600 font-medium">Live Updates</span>
                  </div>
                </div>
              </div>
              <button className="text-sm text-emerald-600 hover:text-green-600 font-medium">
                View All
              </button>
            </div>

            <div className="space-y-3">
              {recentOrders.map((order, _index) => (
                <div
                  key={order.id}
                  className="flex items-center justify-between p-4 bg-white/60 backdrop-blur-sm rounded-lg border border-emerald-100/50 hover:bg-white/80 transition-colors duration-200"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-gradient-to-br from-emerald-400 to-green-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                      {order.id.slice(-2)}
                    </div>
                    <div>
                      <p className="font-semibold text-gray-800">{order.customer}</p>
                      <p className="text-sm text-gray-600">{order.id}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <p className="font-bold text-gray-800">{order.amount}</p>
                      <p className="text-sm text-gray-600">{order.time}</p>
                    </div>
                    <span
                      className={`px-3 py-1 text-xs font-bold rounded-full ${
                        order.status === 'completed'
                          ? 'bg-gradient-to-r from-emerald-400/20 to-green-500/20 text-emerald-800 border border-emerald-200/50'
                          : order.status === 'processing'
                            ? 'bg-gradient-to-r from-amber-400/20 to-orange-500/20 text-amber-800 border border-amber-200/50'
                            : 'bg-gradient-to-r from-blue-400/20 to-indigo-500/20 text-blue-800 border border-blue-200/50'
                      }`}
                    >
                      {order.status.toUpperCase()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}

export default SalesReportsPageClient
