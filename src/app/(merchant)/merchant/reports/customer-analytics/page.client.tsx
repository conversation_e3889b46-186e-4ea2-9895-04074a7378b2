'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { Layout } from '@/components/Merchant/Layout'
import {
  Users,
  TrendingUp,
  TrendingDown,
  Star,
  ArrowLeft,
  Filter,
  Search,
  UserPlus,
  Repeat,
  Heart,
  Award,
  Target,
  DollarSign,
  Calendar,
  Sparkles,
} from 'lucide-react'

const CustomerAnalyticsPageClient = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('month')
  const [selectedSegment, setSelectedSegment] = useState('all')

  const periods = [
    { value: 'week', label: 'This Week' },
    { value: 'month', label: 'This Month' },
    { value: 'quarter', label: 'This Quarter' },
    { value: 'year', label: 'This Year' },
  ]

  const segments = [
    { value: 'all', label: 'All Customers', count: 1247 },
    { value: 'new', label: 'New Customers', count: 89 },
    { value: 'returning', label: 'Returning', count: 1158 },
    { value: 'vip', label: 'VIP Customers', count: 45 },
  ]

  const customerMetrics = [
    {
      label: 'Total Customers',
      value: '1,247',
      change: '+12.3%',
      trend: 'up',
      icon: Users,
      color: 'from-blue-400 to-indigo-600',
      description: 'Active customer base',
    },
    {
      label: 'New Customers',
      value: '89',
      change: '+8.5%',
      trend: 'up',
      icon: UserPlus,
      color: 'from-emerald-400 to-green-600',
      description: 'This month',
    },
    {
      label: 'Retention Rate',
      value: '78.5%',
      change: '+5.2%',
      trend: 'up',
      icon: Repeat,
      color: 'from-amber-400 to-orange-600',
      description: 'Customer loyalty',
    },
    {
      label: 'Avg Order Value',
      value: '$28.50',
      change: '+12.1%',
      trend: 'up',
      icon: DollarSign,
      color: 'from-purple-400 to-pink-600',
      description: 'Per customer',
    },
  ]

  const customerSegments = [
    {
      name: 'VIP Customers',
      count: 45,
      percentage: 3.6,
      avgOrderValue: 85.5,
      frequency: 12.5,
      color: 'from-yellow-400 to-amber-500',
      description: 'High-value customers',
    },
    {
      name: 'Regular Customers',
      count: 456,
      percentage: 36.5,
      avgOrderValue: 32.2,
      frequency: 4.2,
      color: 'from-blue-400 to-indigo-500',
      description: 'Frequent visitors',
    },
    {
      name: 'Occasional Customers',
      count: 623,
      percentage: 49.9,
      avgOrderValue: 18.75,
      frequency: 1.8,
      color: 'from-emerald-400 to-green-500',
      description: 'Infrequent visitors',
    },
    {
      name: 'New Customers',
      count: 123,
      percentage: 9.9,
      avgOrderValue: 24.3,
      frequency: 1.0,
      color: 'from-purple-400 to-pink-500',
      description: 'First-time visitors',
    },
  ]

  const topCustomers = [
    {
      id: 1,
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      totalOrders: 45,
      totalSpent: 1280.5,
      lastOrder: '2 days ago',
      segment: 'VIP',
      rating: 5.0,
      avatar: '/api/placeholder/40/40',
    },
    {
      id: 2,
      name: 'Mike Chen',
      email: '<EMAIL>',
      totalOrders: 38,
      totalSpent: 980.25,
      lastOrder: '1 day ago',
      segment: 'VIP',
      rating: 4.9,
      avatar: '/api/placeholder/40/40',
    },
    {
      id: 3,
      name: 'Emily Davis',
      email: '<EMAIL>',
      totalOrders: 32,
      totalSpent: 720.15,
      lastOrder: '3 days ago',
      segment: 'Regular',
      rating: 4.8,
      avatar: '/api/placeholder/40/40',
    },
    {
      id: 4,
      name: 'David Wilson',
      email: '<EMAIL>',
      totalOrders: 28,
      totalSpent: 650.4,
      lastOrder: '1 week ago',
      segment: 'Regular',
      rating: 4.7,
      avatar: '/api/placeholder/40/40',
    },
    {
      id: 5,
      name: 'Lisa Brown',
      email: '<EMAIL>',
      totalOrders: 25,
      totalSpent: 580.75,
      lastOrder: '2 days ago',
      segment: 'Regular',
      rating: 4.6,
      avatar: '/api/placeholder/40/40',
    },
  ]

  const customerBehavior = [
    { metric: 'Peak Order Time', value: '7:00 PM - 8:00 PM', change: '+15%', trend: 'up' },
    { metric: 'Most Popular Day', value: 'Friday', change: '+8%', trend: 'up' },
    { metric: 'Avg Order Frequency', value: '3.2 orders/month', change: '+12%', trend: 'up' },
    { metric: 'Customer Satisfaction', value: '4.7/5.0', change: '+0.3', trend: 'up' },
    { metric: 'Repeat Order Rate', value: '68.5%', change: '+5.2%', trend: 'up' },
    { metric: 'Referral Rate', value: '23.8%', change: '+3.1%', trend: 'up' },
  ]

  const filteredCustomers =
    selectedSegment === 'all'
      ? topCustomers
      : topCustomers.filter((customer) => customer.segment.toLowerCase() === selectedSegment)

  return (
    <Layout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-indigo-500/10 to-purple-500/10 backdrop-blur-xl rounded-2xl border border-blue-200/30 shadow-xl" />
          <div className="relative p-6 lg:p-8">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4">
                <Link
                  href="/merchant/reports"
                  className="p-2 hover:bg-white/50 rounded-lg transition-colors"
                >
                  <ArrowLeft className="h-5 w-5 text-gray-600" />
                </Link>
                <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25">
                  <Users className="h-6 w-6 text-white drop-shadow-lg" />
                </div>
                <div>
                  <h1 className="text-4xl font-black bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent drop-shadow-sm">
                    Customer Analytics
                  </h1>
                  <p className="text-gray-600 mt-1">Understand your customers and their behavior</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search customers..."
                    className="pl-10 pr-4 py-2 w-64 bg-white/80 backdrop-blur-sm border border-blue-200/50 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400/50 focus:border-blue-400/50 text-sm"
                  />
                </div>
                <button className="group flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm border border-blue-200/50 rounded-lg hover:bg-white/90 hover:border-blue-300/70 transition-all duration-300 shadow-lg hover:shadow-xl">
                  <Filter className="h-4 w-4 mr-2 text-blue-600 group-hover:text-indigo-600 transition-colors" />
                  <span className="font-semibold text-gray-700 group-hover:text-gray-800">
                    Filter
                  </span>
                </button>
              </div>
            </div>

            {/* Period and Segment Selectors */}
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-gray-700">Period:</span>
                <div className="flex bg-white/80 backdrop-blur-sm rounded-lg border border-blue-200/50 p-1">
                  {periods.map((period) => (
                    <button
                      key={period.value}
                      onClick={() => setSelectedPeriod(period.value)}
                      className={`px-3 py-1 text-sm font-bold rounded-md transition-all duration-200 ${
                        selectedPeriod === period.value
                          ? 'bg-gradient-to-r from-blue-400 to-indigo-500 text-white shadow-lg'
                          : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50/50'
                      }`}
                    >
                      {period.label}
                    </button>
                  ))}
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Target className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-gray-700">Segment:</span>
                <div className="flex bg-white/80 backdrop-blur-sm rounded-lg border border-blue-200/50 p-1">
                  {segments.map((segment) => (
                    <button
                      key={segment.value}
                      onClick={() => setSelectedSegment(segment.value)}
                      className={`px-3 py-1 text-sm font-bold rounded-md transition-all duration-200 ${
                        selectedSegment === segment.value
                          ? 'bg-gradient-to-r from-blue-400 to-indigo-500 text-white shadow-lg'
                          : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50/50'
                      }`}
                    >
                      {segment.label} ({segment.count})
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Customer Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
          {customerMetrics.map((metric, _index) => (
            <div key={metric.label} className="relative group">
              <div
                className={`absolute inset-0 bg-gradient-to-br ${metric.color}/20 to-${metric.color.split('-')[1]}-500/20 backdrop-blur-xl rounded-2xl border border-${metric.color.split('-')[1]}-200/50 shadow-xl group-hover:shadow-2xl transition-all duration-300`}
              />
              <div className="relative p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">
                      {metric.label}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">{metric.description}</p>
                  </div>
                  <div
                    className={`relative w-12 h-12 bg-gradient-to-br ${metric.color} rounded-xl flex items-center justify-center shadow-lg shadow-${metric.color.split('-')[1]}-500/25 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <metric.icon className="h-6 w-6 text-white drop-shadow-lg" />
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full animate-pulse" />
                  </div>
                </div>
                <div className="text-3xl font-black text-gray-800 mb-2">{metric.value}</div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {metric.trend === 'up' ? (
                      <TrendingUp className="h-4 w-4 text-emerald-600 mr-2" />
                    ) : (
                      <TrendingDown className="h-4 w-4 text-red-600 mr-2" />
                    )}
                    <span
                      className={`text-sm font-bold ${metric.trend === 'up' ? 'text-emerald-600' : 'text-red-600'}`}
                    >
                      {metric.change}
                    </span>
                  </div>
                  <Sparkles className="h-4 w-4 text-amber-500 animate-pulse" />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Customer Segments and Top Customers */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Customer Segments */}
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-amber-500/10 via-orange-500/10 to-red-500/10 backdrop-blur-xl rounded-2xl border border-amber-200/30 shadow-xl" />
            <div className="relative p-6 lg:p-8">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-amber-400 to-orange-600 rounded-xl flex items-center justify-center shadow-lg shadow-amber-500/25">
                  <Target className="h-5 w-5 text-white drop-shadow-lg" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-800">Customer Segments</h3>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse" />
                    <span className="text-xs text-emerald-600 font-medium">Live Data</span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                {customerSegments.map((segment, _index) => (
                  <div key={segment.name} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div
                          className={`w-4 h-4 bg-gradient-to-br ${segment.color} rounded-full`}
                        />
                        <span className="font-semibold text-gray-800">{segment.name}</span>
                        <span className="text-sm text-gray-600">({segment.count})</span>
                      </div>
                      <span className="text-sm text-gray-600">{segment.percentage}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                      <div
                        className={`h-full bg-gradient-to-r ${segment.color} rounded-full transition-all duration-1000 ease-out`}
                        style={{ width: `${segment.percentage}%` }}
                      />
                    </div>
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <span>Avg: ${segment.avgOrderValue}</span>
                      <span>{segment.frequency} orders/month</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Top Customers */}
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/10 via-green-500/10 to-teal-500/10 backdrop-blur-xl rounded-2xl border border-emerald-200/30 shadow-xl" />
            <div className="relative p-6 lg:p-8">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-emerald-400 to-green-600 rounded-xl flex items-center justify-center shadow-lg shadow-emerald-500/25">
                    <Award className="h-5 w-5 text-white drop-shadow-lg" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-800">Top Customers</h3>
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse" />
                      <span className="text-xs text-emerald-600 font-medium">This Month</span>
                    </div>
                  </div>
                </div>
                <button className="text-sm text-emerald-600 hover:text-green-600 font-medium">
                  View All
                </button>
              </div>

              <div className="space-y-3">
                {filteredCustomers.map((customer, _index) => (
                  <div
                    key={customer.id}
                    className="flex items-center justify-between p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-emerald-100/50 hover:bg-white/80 transition-colors duration-200"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-emerald-400 to-green-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                        {customer.name.charAt(0)}
                      </div>
                      <div>
                        <p className="font-semibold text-gray-800">{customer.name}</p>
                        <p className="text-sm text-gray-600">{customer.email}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-gray-800">${customer.totalSpent.toFixed(2)}</p>
                      <div className="flex items-center space-x-2">
                        <div className="flex items-center space-x-1">
                          <Star className="h-3 w-3 text-yellow-500 fill-current" />
                          <span className="text-xs text-gray-600">{customer.rating}</span>
                        </div>
                        <span
                          className={`px-2 py-1 text-xs font-bold rounded-full ${
                            customer.segment === 'VIP'
                              ? 'bg-gradient-to-r from-yellow-400/20 to-amber-500/20 text-amber-800 border border-amber-200/50'
                              : 'bg-gradient-to-r from-blue-400/20 to-indigo-500/20 text-blue-800 border border-blue-200/50'
                          }`}
                        >
                          {customer.segment}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Customer Behavior Analytics */}
        <div className="relative group">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-pink-500/10 to-rose-500/10 backdrop-blur-xl rounded-2xl border border-purple-200/30 shadow-xl" />
          <div className="relative p-6 lg:p-8">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-purple-400 to-pink-600 rounded-xl flex items-center justify-center shadow-lg shadow-purple-500/25">
                <Heart className="h-5 w-5 text-white drop-shadow-lg" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-800">Customer Behavior Insights</h3>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse" />
                  <span className="text-xs text-emerald-600 font-medium">Behavioral Data</span>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {customerBehavior.map((behavior, _index) => (
                <div
                  key={behavior.metric}
                  className="p-4 bg-white/60 backdrop-blur-sm rounded-lg border border-purple-100/50 hover:bg-white/80 transition-colors duration-200"
                >
                  <div className="flex items-center justify-between mb-2">
                    <p className="text-sm font-semibold text-gray-800">{behavior.metric}</p>
                    <div className="flex items-center space-x-1">
                      {behavior.trend === 'up' ? (
                        <TrendingUp className="h-3 w-3 text-emerald-600" />
                      ) : (
                        <TrendingDown className="h-3 w-3 text-red-600" />
                      )}
                      <span
                        className={`text-xs font-bold ${behavior.trend === 'up' ? 'text-emerald-600' : 'text-red-600'}`}
                      >
                        {behavior.change}
                      </span>
                    </div>
                  </div>
                  <p className="text-lg font-bold text-gray-800">{behavior.value}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Customer Retention Funnel */}
        <div className="relative group">
          <div className="absolute inset-0 bg-gradient-to-br from-gray-50/80 to-blue-50/60 backdrop-blur-xl rounded-2xl border border-blue-200/30 shadow-xl" />
          <div className="relative p-6 lg:p-8">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25">
                <Repeat className="h-5 w-5 text-white drop-shadow-lg" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-800">Customer Retention Funnel</h3>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse" />
                  <span className="text-xs text-emerald-600 font-medium">Retention Analysis</span>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {[
                {
                  stage: 'First Visit',
                  count: 123,
                  percentage: 100,
                  color: 'from-blue-400 to-indigo-500',
                },
                {
                  stage: 'Second Visit',
                  count: 89,
                  percentage: 72.4,
                  color: 'from-emerald-400 to-green-500',
                },
                {
                  stage: 'Third Visit',
                  count: 67,
                  percentage: 54.5,
                  color: 'from-amber-400 to-orange-500',
                },
                {
                  stage: 'Regular Customer',
                  count: 45,
                  percentage: 36.6,
                  color: 'from-purple-400 to-pink-500',
                },
              ].map((stage, _index) => (
                <div key={stage.stage} className="text-center">
                  <div
                    className={`w-20 h-20 mx-auto mb-3 bg-gradient-to-br ${stage.color} rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg`}
                  >
                    {stage.count}
                  </div>
                  <p className="font-semibold text-gray-800 mb-1">{stage.stage}</p>
                  <p className="text-sm text-gray-600">{stage.percentage}%</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}

export default CustomerAnalyticsPageClient
