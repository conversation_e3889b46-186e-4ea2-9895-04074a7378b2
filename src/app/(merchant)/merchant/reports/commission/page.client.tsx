'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { Layout } from '@/components/Merchant/Layout'
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  Calendar,
  ArrowLeft,
  Download,
  Filter,
  RefreshCw,
  CreditCard,
  Banknote,
  Receipt,
  Target,
  Award,
  Clock,
  CheckCircle,
  AlertCircle,
  Sparkles,
  Zap,
} from 'lucide-react'

const CommissionReportsPageClient = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('month')
  const [selectedStatus, setSelectedStatus] = useState('all')

  const periods = [
    { value: 'week', label: 'This Week', data: { total: 1250.5, paid: 1000.0, pending: 250.5 } },
    {
      value: 'month',
      label: 'This Month',
      data: { total: 4890.75, paid: 4200.0, pending: 690.75 },
    },
    {
      value: 'quarter',
      label: 'This Quarter',
      data: { total: 15230.25, paid: 13800.0, pending: 1430.25 },
    },
    { value: 'year', label: 'This Year', data: { total: 61250.8, paid: 58000.0, pending: 3250.8 } },
  ]

  const currentPeriod = periods.find((p) => p.value === selectedPeriod) || periods[1]

  const statuses = [
    { value: 'all', label: 'All Payments', count: 24 },
    { value: 'paid', label: 'Paid', count: 18 },
    { value: 'pending', label: 'Pending', count: 4 },
    { value: 'failed', label: 'Failed', count: 2 },
  ]

  const commissionMetrics = [
    {
      label: 'Total Commission',
      value: `$${currentPeriod.data.total.toLocaleString()}`,
      change: '+8.5%',
      trend: 'up',
      icon: DollarSign,
      color: 'from-emerald-400 to-green-600',
      description: 'Total commission earned',
    },
    {
      label: 'Paid Out',
      value: `$${currentPeriod.data.paid.toLocaleString()}`,
      change: '+12.3%',
      trend: 'up',
      icon: CheckCircle,
      color: 'from-blue-400 to-indigo-600',
      description: 'Amount already paid',
    },
    {
      label: 'Pending',
      value: `$${currentPeriod.data.pending.toLocaleString()}`,
      change: '-5.2%',
      trend: 'down',
      icon: Clock,
      color: 'from-amber-400 to-orange-600',
      description: 'Awaiting payment',
    },
    {
      label: 'Commission Rate',
      value: '12.5%',
      change: '+0.5%',
      trend: 'up',
      icon: Target,
      color: 'from-purple-400 to-pink-600',
      description: 'Platform commission rate',
    },
  ]

  const payoutHistory = [
    {
      id: 'PAY-001',
      date: '2024-01-15',
      amount: 1250.5,
      status: 'paid',
      method: 'Bank Transfer',
      reference: 'TXN-*********',
      description: 'Weekly payout - Jan 8-14',
    },
    {
      id: 'PAY-002',
      date: '2024-01-08',
      amount: 1180.25,
      status: 'paid',
      method: 'Bank Transfer',
      reference: 'TXN-*********',
      description: 'Weekly payout - Jan 1-7',
    },
    {
      id: 'PAY-003',
      date: '2024-01-01',
      amount: 1350.75,
      status: 'paid',
      method: 'Bank Transfer',
      reference: 'TXN-*********',
      description: 'Weekly payout - Dec 25-31',
    },
    {
      id: 'PAY-004',
      date: '2024-01-22',
      amount: 980.5,
      status: 'pending',
      method: 'Bank Transfer',
      reference: 'TXN-*********',
      description: 'Weekly payout - Jan 15-21',
    },
    {
      id: 'PAY-005',
      date: '2024-01-20',
      amount: 750.25,
      status: 'failed',
      method: 'Bank Transfer',
      reference: 'TXN-*********',
      description: 'Weekly payout - Jan 15-21 (Retry)',
    },
  ]

  const commissionBreakdown = [
    {
      category: 'Food Orders',
      orders: 1247,
      revenue: 34200.5,
      commission: 4275.06,
      rate: 12.5,
      color: 'from-emerald-400 to-green-500',
    },
    {
      category: 'Delivery Fees',
      orders: 1247,
      revenue: 2494.0,
      commission: 249.4,
      rate: 10.0,
      color: 'from-blue-400 to-indigo-500',
    },
    {
      category: 'Service Fees',
      orders: 1247,
      revenue: 1710.0,
      commission: 171.0,
      rate: 10.0,
      color: 'from-amber-400 to-orange-500',
    },
    {
      category: 'Processing Fees',
      orders: 1247,
      revenue: 1026.0,
      commission: 0.0,
      rate: 0.0,
      color: 'from-purple-400 to-pink-500',
    },
  ]

  const recentTransactions = [
    {
      id: 'TXN-001',
      orderId: '#1234',
      customer: 'John Doe',
      amount: 24.5,
      commission: 3.06,
      date: '2024-01-22 14:30',
      status: 'completed',
    },
    {
      id: 'TXN-002',
      orderId: '#1235',
      customer: 'Jane Smith',
      amount: 18.75,
      commission: 2.34,
      date: '2024-01-22 14:25',
      status: 'completed',
    },
    {
      id: 'TXN-003',
      orderId: '#1236',
      customer: 'Mike Johnson',
      amount: 32.2,
      commission: 4.03,
      date: '2024-01-22 14:20',
      status: 'completed',
    },
    {
      id: 'TXN-004',
      orderId: '#1237',
      customer: 'Sarah Wilson',
      amount: 15.8,
      commission: 1.98,
      date: '2024-01-22 14:15',
      status: 'completed',
    },
    {
      id: 'TXN-005',
      orderId: '#1238',
      customer: 'David Brown',
      amount: 28.9,
      commission: 3.61,
      date: '2024-01-22 14:10',
      status: 'processing',
    },
  ]

  const filteredPayouts =
    selectedStatus === 'all'
      ? payoutHistory
      : payoutHistory.filter((payout) => payout.status === selectedStatus)

  return (
    <Layout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-rose-500/10 backdrop-blur-xl rounded-2xl border border-purple-200/30 shadow-xl" />
          <div className="relative p-6 lg:p-8">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4">
                <Link
                  href="/merchant/reports"
                  className="p-2 hover:bg-white/50 rounded-lg transition-colors"
                >
                  <ArrowLeft className="h-5 w-5 text-gray-600" />
                </Link>
                <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-600 rounded-xl flex items-center justify-center shadow-lg shadow-purple-500/25">
                  <DollarSign className="h-6 w-6 text-white drop-shadow-lg" />
                </div>
                <div>
                  <h1 className="text-4xl font-black bg-gradient-to-r from-purple-600 via-pink-600 to-rose-600 bg-clip-text text-transparent drop-shadow-sm">
                    Commission Reports
                  </h1>
                  <p className="text-gray-600 mt-1">Track your commission earnings and payouts</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <button className="group flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm border border-purple-200/50 rounded-lg hover:bg-white/90 hover:border-purple-300/70 transition-all duration-300 shadow-lg hover:shadow-xl">
                  <Download className="h-4 w-4 mr-2 text-purple-600 group-hover:text-pink-600 transition-colors" />
                  <span className="font-semibold text-gray-700 group-hover:text-gray-800">
                    Export
                  </span>
                </button>
                <button className="group flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm border border-purple-200/50 rounded-lg hover:bg-white/90 hover:border-purple-300/70 transition-all duration-300 shadow-lg hover:shadow-xl">
                  <RefreshCw className="h-4 w-4 mr-2 text-purple-600 group-hover:text-pink-600 transition-colors" />
                  <span className="font-semibold text-gray-700 group-hover:text-gray-800">
                    Refresh
                  </span>
                </button>
              </div>
            </div>

            {/* Period and Status Selectors */}
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-purple-600" />
                <span className="text-sm font-medium text-gray-700">Period:</span>
                <div className="flex bg-white/80 backdrop-blur-sm rounded-lg border border-purple-200/50 p-1">
                  {periods.map((period) => (
                    <button
                      key={period.value}
                      onClick={() => setSelectedPeriod(period.value)}
                      className={`px-4 py-2 text-sm font-bold rounded-md transition-all duration-200 ${
                        selectedPeriod === period.value
                          ? 'bg-gradient-to-r from-purple-400 to-pink-500 text-white shadow-lg'
                          : 'text-gray-600 hover:text-purple-600 hover:bg-purple-50/50'
                      }`}
                    >
                      {period.label}
                    </button>
                  ))}
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-purple-600" />
                <span className="text-sm font-medium text-gray-700">Status:</span>
                <div className="flex bg-white/80 backdrop-blur-sm rounded-lg border border-purple-200/50 p-1">
                  {statuses.map((status) => (
                    <button
                      key={status.value}
                      onClick={() => setSelectedStatus(status.value)}
                      className={`px-3 py-1 text-sm font-bold rounded-md transition-all duration-200 ${
                        selectedStatus === status.value
                          ? 'bg-gradient-to-r from-purple-400 to-pink-500 text-white shadow-lg'
                          : 'text-gray-600 hover:text-purple-600 hover:bg-purple-50/50'
                      }`}
                    >
                      {status.label} ({status.count})
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Commission Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
          {commissionMetrics.map((metric, index) => (
            <div key={metric.label} className="relative group">
              <div
                className={`absolute inset-0 bg-gradient-to-br ${metric.color}/20 to-${metric.color.split('-')[1]}-500/20 backdrop-blur-xl rounded-2xl border border-${metric.color.split('-')[1]}-200/50 shadow-xl group-hover:shadow-2xl transition-all duration-300`}
              />
              <div className="relative p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">
                      {metric.label}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">{metric.description}</p>
                  </div>
                  <div
                    className={`relative w-12 h-12 bg-gradient-to-br ${metric.color} rounded-xl flex items-center justify-center shadow-lg shadow-${metric.color.split('-')[1]}-500/25 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <metric.icon className="h-6 w-6 text-white drop-shadow-lg" />
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full animate-pulse" />
                  </div>
                </div>
                <div className="text-3xl font-black text-gray-800 mb-2">{metric.value}</div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {metric.trend === 'up' ? (
                      <TrendingUp className="h-4 w-4 text-emerald-600 mr-2" />
                    ) : (
                      <TrendingDown className="h-4 w-4 text-red-600 mr-2" />
                    )}
                    <span
                      className={`text-sm font-bold ${metric.trend === 'up' ? 'text-emerald-600' : 'text-red-600'}`}
                    >
                      {metric.change}
                    </span>
                  </div>
                  <Sparkles className="h-4 w-4 text-amber-500 animate-pulse" />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Commission Breakdown and Payout History */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Commission Breakdown */}
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/10 via-green-500/10 to-teal-500/10 backdrop-blur-xl rounded-2xl border border-emerald-200/30 shadow-xl" />
            <div className="relative p-6 lg:p-8">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-emerald-400 to-green-600 rounded-xl flex items-center justify-center shadow-lg shadow-emerald-500/25">
                  <Receipt className="h-5 w-5 text-white drop-shadow-lg" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-800">Commission Breakdown</h3>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse" />
                    <span className="text-xs text-emerald-600 font-medium">This Month</span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                {commissionBreakdown.map((item, index) => (
                  <div key={item.category} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`w-4 h-4 bg-gradient-to-br ${item.color} rounded-full`} />
                        <span className="font-semibold text-gray-800">{item.category}</span>
                      </div>
                      <div className="text-right">
                        <span className="font-bold text-gray-800">
                          ${item.commission.toFixed(2)}
                        </span>
                        <p className="text-xs text-gray-600">{item.rate}% rate</p>
                      </div>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                      <div
                        className={`h-full bg-gradient-to-r ${item.color} rounded-full transition-all duration-1000 ease-out`}
                        style={{ width: `${(item.commission / 5000) * 100}%` }}
                      />
                    </div>
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <span>Revenue: ${item.revenue.toFixed(2)}</span>
                      <span>{item.orders} orders</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Payout History */}
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-indigo-500/10 to-purple-500/10 backdrop-blur-xl rounded-2xl border border-blue-200/30 shadow-xl" />
            <div className="relative p-6 lg:p-8">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25">
                    <CreditCard className="h-5 w-5 text-white drop-shadow-lg" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-800">Payout History</h3>
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse" />
                      <span className="text-xs text-emerald-600 font-medium">Recent Payments</span>
                    </div>
                  </div>
                </div>
                <button className="text-sm text-blue-600 hover:text-indigo-600 font-medium">
                  View All
                </button>
              </div>

              <div className="space-y-3">
                {filteredPayouts.map((payout, index) => (
                  <div
                    key={payout.id}
                    className="flex items-center justify-between p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-blue-100/50 hover:bg-white/80 transition-colors duration-200"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                        {payout.id.slice(-3)}
                      </div>
                      <div>
                        <p className="font-semibold text-gray-800">{payout.id}</p>
                        <p className="text-sm text-gray-600">{payout.description}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-gray-800">${payout.amount.toFixed(2)}</p>
                      <div className="flex items-center space-x-2">
                        <span
                          className={`px-2 py-1 text-xs font-bold rounded-full ${
                            payout.status === 'paid'
                              ? 'bg-gradient-to-r from-emerald-400/20 to-green-500/20 text-emerald-800 border border-emerald-200/50'
                              : payout.status === 'pending'
                                ? 'bg-gradient-to-r from-amber-400/20 to-orange-500/20 text-amber-800 border border-amber-200/50'
                                : 'bg-gradient-to-r from-red-400/20 to-red-500/20 text-red-800 border border-red-200/50'
                          }`}
                        >
                          {payout.status.toUpperCase()}
                        </span>
                        <span className="text-xs text-gray-500">{payout.date}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Recent Transactions */}
        <div className="relative group">
          <div className="absolute inset-0 bg-gradient-to-br from-gray-50/80 to-purple-50/60 backdrop-blur-xl rounded-2xl border border-purple-200/30 shadow-xl" />
          <div className="relative p-6 lg:p-8">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-purple-400 to-pink-600 rounded-xl flex items-center justify-center shadow-lg shadow-purple-500/25">
                <Banknote className="h-5 w-5 text-white drop-shadow-lg" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-800">Recent Transactions</h3>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse" />
                  <span className="text-xs text-emerald-600 font-medium">Live Updates</span>
                </div>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-purple-200/50">
                    <th className="text-left py-3 px-4 font-bold text-gray-700">Transaction</th>
                    <th className="text-left py-3 px-4 font-bold text-gray-700">Order</th>
                    <th className="text-left py-3 px-4 font-bold text-gray-700">Customer</th>
                    <th className="text-left py-3 px-4 font-bold text-gray-700">Amount</th>
                    <th className="text-left py-3 px-4 font-bold text-gray-700">Commission</th>
                    <th className="text-left py-3 px-4 font-bold text-gray-700">Date</th>
                    <th className="text-left py-3 px-4 font-bold text-gray-700">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {recentTransactions.map((transaction) => (
                    <tr
                      key={transaction.id}
                      className="border-b border-purple-100/50 hover:bg-purple-50/30 transition-colors duration-200"
                    >
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-2">
                          <div className="w-8 h-8 bg-gradient-to-br from-purple-400 to-pink-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                            {transaction.id.slice(-2)}
                          </div>
                          <span className="font-semibold text-gray-800">{transaction.id}</span>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <span className="font-bold text-gray-800">{transaction.orderId}</span>
                      </td>
                      <td className="py-4 px-4">
                        <span className="font-bold text-gray-800">{transaction.customer}</span>
                      </td>
                      <td className="py-4 px-4">
                        <span className="font-bold text-gray-800">
                          ${transaction.amount.toFixed(2)}
                        </span>
                      </td>
                      <td className="py-4 px-4">
                        <span className="font-bold text-gray-800">
                          ${transaction.commission.toFixed(2)}
                        </span>
                      </td>
                      <td className="py-4 px-4">
                        <span className="text-sm text-gray-600">{transaction.date}</span>
                      </td>
                      <td className="py-4 px-4">
                        <span
                          className={`px-3 py-1 text-xs font-bold rounded-full ${
                            transaction.status === 'completed'
                              ? 'bg-gradient-to-r from-emerald-400/20 to-green-500/20 text-emerald-800 border border-emerald-200/50'
                              : 'bg-gradient-to-r from-amber-400/20 to-orange-500/20 text-amber-800 border border-amber-200/50'
                          }`}
                        >
                          {transaction.status.toUpperCase()}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}

export default CommissionReportsPageClient
