'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { Layout } from '@/components/Merchant/Layout'
import {
  ShoppingBag,
  Clock,
  CheckCircle,
  XCircle,
  Bell,
  Printer,
  Eye,
  Filter,
  Search,
  RefreshCw,
  Star,
  Sparkles,
  TrendingUp,
  Users,
  DollarSign,
  Calendar,
  MapPin,
  Phone,
  ChefHat,
  Timer,
  AlertCircle,
  Check,
  X,
  Zap,
  Crown,
} from 'lucide-react'

// TypeScript interfaces for order management
interface OrderItem {
  id: string
  name: string
  quantity: number
  price: number
  specialInstructions?: string
  image?: string
}

interface Customer {
  id: string
  name: string
  phone: string
  email?: string
  address?: string
  isVip?: boolean
}

interface Order {
  id: string
  orderNumber: string
  customer: Customer
  items: OrderItem[]
  totalAmount: number
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  orderType: 'delivery' | 'pickup' | 'dine_in'
  createdAt: Date
  estimatedTime?: number
  specialInstructions?: string
  paymentStatus: 'paid' | 'pending' | 'failed'
  deliveryAddress?: string
}

// Mock data for demonstration
const generateMockOrders = (): Order[] => {
  const customers: Customer[] = [
    {
      id: '1',
      name: '<PERSON>',
      phone: '+1234567890',
      email: '<EMAIL>',
      address: '123 Main St',
      isVip: true,
    },
    {
      id: '2',
      name: '<PERSON>',
      phone: '+0987654321',
      email: '<EMAIL>',
      address: '456 Oak Ave',
    },
    {
      id: '3',
      name: 'Mike Johnson',
      phone: '+1122334455',
      email: '<EMAIL>',
      address: '789 Pine Rd',
      isVip: true,
    },
    {
      id: '4',
      name: 'Sarah Wilson',
      phone: '+5566778899',
      email: '<EMAIL>',
      address: '321 Elm St',
    },
  ]

  const mockItems: OrderItem[] = [
    { id: '1', name: 'Margherita Pizza', quantity: 2, price: 18.99 },
    { id: '2', name: 'Caesar Salad', quantity: 1, price: 12.5 },
    {
      id: '3',
      name: 'Grilled Chicken',
      quantity: 1,
      price: 22.99,
      specialInstructions: 'Medium rare',
    },
    { id: '4', name: 'Pasta Carbonara', quantity: 1, price: 16.99 },
    { id: '5', name: 'Chocolate Cake', quantity: 2, price: 8.99 },
  ]

  return [
    {
      id: '1',
      orderNumber: 'ORD-001',
      customer: customers[0],
      items: [mockItems[0], mockItems[1]],
      totalAmount: 50.48,
      status: 'pending',
      orderType: 'delivery',
      createdAt: new Date(Date.now() - 5 * 60000), // 5 minutes ago
      estimatedTime: 30,
      paymentStatus: 'paid',
      deliveryAddress: '123 Main St, City, State 12345',
    },
    {
      id: '2',
      orderNumber: 'ORD-002',
      customer: customers[1],
      items: [mockItems[2], mockItems[3]],
      totalAmount: 39.98,
      status: 'in_progress',
      orderType: 'pickup',
      createdAt: new Date(Date.now() - 15 * 60000), // 15 minutes ago
      estimatedTime: 20,
      paymentStatus: 'paid',
    },
    {
      id: '3',
      orderNumber: 'ORD-003',
      customer: customers[2],
      items: [mockItems[0], mockItems[4]],
      totalAmount: 36.97,
      status: 'completed',
      orderType: 'dine_in',
      createdAt: new Date(Date.now() - 45 * 60000), // 45 minutes ago
      paymentStatus: 'paid',
    },
    {
      id: '4',
      orderNumber: 'ORD-004',
      customer: customers[3],
      items: [mockItems[1], mockItems[3], mockItems[4]],
      totalAmount: 38.48,
      status: 'cancelled',
      orderType: 'delivery',
      createdAt: new Date(Date.now() - 60 * 60000), // 1 hour ago
      paymentStatus: 'failed',
      deliveryAddress: '321 Elm St, City, State 54321',
    },
  ]
}

const OrdersPageClient = () => {
  const [orders, setOrders] = useState<Order[]>([])
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([])
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)
  const [showOrderDetails, setShowOrderDetails] = useState(false)
  const [notifications, setNotifications] = useState<string[]>([])

  // Initialize orders and simulate real-time updates
  useEffect(() => {
    const mockOrders = generateMockOrders()
    setOrders(mockOrders)
    setFilteredOrders(mockOrders)

    // Simulate new order notifications every 30 seconds
    const interval = setInterval(() => {
      const newNotification = `New order #ORD-${String(Date.now()).slice(-3)} received!`
      setNotifications((prev) => [newNotification, ...prev.slice(0, 4)]) // Keep last 5 notifications
    }, 30000)

    return () => clearInterval(interval)
  }, [])

  // Filter orders based on status and search term
  useEffect(() => {
    let filtered = orders

    if (selectedStatus !== 'all') {
      filtered = filtered.filter((order) => order.status === selectedStatus)
    }

    if (searchTerm) {
      filtered = filtered.filter(
        (order) =>
          order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
          order.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          order.customer.phone.includes(searchTerm),
      )
    }

    setFilteredOrders(filtered)
  }, [orders, selectedStatus, searchTerm])

  const handleStatusChange = useCallback((orderId: string, newStatus: Order['status']) => {
    setOrders((prevOrders) =>
      prevOrders.map((order) => (order.id === orderId ? { ...order, status: newStatus } : order)),
    )
  }, [])

  const handlePrintReceipt = useCallback((order: Order) => {
    // Create a print window with receipt content
    const printWindow = window.open('', '_blank')
    if (printWindow) {
      const receiptContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Receipt - ${order.orderNumber}</title>
          <style>
            body { font-family: monospace; padding: 20px; max-width: 300px; }
            .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; }
            .order-info { margin: 15px 0; }
            .items { margin: 15px 0; }
            .item { display: flex; justify-content: space-between; margin: 5px 0; }
            .total { border-top: 2px solid #000; padding-top: 10px; font-weight: bold; }
          </style>
        </head>
        <body>
          <div class="header">
            <h2>YoFood Restaurant</h2>
            <p>Receipt #${order.orderNumber}</p>
          </div>
          <div class="order-info">
            <p><strong>Customer:</strong> ${order.customer.name}</p>
            <p><strong>Phone:</strong> ${order.customer.phone}</p>
            <p><strong>Type:</strong> ${order.orderType.replace('_', ' ').toUpperCase()}</p>
            <p><strong>Date:</strong> ${order.createdAt.toLocaleString()}</p>
          </div>
          <div class="items">
            <h3>Items:</h3>
            ${order.items
              .map(
                (item) => `
              <div class="item">
                <span>${item.quantity}x ${item.name}</span>
                <span>$${(item.price * item.quantity).toFixed(2)}</span>
              </div>
            `,
              )
              .join('')}
          </div>
          <div class="total">
            <div class="item">
              <span>TOTAL:</span>
              <span>$${order.totalAmount.toFixed(2)}</span>
            </div>
          </div>
        </body>
        </html>
      `
      printWindow.document.write(receiptContent)
      printWindow.document.close()
      printWindow.print()
    }
  }, [])

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return 'from-amber-400/20 to-yellow-500/20 text-amber-800 border-amber-200/50'
      case 'in_progress':
        return 'from-blue-400/20 to-indigo-500/20 text-blue-800 border-blue-200/50'
      case 'completed':
        return 'from-emerald-400/20 to-green-500/20 text-emerald-800 border-emerald-200/50'
      case 'cancelled':
        return 'from-red-400/20 to-red-500/20 text-red-800 border-red-200/50'
      default:
        return 'from-gray-400/20 to-gray-500/20 text-gray-800 border-gray-200/50'
    }
  }

  const getStatusIcon = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />
      case 'in_progress':
        return <ChefHat className="h-4 w-4" />
      case 'completed':
        return <CheckCircle className="h-4 w-4" />
      case 'cancelled':
        return <XCircle className="h-4 w-4" />
      default:
        return <AlertCircle className="h-4 w-4" />
    }
  }

  const orderStats = {
    total: orders.length,
    pending: orders.filter((o) => o.status === 'pending').length,
    inProgress: orders.filter((o) => o.status === 'in_progress').length,
    completed: orders.filter((o) => o.status === 'completed').length,
    totalRevenue: orders
      .filter((o) => o.status === 'completed')
      .reduce((sum, o) => sum + o.totalAmount, 0),
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-amber-500/10 via-orange-500/10 to-red-500/10 backdrop-blur-xl rounded-2xl border border-amber-200/30 shadow-xl" />
          <div className="relative p-6 lg:p-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-amber-400 via-orange-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25">
                  <ShoppingBag className="h-6 w-6 text-white drop-shadow-lg" />
                </div>
                <div>
                  <h1 className="text-4xl font-black bg-gradient-to-r from-amber-600 via-orange-600 to-red-600 bg-clip-text text-transparent drop-shadow-sm">
                    Order Management
                  </h1>
                  <div className="flex items-center space-x-1 mt-1">
                    <Star className="h-3 w-3 text-amber-500 animate-pulse" />
                    <span className="text-xs text-amber-600 font-medium">Live Order Feed</span>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <button
                  onClick={() => window.location.reload()}
                  className="group flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl hover:bg-white/90 hover:border-amber-300/70 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  <RefreshCw className="h-4 w-4 mr-2 text-amber-600 group-hover:text-orange-600 group-hover:rotate-180 transition-all duration-300" />
                  <span className="font-semibold text-gray-700 group-hover:text-gray-800">
                    Refresh
                  </span>
                </button>

                {notifications.length > 0 && (
                  <div className="relative">
                    <Bell className="h-6 w-6 text-amber-600 animate-pulse" />
                    <span className="absolute -top-1 -right-1 h-4 w-4 bg-gradient-to-br from-red-500 to-red-600 text-white text-xs rounded-full flex items-center justify-center font-bold shadow-lg shadow-red-500/30 animate-pulse border-2 border-white">
                      {notifications.length}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Order Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-indigo-600/20 backdrop-blur-xl rounded-2xl border border-blue-500/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
            <div className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-bold text-blue-600 uppercase tracking-wide">
                    Total Orders
                  </p>
                  <div className="flex items-center mt-1">
                    <Star className="h-3 w-3 text-blue-500 mr-1" />
                    <span className="text-xs text-blue-600 font-medium">Today</span>
                  </div>
                </div>
                <div className="relative w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25 group-hover:scale-110 transition-transform duration-300">
                  <ShoppingBag className="h-6 w-6 text-white drop-shadow-lg" />
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full animate-pulse" />
                </div>
              </div>
              <div className="text-3xl font-black text-blue-600 mb-2">{orderStats.total}</div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <TrendingUp className="h-4 w-4 text-blue-500 mr-2" />
                  <span className="text-sm text-blue-600 font-bold">+12%</span>
                </div>
                <Sparkles className="h-4 w-4 text-blue-500 animate-pulse" />
              </div>
            </div>
          </div>

          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-amber-500/20 to-yellow-600/20 backdrop-blur-xl rounded-2xl border border-amber-500/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
            <div className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-bold text-amber-600 uppercase tracking-wide">
                    Pending
                  </p>
                  <div className="flex items-center mt-1">
                    <Clock className="h-3 w-3 text-amber-500 mr-1 animate-pulse" />
                    <span className="text-xs text-amber-600 font-medium">Urgent</span>
                  </div>
                </div>
                <div className="relative w-12 h-12 bg-gradient-to-br from-amber-500 to-yellow-600 rounded-xl flex items-center justify-center shadow-lg shadow-amber-500/25 group-hover:scale-110 transition-transform duration-300">
                  <Timer className="h-6 w-6 text-white drop-shadow-lg" />
                </div>
              </div>
              <div className="text-3xl font-black text-amber-600 mb-2">{orderStats.pending}</div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <AlertCircle className="h-4 w-4 text-amber-500 mr-2" />
                  <span className="text-sm text-amber-600 font-bold">Action Needed</span>
                </div>
                <Zap className="h-4 w-4 text-amber-500 animate-pulse" />
              </div>
            </div>
          </div>

          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-indigo-500/20 backdrop-blur-xl rounded-2xl border border-blue-400/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
            <div className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-bold text-blue-600 uppercase tracking-wide">
                    In Progress
                  </p>
                  <div className="flex items-center mt-1">
                    <ChefHat className="h-3 w-3 text-blue-500 mr-1" />
                    <span className="text-xs text-blue-600 font-medium">Cooking</span>
                  </div>
                </div>
                <div className="relative w-12 h-12 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-xl flex items-center justify-center shadow-lg shadow-blue-400/25 group-hover:scale-110 transition-transform duration-300">
                  <ChefHat className="h-6 w-6 text-white drop-shadow-lg" />
                </div>
              </div>
              <div className="text-3xl font-black text-blue-600 mb-2">{orderStats.inProgress}</div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Timer className="h-4 w-4 text-blue-500 mr-2" />
                  <span className="text-sm text-blue-600 font-bold">Active</span>
                </div>
                <Sparkles className="h-4 w-4 text-blue-500 animate-pulse" />
              </div>
            </div>
          </div>

          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-400/20 to-green-600/20 backdrop-blur-xl rounded-2xl border border-emerald-400/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
            <div className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-bold text-emerald-600 uppercase tracking-wide">
                    Completed
                  </p>
                  <div className="flex items-center mt-1">
                    <CheckCircle className="h-3 w-3 text-emerald-500 mr-1" />
                    <span className="text-xs text-emerald-600 font-medium">Success</span>
                  </div>
                </div>
                <div className="relative w-12 h-12 bg-gradient-to-br from-emerald-400 to-green-600 rounded-xl flex items-center justify-center shadow-lg shadow-emerald-400/25 group-hover:scale-110 transition-transform duration-300">
                  <CheckCircle className="h-6 w-6 text-white drop-shadow-lg" />
                </div>
              </div>
              <div className="text-3xl font-black text-emerald-600 mb-2">
                {orderStats.completed}
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <TrendingUp className="h-4 w-4 text-emerald-500 mr-2" />
                  <span className="text-sm text-emerald-600 font-bold">+8%</span>
                </div>
                <Sparkles className="h-4 w-4 text-emerald-500 animate-pulse" />
              </div>
            </div>
          </div>

          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-green-500/20 to-emerald-600/20 backdrop-blur-xl rounded-2xl border border-green-500/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
            <div className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-bold text-green-600 uppercase tracking-wide">
                    Revenue
                  </p>
                  <div className="flex items-center mt-1">
                    <DollarSign className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600 font-medium">Today</span>
                  </div>
                </div>
                <div className="relative w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg shadow-green-500/25 group-hover:scale-110 transition-transform duration-300">
                  <DollarSign className="h-6 w-6 text-white drop-shadow-lg" />
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full animate-pulse" />
                </div>
              </div>
              <div className="text-3xl font-black text-green-600 mb-2">
                ${orderStats.totalRevenue.toFixed(0)}
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <TrendingUp className="h-4 w-4 text-green-500 mr-2" />
                  <span className="text-sm text-green-600 font-bold">+15%</span>
                </div>
                <Crown className="h-4 w-4 text-green-500 animate-pulse" />
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-white/80 via-amber-50/80 to-orange-50/80 backdrop-blur-xl rounded-2xl border border-amber-200/30 shadow-xl" />
          <div className="relative p-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              <div className="flex flex-wrap items-center space-x-2">
                <Filter className="h-5 w-5 text-amber-600" />
                <span className="text-sm font-bold text-gray-700 mr-4">Filter by Status:</span>
                {['all', 'pending', 'in_progress', 'completed', 'cancelled'].map((status) => (
                  <button
                    key={status}
                    onClick={() => setSelectedStatus(status)}
                    className={`px-4 py-2 rounded-xl text-sm font-bold transition-all duration-300 transform hover:scale-105 ${
                      selectedStatus === status
                        ? 'bg-gradient-to-r from-amber-400 to-orange-500 text-white shadow-lg shadow-amber-500/25'
                        : 'bg-white/80 text-gray-600 hover:bg-white/90 hover:text-gray-800 border border-amber-200/50'
                    }`}
                  >
                    {status.replace('_', ' ').replace(/\b\w/g, (l) => l.toUpperCase())}
                  </button>
                ))}
              </div>

              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-orange-500/20 rounded-xl blur-sm group-focus-within:blur-md group-focus-within:from-amber-400/30 group-focus-within:to-orange-500/30 transition-all duration-300" />
                <div className="relative flex items-center">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-amber-600 group-focus-within:text-orange-600 transition-colors z-10" />
                  <input
                    type="text"
                    placeholder="Search orders, customers, phone..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-12 pr-4 py-3 w-full lg:w-80 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg focus:shadow-xl"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Orders List */}
        <div className="space-y-4">
          {filteredOrders.length === 0 ? (
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-gray-100/80 to-gray-200/80 backdrop-blur-xl rounded-2xl border border-gray-200/50 shadow-xl" />
              <div className="relative p-12 text-center">
                <ShoppingBag className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-600 mb-2">No Orders Found</h3>
                <p className="text-gray-500">No orders match your current filter criteria.</p>
              </div>
            </div>
          ) : (
            filteredOrders.map((order, index) => (
              <div
                key={order.id}
                className="relative group animate-fade-in-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/90 via-amber-50/90 to-orange-50/90 backdrop-blur-xl rounded-2xl border border-amber-200/30 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
                <div className="relative p-6">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                    {/* Order Info */}
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg font-black text-gray-800">
                            #{order.orderNumber}
                          </span>
                          {order.customer.isVip && (
                            <div className="flex items-center px-2 py-1 bg-gradient-to-r from-yellow-400/20 to-amber-500/20 rounded-full border border-yellow-400/50">
                              <Crown className="h-3 w-3 text-yellow-600 mr-1" />
                              <span className="text-xs font-bold text-yellow-700">VIP</span>
                            </div>
                          )}
                        </div>
                        <span
                          className={`px-3 py-1 text-xs font-bold rounded-full border shadow-lg ${getStatusColor(order.status)}`}
                        >
                          <div className="flex items-center space-x-1">
                            {getStatusIcon(order.status)}
                            <span>{order.status.replace('_', ' ').toUpperCase()}</span>
                          </div>
                        </span>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div className="flex items-center space-x-2">
                          <Users className="h-4 w-4 text-amber-600" />
                          <div>
                            <p className="text-sm font-bold text-gray-800">{order.customer.name}</p>
                            <p className="text-xs text-gray-600">{order.customer.phone}</p>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <DollarSign className="h-4 w-4 text-green-600" />
                          <div>
                            <p className="text-sm font-bold text-gray-800">
                              ${order.totalAmount.toFixed(2)}
                            </p>
                            <p className="text-xs text-gray-600">{order.items.length} items</p>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-blue-600" />
                          <div>
                            <p className="text-sm font-bold text-gray-800">
                              {order.createdAt.toLocaleTimeString()}
                            </p>
                            <p className="text-xs text-gray-600">
                              {order.orderType.replace('_', ' ')}
                            </p>
                          </div>
                        </div>

                        {order.estimatedTime && (
                          <div className="flex items-center space-x-2">
                            <Timer className="h-4 w-4 text-orange-600" />
                            <div>
                              <p className="text-sm font-bold text-gray-800">
                                {order.estimatedTime} min
                              </p>
                              <p className="text-xs text-gray-600">Estimated</p>
                            </div>
                          </div>
                        )}
                      </div>

                      {order.deliveryAddress && (
                        <div className="flex items-center space-x-2 mt-3">
                          <MapPin className="h-4 w-4 text-red-600" />
                          <p className="text-sm text-gray-600">{order.deliveryAddress}</p>
                        </div>
                      )}
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => {
                          setSelectedOrder(order)
                          setShowOrderDetails(true)
                        }}
                        className="group flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl hover:bg-white/90 hover:border-amber-300/70 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
                      >
                        <Eye className="h-4 w-4 mr-2 text-amber-600 group-hover:text-orange-600 transition-colors" />
                        <span className="font-semibold text-gray-700 group-hover:text-gray-800">
                          View
                        </span>
                      </button>

                      <button
                        onClick={() => handlePrintReceipt(order)}
                        className="group flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm border border-blue-200/50 rounded-xl hover:bg-white/90 hover:border-blue-300/70 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
                      >
                        <Printer className="h-4 w-4 mr-2 text-blue-600 group-hover:text-blue-700 transition-colors" />
                        <span className="font-semibold text-gray-700 group-hover:text-gray-800">
                          Print
                        </span>
                      </button>

                      {order.status === 'pending' && (
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleStatusChange(order.id, 'in_progress')}
                            className="group flex items-center px-4 py-2 bg-gradient-to-r from-emerald-400 via-green-500 to-emerald-600 text-white rounded-xl hover:from-emerald-500 hover:via-green-600 hover:to-emerald-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-green-400/50"
                          >
                            <Check className="h-4 w-4 mr-2 group-hover:rotate-12 transition-transform duration-300" />
                            <span className="font-bold">Accept</span>
                          </button>
                          <button
                            onClick={() => handleStatusChange(order.id, 'cancelled')}
                            className="group flex items-center px-4 py-2 bg-gradient-to-r from-red-400 via-red-500 to-red-600 text-white rounded-xl hover:from-red-500 hover:via-red-600 hover:to-red-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-red-400/50"
                          >
                            <X className="h-4 w-4 mr-2 group-hover:rotate-90 transition-transform duration-300" />
                            <span className="font-bold">Reject</span>
                          </button>
                        </div>
                      )}

                      {order.status === 'in_progress' && (
                        <button
                          onClick={() => handleStatusChange(order.id, 'completed')}
                          className="group flex items-center px-6 py-3 bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 text-white rounded-xl hover:from-amber-500 hover:via-orange-600 hover:to-red-600 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-orange-400/50"
                        >
                          <CheckCircle className="h-4 w-4 mr-2 group-hover:rotate-90 transition-transform duration-300" />
                          <span className="font-bold">Complete</span>
                          <Zap className="h-4 w-4 ml-2 animate-pulse" />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Order Details Modal */}
        {showOrderDetails && selectedOrder && (
          <div className="fixed inset-0 bg-gradient-to-br from-black/60 via-amber-900/40 to-orange-900/30 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="relative max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-white/95 via-amber-50/95 to-orange-50/95 backdrop-blur-xl rounded-2xl border border-amber-200/50 shadow-2xl" />
              <div className="relative p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-amber-400 via-orange-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25">
                      <ShoppingBag className="h-5 w-5 text-white drop-shadow-lg" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-black text-gray-800">Order Details</h3>
                      <p className="text-sm text-gray-600">#{selectedOrder.orderNumber}</p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowOrderDetails(false)}
                    className="p-2 text-gray-500 hover:text-gray-700 hover:bg-white/50 rounded-xl transition-all duration-300"
                  >
                    <X className="h-6 w-6" />
                  </button>
                </div>

                <div className="space-y-6">
                  {/* Customer Info */}
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-400/10 to-indigo-500/10 backdrop-blur-xl rounded-xl border border-blue-200/30 shadow-lg" />
                    <div className="relative p-4">
                      <h4 className="text-lg font-bold text-gray-800 mb-3">Customer Information</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="flex items-center space-x-2">
                          <Users className="h-4 w-4 text-blue-600" />
                          <span className="font-semibold text-gray-700">
                            {selectedOrder.customer.name}
                          </span>
                          {selectedOrder.customer.isVip && (
                            <Crown className="h-4 w-4 text-yellow-600" />
                          )}
                        </div>
                        <div className="flex items-center space-x-2">
                          <Phone className="h-4 w-4 text-blue-600" />
                          <span className="text-gray-700">{selectedOrder.customer.phone}</span>
                        </div>
                        {selectedOrder.deliveryAddress && (
                          <div className="flex items-center space-x-2 md:col-span-2">
                            <MapPin className="h-4 w-4 text-red-600" />
                            <span className="text-gray-700">{selectedOrder.deliveryAddress}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Order Items */}
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-green-400/10 to-emerald-500/10 backdrop-blur-xl rounded-xl border border-green-200/30 shadow-lg" />
                    <div className="relative p-4">
                      <h4 className="text-lg font-bold text-gray-800 mb-3">Order Items</h4>
                      <div className="space-y-3">
                        {selectedOrder.items.map((item) => (
                          <div
                            key={item.id}
                            className="flex items-center justify-between p-3 bg-white/50 rounded-lg border border-green-200/30"
                          >
                            <div>
                              <p className="font-semibold text-gray-800">{item.name}</p>
                              <p className="text-sm text-gray-600">Quantity: {item.quantity}</p>
                              {item.specialInstructions && (
                                <p className="text-xs text-amber-600 font-medium">
                                  Note: {item.specialInstructions}
                                </p>
                              )}
                            </div>
                            <div className="text-right">
                              <p className="font-bold text-gray-800">
                                ${(item.price * item.quantity).toFixed(2)}
                              </p>
                              <p className="text-sm text-gray-600">${item.price.toFixed(2)} each</p>
                            </div>
                          </div>
                        ))}
                      </div>
                      <div className="mt-4 pt-4 border-t border-green-200/50">
                        <div className="flex items-center justify-between">
                          <span className="text-lg font-black text-gray-800">Total:</span>
                          <span className="text-xl font-black text-green-600">
                            ${selectedOrder.totalAmount.toFixed(2)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() => handlePrintReceipt(selectedOrder)}
                      className="group flex items-center px-6 py-3 bg-gradient-to-r from-blue-400 via-blue-500 to-indigo-600 text-white rounded-xl hover:from-blue-500 hover:via-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-blue-400/50"
                    >
                      <Printer className="h-4 w-4 mr-2 group-hover:rotate-12 transition-transform duration-300" />
                      <span className="font-bold">Print Receipt</span>
                    </button>

                    {selectedOrder.status === 'pending' && (
                      <>
                        <button
                          onClick={() => {
                            handleStatusChange(selectedOrder.id, 'in_progress')
                            setShowOrderDetails(false)
                          }}
                          className="group flex items-center px-6 py-3 bg-gradient-to-r from-emerald-400 via-green-500 to-emerald-600 text-white rounded-xl hover:from-emerald-500 hover:via-green-600 hover:to-emerald-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-green-400/50"
                        >
                          <Check className="h-4 w-4 mr-2 group-hover:rotate-12 transition-transform duration-300" />
                          <span className="font-bold">Accept Order</span>
                        </button>
                        <button
                          onClick={() => {
                            handleStatusChange(selectedOrder.id, 'cancelled')
                            setShowOrderDetails(false)
                          }}
                          className="group flex items-center px-6 py-3 bg-gradient-to-r from-red-400 via-red-500 to-red-600 text-white rounded-xl hover:from-red-500 hover:via-red-600 hover:to-red-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-red-400/50"
                        >
                          <X className="h-4 w-4 mr-2 group-hover:rotate-90 transition-transform duration-300" />
                          <span className="font-bold">Reject Order</span>
                        </button>
                      </>
                    )}

                    {selectedOrder.status === 'in_progress' && (
                      <button
                        onClick={() => {
                          handleStatusChange(selectedOrder.id, 'completed')
                          setShowOrderDetails(false)
                        }}
                        className="group flex items-center px-6 py-3 bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 text-white rounded-xl hover:from-amber-500 hover:via-orange-600 hover:to-red-600 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-orange-400/50"
                      >
                        <CheckCircle className="h-4 w-4 mr-2 group-hover:rotate-90 transition-transform duration-300" />
                        <span className="font-bold">Mark Complete</span>
                        <Zap className="h-4 w-4 ml-2 animate-pulse" />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Notifications Toast */}
        {notifications.length > 0 && (
          <div className="fixed top-4 right-4 space-y-2 z-40">
            {notifications.slice(0, 3).map((notification, index) => (
              <div
                key={index}
                className="relative animate-slide-in-right"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-amber-400/90 via-orange-500/90 to-red-500/90 backdrop-blur-xl rounded-xl border border-amber-200/50 shadow-2xl" />
                <div className="relative p-4 flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-white/20 to-white/10 rounded-lg flex items-center justify-center">
                    <Bell className="h-4 w-4 text-white animate-pulse" />
                  </div>
                  <div>
                    <p className="text-white font-bold text-sm">{notification}</p>
                    <p className="text-white/80 text-xs">Just now</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </Layout>
  )
}

export default OrdersPageClient
