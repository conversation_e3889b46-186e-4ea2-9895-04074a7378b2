'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  User,
  Mail,
  Phone,
  ArrowRight,
  Loader2,
  CheckCircle,
  UserPlus,
  ChefHat,
  Crown,
  Sparkles,
} from 'lucide-react'
import Link from 'next/link'

interface FormData {
  firstName: string
  lastName: string
  email: string
  phone: string
}

interface FormErrors {
  firstName?: string
  lastName?: string
  email?: string
  phone?: string
}

const MerchantRegisterPageClient = () => {
  const [step, setStep] = useState<'form' | 'confirmation'>('form')
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
  })
  const [errors, setErrors] = useState<FormErrors>({})
  const [isLoading, setIsLoading] = useState(false)
  const [isRegistered, setIsRegistered] = useState(false)

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required'
    } else if (formData.firstName.trim().length < 2) {
      newErrors.firstName = 'First name must be at least 2 characters'
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required'
    } else if (formData.lastName.trim().length < 2) {
      newErrors.lastName = 'Last name must be at least 2 characters'
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required'
    } else if (!phoneRegex.test(formData.phone.replace(/[\s\-\(\)]/g, ''))) {
      newErrors.phone = 'Please enter a valid phone number'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (field: keyof FormData) => (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setFormData((prev) => ({ ...prev, [field]: value }))

    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }))
    }
  }

  const formatPhoneNumber = (value: string) => {
    // Remove all non-digits except + sign
    const phoneNumber = value.replace(/[^\d\+]/g, '')

    // Basic US phone number formatting
    if (phoneNumber.length === 10) {
      return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`
    } else if (phoneNumber.length === 11 && phoneNumber.startsWith('1')) {
      return `+1 (${phoneNumber.slice(1, 4)}) ${phoneNumber.slice(4, 7)}-${phoneNumber.slice(7, 11)}`
    }

    return value
  }

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value.replace(/[^\d\s\-\(\)\+]/g, '')
    const formattedValue = formatPhoneNumber(rawValue)
    setFormData((prev) => ({ ...prev, phone: formattedValue }))

    if (errors.phone) {
      setErrors((prev) => ({ ...prev, phone: undefined }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 2000))

    setIsRegistered(true)
    setStep('confirmation')
    setIsLoading(false)
  }

  const resendConfirmation = async () => {
    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 1000))
    setIsRegistered(true)
    setIsLoading(false)
  }

  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-amber-50 via-white to-orange-50">
      {/* Dynamic Background */}
      <div className="absolute inset-0">
        {/* Primary Background Image */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat scale-105 transition-transform duration-[20s] ease-linear"
          style={{
            backgroundImage:
              'url("https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80")',
          }}
        />

        {/* Gradient Overlays */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/60 via-emerald-900/40 to-blue-900/60"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent"></div>

        {/* Animated Mesh Background */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-emerald-500/20 to-blue-500/20 animate-pulse"></div>
          <div className="absolute top-20 left-20 w-96 h-96 bg-emerald-400/10 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-32 right-20 w-80 h-80 bg-blue-400/10 rounded-full blur-3xl animate-float-delayed"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-radial from-teal-300/5 to-transparent rounded-full animate-spin-slow"></div>
        </div>
      </div>

      {/* Floating Particles */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-emerald-300 rounded-full animate-ping"></div>
        <div className="absolute top-3/4 left-3/4 w-1 h-1 bg-blue-300 rounded-full animate-ping delay-1000"></div>
        <div className="absolute top-1/2 left-1/6 w-1.5 h-1.5 bg-teal-300 rounded-full animate-ping delay-2000"></div>
        <div className="absolute top-1/6 right-1/4 w-2 h-2 bg-emerald-400 rounded-full animate-ping delay-500"></div>
      </div>

      <div className="relative z-20 min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          {/* Announcement Badge */}
          <div className="flex items-center px-6 py-3 bg-white/10 backdrop-blur-md border border-white/20 rounded-full text-sm font-medium mb-8 animate-fade-in-down text-white mx-auto w-fit">
            <Crown className="w-4 h-4 mr-2 text-emerald-400" />
            Join 1000+ successful restaurants
          </div>

          <Card className="shadow-2xl bg-white/10 backdrop-blur-md border border-white/20 overflow-hidden animate-fade-in-up">
            <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-white/5"></div>

            <CardHeader className="text-center pb-6 relative">
              {/* Hero Icon */}
              <div className="relative mx-auto mb-6">
                <div className="w-20 h-20 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-full flex items-center justify-center mx-auto shadow-2xl animate-fade-in-down">
                  {step === 'form' ? (
                    <UserPlus className="w-10 h-10 text-white" />
                  ) : (
                    <CheckCircle className="w-10 h-10 text-white" />
                  )}
                </div>
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-emerald-400 rounded-full flex items-center justify-center animate-bounce">
                  <Sparkles className="w-3 h-3 text-white" />
                </div>
              </div>

              <CardTitle className="text-3xl font-black text-white leading-tight animate-fade-in-up">
                {step === 'form' ? (
                  <>
                    <span className="block">Join Our</span>
                    <span className="block bg-gradient-to-r from-emerald-300 via-teal-300 to-blue-300 bg-clip-text text-transparent animate-gradient-x text-2xl">
                      Culinary Empire
                    </span>
                  </>
                ) : (
                  <>
                    <span className="block">Check Your</span>
                    <span className="block bg-gradient-to-r from-emerald-300 via-teal-300 to-blue-300 bg-clip-text text-transparent animate-gradient-x text-2xl">
                      Email
                    </span>
                  </>
                )}
              </CardTitle>

              <CardDescription className="text-gray-300 text-lg mt-4 animate-fade-in-up delay-200">
                {step === 'form'
                  ? 'Create your merchant account and start your digital transformation'
                  : `We've sent a confirmation link to ${formData.email}`}
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-6 relative">
              {step === 'form' ? (
                <form onSubmit={handleSubmit} className="space-y-5">
                  {/* First Name */}
                  <div className="space-y-2 animate-fade-in-up delay-300">
                    <Label htmlFor="firstName" className="text-sm font-semibold text-white">
                      First Name *
                    </Label>
                    <div className="relative group">
                      <User className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-emerald-400/70 group-focus-within:text-emerald-300 group-hover:text-emerald-300 transition-all duration-300" />
                      <Input
                        id="firstName"
                        type="text"
                        placeholder="John"
                        value={formData.firstName}
                        onChange={handleInputChange('firstName')}
                        className={`pl-12 h-12 bg-black/20 backdrop-blur-md border-2 text-white placeholder-gray-400 focus:bg-black/30 hover:bg-black/25 transition-all duration-300 rounded-xl shadow-lg ${
                          errors.firstName
                            ? 'border-red-400/60 focus:border-red-400 focus:ring-2 focus:ring-red-400/30'
                            : 'border-white/20 focus:border-emerald-500/50 focus:ring-2 focus:ring-emerald-500/30 hover:border-white/30'
                        }`}
                        required
                      />
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-emerald-500/5 to-blue-500/5 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                      <div
                        className={`absolute inset-0 rounded-xl border opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none ${
                          errors.firstName ? 'border-red-400/20' : 'border-emerald-500/20'
                        }`}
                      ></div>
                    </div>
                    {errors.firstName && (
                      <div className="flex items-center space-x-2 text-red-400 bg-red-500/10 backdrop-blur-sm border border-red-400/20 px-3 py-2 rounded-lg animate-fade-in-up">
                        <svg
                          className="w-4 h-4 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <span className="text-sm font-medium">{errors.firstName}</span>
                      </div>
                    )}
                  </div>

                  {/* Last Name */}
                  <div className="space-y-2 animate-fade-in-up delay-400">
                    <Label htmlFor="lastName" className="text-sm font-semibold text-white">
                      Last Name *
                    </Label>
                    <div className="relative group">
                      <User className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-emerald-400/70 group-focus-within:text-emerald-300 group-hover:text-emerald-300 transition-all duration-300" />
                      <Input
                        id="lastName"
                        type="text"
                        placeholder="Doe"
                        value={formData.lastName}
                        onChange={handleInputChange('lastName')}
                        className={`pl-12 h-12 bg-black/20 backdrop-blur-md border-2 text-white placeholder-gray-400 focus:bg-black/30 hover:bg-black/25 transition-all duration-300 rounded-xl shadow-lg ${
                          errors.lastName
                            ? 'border-red-400/60 focus:border-red-400 focus:ring-2 focus:ring-red-400/30'
                            : 'border-white/20 focus:border-emerald-500/50 focus:ring-2 focus:ring-emerald-500/30 hover:border-white/30'
                        }`}
                        required
                      />
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-emerald-500/5 to-blue-500/5 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                      <div
                        className={`absolute inset-0 rounded-xl border opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none ${
                          errors.lastName ? 'border-red-400/20' : 'border-emerald-500/20'
                        }`}
                      ></div>
                    </div>
                    {errors.lastName && (
                      <div className="flex items-center space-x-2 text-red-400 bg-red-500/10 backdrop-blur-sm border border-red-400/20 px-3 py-2 rounded-lg animate-fade-in-up">
                        <svg
                          className="w-4 h-4 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <span className="text-sm font-medium">{errors.lastName}</span>
                      </div>
                    )}
                  </div>

                  {/* Email */}
                  <div className="space-y-2 animate-fade-in-up delay-500">
                    <Label htmlFor="email" className="text-sm font-semibold text-white">
                      Email Address *
                    </Label>
                    <div className="relative group">
                      <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-emerald-400/70 group-focus-within:text-emerald-300 group-hover:text-emerald-300 transition-all duration-300" />
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={formData.email}
                        onChange={handleInputChange('email')}
                        className={`pl-12 h-12 bg-black/20 backdrop-blur-md border-2 text-white placeholder-gray-400 focus:bg-black/30 hover:bg-black/25 transition-all duration-300 rounded-xl shadow-lg ${
                          errors.email
                            ? 'border-red-400/60 focus:border-red-400 focus:ring-2 focus:ring-red-400/30'
                            : 'border-white/20 focus:border-emerald-500/50 focus:ring-2 focus:ring-emerald-500/30 hover:border-white/30'
                        }`}
                        required
                      />
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-emerald-500/5 to-blue-500/5 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                      <div
                        className={`absolute inset-0 rounded-xl border opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none ${
                          errors.email ? 'border-red-400/20' : 'border-emerald-500/20'
                        }`}
                      ></div>
                    </div>
                    {errors.email && (
                      <div className="flex items-center space-x-2 text-red-400 bg-red-500/10 backdrop-blur-sm border border-red-400/20 px-3 py-2 rounded-lg animate-fade-in-up">
                        <svg
                          className="w-4 h-4 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <span className="text-sm font-medium">{errors.email}</span>
                      </div>
                    )}
                  </div>

                  {/* Phone */}
                  <div className="space-y-2 animate-fade-in-up delay-600">
                    <Label htmlFor="phone" className="text-sm font-semibold text-white">
                      Phone Number *
                    </Label>
                    <div className="relative group">
                      <Phone className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-emerald-400/70 group-focus-within:text-emerald-300 group-hover:text-emerald-300 transition-all duration-300" />
                      <Input
                        id="phone"
                        type="tel"
                        placeholder="+****************"
                        value={formData.phone}
                        onChange={handlePhoneChange}
                        className={`pl-12 h-12 bg-black/20 backdrop-blur-md border-2 text-white placeholder-gray-400 focus:bg-black/30 hover:bg-black/25 transition-all duration-300 rounded-xl shadow-lg ${
                          errors.phone
                            ? 'border-red-400/60 focus:border-red-400 focus:ring-2 focus:ring-red-400/30'
                            : 'border-white/20 focus:border-emerald-500/50 focus:ring-2 focus:ring-emerald-500/30 hover:border-white/30'
                        }`}
                        required
                      />
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-emerald-500/5 to-blue-500/5 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                      <div
                        className={`absolute inset-0 rounded-xl border opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none ${
                          errors.phone ? 'border-red-400/20' : 'border-emerald-500/20'
                        }`}
                      ></div>
                    </div>
                    {errors.phone && (
                      <div className="flex items-center space-x-2 text-red-400 bg-red-500/10 backdrop-blur-sm border border-red-400/20 px-3 py-2 rounded-lg animate-fade-in-up">
                        <svg
                          className="w-4 h-4 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <span className="text-sm font-medium">{errors.phone}</span>
                      </div>
                    )}
                  </div>

                  <Button
                    type="submit"
                    className="w-full h-14 bg-gradient-to-r from-emerald-500 to-blue-500 hover:from-emerald-600 hover:to-blue-600 text-white font-bold text-lg rounded-xl transform hover:scale-105 hover:-translate-y-1 transition-all duration-300 shadow-2xl overflow-hidden group animate-fade-in-up delay-700"
                    disabled={isLoading}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative flex items-center justify-center">
                      {isLoading ? (
                        <>
                          <Loader2 className="w-6 h-6 mr-3 animate-spin" />
                          Creating Account...
                        </>
                      ) : (
                        <>
                          <ChefHat className="w-6 h-6 mr-3 group-hover:rotate-12 transition-transform duration-300" />
                          Create Account
                          <ArrowRight className="w-5 h-5 ml-3 group-hover:translate-x-2 transition-transform duration-300" />
                        </>
                      )}
                    </div>
                    <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-teal-400 to-blue-400 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
                  </Button>

                  <p className="text-xs text-gray-300 text-center animate-fade-in-up delay-800">
                    By creating an account, you agree to our{' '}
                    <a
                      href="#"
                      className="text-emerald-300 hover:text-emerald-200 underline transition-colors duration-200"
                    >
                      Terms of Service
                    </a>{' '}
                    and{' '}
                    <a
                      href="#"
                      className="text-emerald-300 hover:text-emerald-200 underline transition-colors duration-200"
                    >
                      Privacy Policy
                    </a>
                  </p>
                </form>
              ) : (
                <div className="space-y-6 text-center">
                  {isRegistered && (
                    <div className="flex items-center justify-center space-x-3 text-emerald-300 bg-emerald-500/20 backdrop-blur-sm border border-emerald-400/30 p-4 rounded-xl animate-fade-in-up">
                      <CheckCircle className="w-6 h-6" />
                      <span className="font-medium">Registration successful!</span>
                    </div>
                  )}

                  <div className="space-y-4 animate-fade-in-up delay-200">
                    <p className="text-gray-300 leading-relaxed">
                      We&apos;ve sent a confirmation email to verify your account. Please check your
                      inbox and click the confirmation link to complete your registration.
                    </p>

                    <div className="p-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl">
                      <p className="text-sm text-emerald-300 font-medium mb-1">Email sent to:</p>
                      <p className="text-white font-semibold">{formData.email}</p>
                    </div>

                    <Button
                      onClick={resendConfirmation}
                      className="w-full h-12 bg-white/10 backdrop-blur-md border border-emerald-300/50 text-emerald-300 hover:bg-emerald-500/20 hover:border-emerald-300 transition-all duration-300 rounded-xl"
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                          Resending...
                        </>
                      ) : (
                        'Resend Confirmation Email'
                      )}
                    </Button>

                    <button
                      onClick={() => {
                        setStep('form')
                        setIsRegistered(false)
                      }}
                      className="block w-full text-gray-300 hover:text-white transition-colors duration-200 text-sm"
                    >
                      ← Back to registration
                    </button>
                  </div>
                </div>
              )}

              {/* Trust Indicators */}
              <div className="flex justify-center items-center gap-6 pt-6 text-gray-300 text-sm border-t border-white/20 animate-fade-in-up delay-900">
                <div className="flex items-center">
                  <CheckCircle className="w-4 h-4 mr-2 text-emerald-400" />
                  Free forever
                </div>
                <div className="flex items-center">
                  <Crown className="w-4 h-4 mr-2 text-emerald-400" />
                  Premium features
                </div>
              </div>

              <div className="text-center pt-4 animate-fade-in-up delay-1000">
                <p className="text-gray-300">
                  Already have an account?{' '}
                  <Link
                    href="/merchant/login"
                    className="text-emerald-300 hover:text-emerald-200 font-semibold transition-colors duration-200 hover:underline"
                  >
                    Sign in here
                  </Link>
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Additional decorative elements */}
          <div className="absolute -z-10 top-4 left-4 w-32 h-32 bg-gradient-to-br from-emerald-500/20 to-blue-500/20 rounded-full blur-2xl animate-float"></div>
          <div className="absolute -z-10 bottom-4 right-4 w-40 h-40 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-2xl animate-float-delayed"></div>
        </div>
      </div>
    </div>
  )
}

export default MerchantRegisterPageClient
