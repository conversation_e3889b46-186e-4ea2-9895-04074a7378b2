'use client'

import React, { useState, useEffect } from 'react'
import { Layout } from '@/components/Merchant/Layout'
import {
  Users,
  UserPlus,
  Shield,
  Eye,
  Edit,
  Trash2,
  Search,
  Filter,
  Star,
  DollarSign,
  ShoppingBag,
  ChefHat,
  CreditCard,
  Crown,
  Zap,
  Sparkles,
  TrendingUp,
  Activity,
  Calendar,
  Phone,
  Award,
  UserCheck,
  Target,
  RefreshCw,
  Download,
  ChevronRight,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Settings,
  BarChart3,
} from 'lucide-react'

// TypeScript interfaces for staff management
interface StaffPermissions {
  canViewOrders: boolean
  canManageOrders: boolean
  canViewCustomers: boolean
  canManageCustomers: boolean
  canViewMenu: boolean
  canManageMenu: boolean
  canViewReports: boolean
  canManageStaff: boolean
  canViewAnalytics: boolean
  canManageSettings: boolean
  canProcessPayments: boolean
  canManageInventory: boolean
}

interface StaffMember {
  id: string
  name: string
  email: string
  phone: string
  role: 'waiter' | 'cashier' | 'kitchen' | 'manager' | 'admin'
  status: 'active' | 'inactive' | 'suspended'
  joinDate: Date
  lastActive: Date
  permissions: StaffPermissions
  totalOrders: number
  totalRevenue: number
  rating: number
  avatar?: string
  address?: string
  emergencyContact?: string
  shiftSchedule?: {
    monday: string
    tuesday: string
    wednesday: string
    thursday: string
    friday: string
    saturday: string
    sunday: string
  }
}

// Mock data generation
const generateMockStaff = (): StaffMember[] => {
  const baseStaff = [
    {
      id: '1',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '+1234567890',
      role: 'manager' as const,
      status: 'active' as const,
      joinDate: new Date('2022-03-15'),
      lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000),
      permissions: {
        canViewOrders: true,
        canManageOrders: true,
        canViewCustomers: true,
        canManageCustomers: true,
        canViewMenu: true,
        canManageMenu: true,
        canViewReports: true,
        canManageStaff: true,
        canViewAnalytics: true,
        canManageSettings: true,
        canProcessPayments: true,
        canManageInventory: true,
      },
      totalOrders: 1247,
      totalRevenue: 15680.5,
      rating: 4.9,
      address: '123 Main St, City, State 12345',
      emergencyContact: '+0987654321',
    },
    {
      id: '2',
      name: 'Mike Chen',
      email: '<EMAIL>',
      phone: '+1987654321',
      role: 'waiter' as const,
      status: 'active' as const,
      joinDate: new Date('2023-01-20'),
      lastActive: new Date(Date.now() - 30 * 60 * 1000),
      permissions: {
        canViewOrders: true,
        canManageOrders: true,
        canViewCustomers: true,
        canManageCustomers: false,
        canViewMenu: true,
        canManageMenu: false,
        canViewReports: false,
        canManageStaff: false,
        canViewAnalytics: false,
        canManageSettings: false,
        canProcessPayments: false,
        canManageInventory: false,
      },
      totalOrders: 892,
      totalRevenue: 11240.75,
      rating: 4.7,
      address: '456 Oak Ave, City, State 12345',
      emergencyContact: '+1122334455',
    },
    {
      id: '3',
      name: 'Emma Rodriguez',
      email: '<EMAIL>',
      phone: '+**********',
      role: 'cashier' as const,
      status: 'active' as const,
      joinDate: new Date('2023-06-10'),
      lastActive: new Date(Date.now() - 15 * 60 * 1000),
      permissions: {
        canViewOrders: true,
        canManageOrders: true,
        canViewCustomers: true,
        canManageCustomers: false,
        canViewMenu: true,
        canManageMenu: false,
        canViewReports: true,
        canManageStaff: false,
        canViewAnalytics: false,
        canManageSettings: false,
        canProcessPayments: true,
        canManageInventory: false,
      },
      totalOrders: 634,
      totalRevenue: 8960.25,
      rating: 4.8,
      address: '789 Pine Rd, City, State 12345',
      emergencyContact: '+1444555666',
    },
    {
      id: '4',
      name: 'David Kim',
      email: '<EMAIL>',
      phone: '+1777888999',
      role: 'kitchen' as const,
      status: 'active' as const,
      joinDate: new Date('2022-11-05'),
      lastActive: new Date(Date.now() - 45 * 60 * 1000),
      permissions: {
        canViewOrders: true,
        canManageOrders: true,
        canViewCustomers: false,
        canManageCustomers: false,
        canViewMenu: true,
        canManageMenu: true,
        canViewReports: false,
        canManageStaff: false,
        canViewAnalytics: false,
        canManageSettings: false,
        canProcessPayments: false,
        canManageInventory: true,
      },
      totalOrders: 0,
      totalRevenue: 0,
      rating: 4.6,
      address: '321 Elm St, City, State 12345',
      emergencyContact: '+1333444555',
    },
    {
      id: '5',
      name: 'Lisa Wang',
      email: '<EMAIL>',
      phone: '+1222333444',
      role: 'waiter' as const,
      status: 'inactive' as const,
      joinDate: new Date('2023-08-15'),
      lastActive: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      permissions: {
        canViewOrders: true,
        canManageOrders: true,
        canViewCustomers: true,
        canManageCustomers: false,
        canViewMenu: true,
        canManageMenu: false,
        canViewReports: false,
        canManageStaff: false,
        canViewAnalytics: false,
        canManageSettings: false,
        canProcessPayments: false,
        canManageInventory: false,
      },
      totalOrders: 156,
      totalRevenue: 1980.4,
      rating: 4.5,
      address: '654 Maple Dr, City, State 12345',
      emergencyContact: '+**********',
    },
  ]

  return baseStaff
}

const AccountsPageClient = () => {
  const [staff, setStaff] = useState<StaffMember[]>([])
  const [filteredStaff, setFilteredStaff] = useState<StaffMember[]>([])
  const [_selectedStaff, setSelectedStaff] = useState<StaffMember | null>(null)
  const [_showStaffDetails, setShowStaffDetails] = useState(false)
  const [_showAddStaff, setShowAddStaff] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterRole, setFilterRole] = useState<string>('all')
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [sortBy, setSortBy] = useState<'name' | 'role' | 'joinDate' | 'rating'>('name')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')

  // Initialize staff data
  useEffect(() => {
    const mockStaff = generateMockStaff()
    setStaff(mockStaff)
    setFilteredStaff(mockStaff)
  }, [])

  // Filter and sort staff
  useEffect(() => {
    let filtered = staff

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(
        (member) =>
          member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          member.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          member.phone.includes(searchTerm),
      )
    }

    // Filter by role
    if (filterRole !== 'all') {
      filtered = filtered.filter((member) => member.role === filterRole)
    }

    // Filter by status
    if (filterStatus !== 'all') {
      filtered = filtered.filter((member) => member.status === filterStatus)
    }

    // Sort staff
    filtered.sort((a, b) => {
      let aValue: string | number, bValue: string | number

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase()
          bValue = b.name.toLowerCase()
          break
        case 'role':
          aValue = a.role
          bValue = b.role
          break
        case 'joinDate':
          aValue = a.joinDate.getTime()
          bValue = b.joinDate.getTime()
          break
        case 'rating':
          aValue = a.rating
          bValue = b.rating
          break
        default:
          aValue = a.name.toLowerCase()
          bValue = b.name.toLowerCase()
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    setFilteredStaff(filtered)
  }, [staff, searchTerm, filterRole, filterStatus, sortBy, sortOrder])

  const getRoleColor = (role: StaffMember['role']) => {
    switch (role) {
      case 'admin':
        return 'from-red-500/20 to-red-700/20 text-red-800 border-red-300/50'
      case 'manager':
        return 'from-purple-500/20 to-purple-700/20 text-purple-800 border-purple-300/50'
      case 'waiter':
        return 'from-blue-500/20 to-blue-700/20 text-blue-800 border-blue-300/50'
      case 'cashier':
        return 'from-green-500/20 to-green-700/20 text-green-800 border-green-300/50'
      case 'kitchen':
        return 'from-orange-500/20 to-orange-700/20 text-orange-800 border-orange-300/50'
      default:
        return 'from-gray-500/20 to-gray-700/20 text-gray-800 border-gray-300/50'
    }
  }

  const _getRoleIcon = (role: StaffMember['role']) => {
    switch (role) {
      case 'admin':
        return <Crown className="h-4 w-4" />
      case 'manager':
        return <Shield className="h-4 w-4" />
      case 'waiter':
        return <Users className="h-4 w-4" />
      case 'cashier':
        return <CreditCard className="h-4 w-4" />
      case 'kitchen':
        return <ChefHat className="h-4 w-4" />
      default:
        return <Users className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: StaffMember['status']) => {
    switch (status) {
      case 'active':
        return 'from-emerald-500/20 to-green-700/20 text-emerald-800 border-emerald-300/50'
      case 'inactive':
        return 'from-gray-500/20 to-gray-700/20 text-gray-800 border-gray-300/50'
      case 'suspended':
        return 'from-red-500/20 to-red-700/20 text-red-800 border-red-300/50'
      default:
        return 'from-gray-500/20 to-gray-700/20 text-gray-800 border-gray-300/50'
    }
  }

  const getStatusIcon = (status: StaffMember['status']) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4" />
      case 'inactive':
        return <XCircle className="h-4 w-4" />
      case 'suspended':
        return <AlertTriangle className="h-4 w-4" />
      default:
        return <XCircle className="h-4 w-4" />
    }
  }

  const staffStats = {
    total: staff.length,
    active: staff.filter((s) => s.status === 'active').length,
    managers: staff.filter((s) => s.role === 'manager' || s.role === 'admin').length,
    waiters: staff.filter((s) => s.role === 'waiter').length,
    cashiers: staff.filter((s) => s.role === 'cashier').length,
    kitchen: staff.filter((s) => s.role === 'kitchen').length,
    totalRevenue: staff.reduce((sum, s) => sum + s.totalRevenue, 0),
    averageRating: staff.reduce((sum, s) => sum + s.rating, 0) / staff.length,
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-amber-500/10 via-orange-500/10 to-red-500/10 backdrop-blur-xl rounded-2xl border border-amber-200/30 shadow-xl" />
          <div className="relative p-6 lg:p-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-amber-400 via-orange-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25">
                  <Users className="h-6 w-6 text-white drop-shadow-lg" />
                </div>
                <div>
                  <h1 className="text-4xl font-black bg-gradient-to-r from-amber-600 via-orange-600 to-red-600 bg-clip-text text-transparent drop-shadow-sm">
                    Staff Accounts
                  </h1>
                  <div className="flex items-center space-x-1 mt-1">
                    <Shield className="h-3 w-3 text-amber-500 animate-pulse" />
                    <span className="text-xs text-amber-600 font-medium">
                      Role Management & Permissions
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <button className="group flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl hover:bg-white/90 hover:border-amber-300/70 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                  <Download className="h-4 w-4 mr-2 text-amber-600 group-hover:text-orange-600 transition-colors" />
                  <span className="font-semibold text-gray-700 group-hover:text-gray-800">
                    Export
                  </span>
                </button>
                <button className="group flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl hover:bg-white/90 hover:border-amber-300/70 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                  <RefreshCw className="h-4 w-4 mr-2 text-amber-600 group-hover:text-orange-600 group-hover:rotate-180 transition-all duration-300" />
                  <span className="font-semibold text-gray-700 group-hover:text-gray-800">
                    Refresh
                  </span>
                </button>
                <button
                  onClick={() => setShowAddStaff(true)}
                  className="group flex items-center px-6 py-3 bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 text-white rounded-xl hover:from-amber-500 hover:via-orange-600 hover:to-red-600 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-orange-400/50"
                >
                  <UserPlus className="h-4 w-4 mr-2 group-hover:rotate-12 transition-transform duration-300" />
                  <span className="font-bold">Add Staff</span>
                  <Zap className="h-4 w-4 ml-2 animate-pulse" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Staff Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-indigo-600/20 backdrop-blur-xl rounded-2xl border border-blue-500/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
            <div className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-bold text-blue-600 uppercase tracking-wide">
                    Total Staff
                  </p>
                  <div className="flex items-center mt-1">
                    <Users className="h-3 w-3 text-blue-500 mr-1" />
                    <span className="text-xs text-blue-600 font-medium">All Roles</span>
                  </div>
                </div>
                <div className="relative w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25 group-hover:scale-110 transition-transform duration-300">
                  <Users className="h-6 w-6 text-white drop-shadow-lg" />
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full animate-pulse" />
                </div>
              </div>
              <div className="text-3xl font-black text-blue-600 mb-2">{staffStats.total}</div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <TrendingUp className="h-4 w-4 text-blue-500 mr-2" />
                  <span className="text-sm text-blue-600 font-bold">+2 this month</span>
                </div>
                <Sparkles className="h-4 w-4 text-blue-500 animate-pulse" />
              </div>
            </div>
          </div>

          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/20 to-green-600/20 backdrop-blur-xl rounded-2xl border border-emerald-500/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
            <div className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-bold text-emerald-600 uppercase tracking-wide">
                    Active Staff
                  </p>
                  <div className="flex items-center mt-1">
                    <CheckCircle className="h-3 w-3 text-emerald-500 mr-1" />
                    <span className="text-xs text-emerald-600 font-medium">Currently Working</span>
                  </div>
                </div>
                <div className="relative w-12 h-12 bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg shadow-emerald-500/25 group-hover:scale-110 transition-transform duration-300">
                  <UserCheck className="h-6 w-6 text-white drop-shadow-lg" />
                </div>
              </div>
              <div className="text-3xl font-black text-emerald-600 mb-2">{staffStats.active}</div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Activity className="h-4 w-4 text-emerald-500 mr-2 animate-pulse" />
                  <span className="text-sm text-emerald-600 font-bold">Online</span>
                </div>
                <Zap className="h-4 w-4 text-emerald-500 animate-pulse" />
              </div>
            </div>
          </div>

          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-pink-600/20 backdrop-blur-xl rounded-2xl border border-purple-500/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
            <div className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-bold text-purple-600 uppercase tracking-wide">
                    Managers
                  </p>
                  <div className="flex items-center mt-1">
                    <Crown className="h-3 w-3 text-purple-500 mr-1 animate-pulse" />
                    <span className="text-xs text-purple-600 font-medium">Leadership</span>
                  </div>
                </div>
                <div className="relative w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg shadow-purple-500/25 group-hover:scale-110 transition-transform duration-300">
                  <Shield className="h-6 w-6 text-white drop-shadow-lg" />
                </div>
              </div>
              <div className="text-3xl font-black text-purple-600 mb-2">{staffStats.managers}</div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Award className="h-4 w-4 text-purple-500 mr-2" />
                  <span className="text-sm text-purple-600 font-bold">Admin Access</span>
                </div>
                <Crown className="h-4 w-4 text-purple-500 animate-pulse" />
              </div>
            </div>
          </div>

          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-orange-500/20 to-red-600/20 backdrop-blur-xl rounded-2xl border border-orange-500/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
            <div className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-bold text-orange-600 uppercase tracking-wide">
                    Avg Rating
                  </p>
                  <div className="flex items-center mt-1">
                    <Star className="h-3 w-3 text-orange-500 mr-1" />
                    <span className="text-xs text-orange-600 font-medium">Performance</span>
                  </div>
                </div>
                <div className="relative w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25 group-hover:scale-110 transition-transform duration-300">
                  <Target className="h-6 w-6 text-white drop-shadow-lg" />
                </div>
              </div>
              <div className="text-3xl font-black text-orange-600 mb-2">
                {staffStats.averageRating.toFixed(1)}
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <TrendingUp className="h-4 w-4 text-orange-500 mr-2" />
                  <span className="text-sm text-orange-600 font-bold">+0.2 this month</span>
                </div>
                <Sparkles className="h-4 w-4 text-orange-500 animate-pulse" />
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filter Controls */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-br from-amber-400/10 via-orange-500/10 to-red-500/10 backdrop-blur-xl rounded-2xl border border-amber-200/30 shadow-xl" />
          <div className="relative p-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              {/* Search */}
              <div className="relative group flex-1 max-w-md">
                <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-orange-500/20 rounded-xl blur-sm group-focus-within:blur-md group-focus-within:from-amber-400/30 group-focus-within:to-orange-500/30 transition-all duration-300" />
                <div className="relative flex items-center">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-amber-600 group-focus-within:text-orange-600 transition-colors z-10" />
                  <input
                    type="text"
                    placeholder="Search staff by name, email, or phone..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-12 pr-4 py-3 w-full bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg focus:shadow-xl"
                  />
                </div>
              </div>

              {/* Filters */}
              <div className="flex flex-wrap items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Filter className="h-4 w-4 text-amber-600" />
                  <select
                    value={filterRole}
                    onChange={(e) => setFilterRole(e.target.value)}
                    className="px-3 py-2 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 transition-all duration-300 text-gray-700 shadow-lg"
                  >
                    <option value="all">All Roles</option>
                    <option value="admin">Admin</option>
                    <option value="manager">Manager</option>
                    <option value="waiter">Waiter</option>
                    <option value="cashier">Cashier</option>
                    <option value="kitchen">Kitchen</option>
                  </select>
                </div>

                <div className="flex items-center space-x-2">
                  <select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                    className="px-3 py-2 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 transition-all duration-300 text-gray-700 shadow-lg"
                  >
                    <option value="all">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="suspended">Suspended</option>
                  </select>
                </div>

                <div className="flex items-center space-x-2">
                  <select
                    value={`${sortBy}-${sortOrder}`}
                    onChange={(e) => {
                      const [field, order] = e.target.value.split('-')
                      setSortBy(field as 'name' | 'role' | 'joinDate' | 'rating')
                      setSortOrder(order as 'asc' | 'desc')
                    }}
                    className="px-3 py-2 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 transition-all duration-300 text-gray-700 shadow-lg"
                  >
                    <option value="name-asc">Name A-Z</option>
                    <option value="name-desc">Name Z-A</option>
                    <option value="role-asc">Role A-Z</option>
                    <option value="role-desc">Role Z-A</option>
                    <option value="joinDate-desc">Newest First</option>
                    <option value="joinDate-asc">Oldest First</option>
                    <option value="rating-desc">Highest Rating</option>
                    <option value="rating-asc">Lowest Rating</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Staff List */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredStaff.map((member) => (
            <div key={member.id} className="relative group">
              <div className="absolute inset-0 bg-gradient-to-br from-white/40 to-white/60 backdrop-blur-xl rounded-2xl border border-white/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
              <div className="relative p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <div className="w-12 h-12 bg-gradient-to-br from-amber-400 to-orange-600 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25">
                        <span className="text-white font-bold text-lg">
                          {member.name
                            .split(' ')
                            .map((n) => n[0])
                            .join('')}
                        </span>
                      </div>
                      <div
                        className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center ${
                          member.status === 'active'
                            ? 'bg-emerald-500'
                            : member.status === 'inactive'
                              ? 'bg-gray-500'
                              : 'bg-red-500'
                        }`}
                      >
                        {getStatusIcon(member.status)}
                      </div>
                    </div>
                    <div>
                      <h3 className="font-bold text-gray-800 text-lg">{member.name}</h3>
                      <p className="text-sm text-gray-600">{member.email}</p>
                      <div className="flex items-center space-x-2 mt-1">
                        <span
                          className={`px-2 py-1 text-xs font-bold rounded-full border bg-gradient-to-r ${getRoleColor(member.role)} shadow-lg`}
                        >
                          {member.role.toUpperCase()}
                        </span>
                        <span
                          className={`px-2 py-1 text-xs font-bold rounded-full border bg-gradient-to-r ${getStatusColor(member.status)} shadow-lg`}
                        >
                          {member.status.toUpperCase()}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    <button
                      onClick={() => {
                        setSelectedStaff(member)
                        setShowStaffDetails(true)
                      }}
                      className="p-2 text-amber-600 hover:text-orange-600 hover:bg-amber-100/50 rounded-xl transition-all duration-300"
                    >
                      <Eye className="h-4 w-4" />
                    </button>
                    <button className="p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-100/50 rounded-xl transition-all duration-300">
                      <Edit className="h-4 w-4" />
                    </button>
                    <button className="p-2 text-red-600 hover:text-red-700 hover:bg-red-100/50 rounded-xl transition-all duration-300">
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Phone className="h-4 w-4 text-gray-500" />
                      <span className="text-sm text-gray-600">{member.phone}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 text-amber-500" />
                      <span className="text-sm font-bold text-gray-700">{member.rating}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span className="text-sm text-gray-600">
                        Joined {member.joinDate.toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Activity className="h-4 w-4 text-emerald-500" />
                      <span className="text-sm text-gray-600">
                        {Math.floor((Date.now() - member.lastActive.getTime()) / (1000 * 60))}m ago
                      </span>
                    </div>
                  </div>

                  {member.role !== 'kitchen' && (
                    <div className="flex items-center justify-between pt-2 border-t border-gray-200/50">
                      <div className="flex items-center space-x-2">
                        <ShoppingBag className="h-4 w-4 text-blue-500" />
                        <span className="text-sm text-gray-600">{member.totalOrders} orders</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <DollarSign className="h-4 w-4 text-green-500" />
                        <span className="text-sm font-bold text-gray-700">
                          ${member.totalRevenue.toFixed(0)}
                        </span>
                      </div>
                    </div>
                  )}

                  <div className="pt-3">
                    <button
                      onClick={() => {
                        setSelectedStaff(member)
                        setShowStaffDetails(true)
                      }}
                      className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-gradient-to-r from-amber-400/20 to-orange-500/20 hover:from-amber-400/30 hover:to-orange-500/30 border border-amber-200/50 rounded-xl transition-all duration-300 group"
                    >
                      <span className="text-sm font-semibold text-amber-700 group-hover:text-orange-700">
                        View Details
                      </span>
                      <ChevronRight className="h-4 w-4 text-amber-600 group-hover:text-orange-600 group-hover:translate-x-1 transition-all duration-300" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredStaff.length === 0 && (
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-br from-gray-400/10 via-slate-500/10 to-gray-500/10 backdrop-blur-xl rounded-2xl border border-gray-200/30 shadow-xl" />
            <div className="relative p-12 text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-gray-400 to-slate-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg shadow-gray-500/25">
                <Users className="h-8 w-8 text-white drop-shadow-lg" />
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-2">No Staff Found</h3>
              <p className="text-gray-600 mb-6">
                {searchTerm || filterRole !== 'all' || filterStatus !== 'all'
                  ? 'Try adjusting your search or filter criteria.'
                  : 'Get started by adding your first staff member.'}
              </p>
              <button
                onClick={() => setShowAddStaff(true)}
                className="group flex items-center px-6 py-3 bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 text-white rounded-xl hover:from-amber-500 hover:via-orange-600 hover:to-red-600 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-orange-400/50 mx-auto"
              >
                <UserPlus className="h-4 w-4 mr-2 group-hover:rotate-12 transition-transform duration-300" />
                <span className="font-bold">Add First Staff Member</span>
                <Zap className="h-4 w-4 ml-2 animate-pulse" />
              </button>
            </div>
          </div>
        )}

        {/* Staff Details Modal */}
        {_selectedStaff && _showStaffDetails && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="relative w-full max-w-4xl max-h-[90vh] overflow-y-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-white/95 to-amber-50/95 backdrop-blur-xl rounded-2xl border border-amber-200/30 shadow-2xl" />
              <div className="relative p-8">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-16 h-16 bg-gradient-to-br from-amber-400 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg shadow-orange-500/25">
                      <span className="text-white font-bold text-2xl">
                        {_selectedStaff.name
                          .split(' ')
                          .map((n) => n[0])
                          .join('')}
                      </span>
                    </div>
                    <div>
                      <h2 className="text-3xl font-black text-gray-800">{_selectedStaff.name}</h2>
                      <div className="flex items-center space-x-2 mt-1">
                        <span
                          className={`px-3 py-1 text-sm font-bold rounded-full border bg-gradient-to-r ${getRoleColor(_selectedStaff.role)} shadow-lg`}
                        >
                          {_selectedStaff.role.toUpperCase()}
                        </span>
                        <span
                          className={`px-3 py-1 text-sm font-bold rounded-full border bg-gradient-to-r ${getStatusColor(_selectedStaff.status)} shadow-lg`}
                        >
                          {_selectedStaff.status.toUpperCase()}
                        </span>
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowStaffDetails(false)}
                    className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100/50 rounded-xl transition-all duration-300"
                  >
                    <XCircle className="h-6 w-6" />
                  </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Personal Information */}
                  <div className="space-y-6">
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-br from-blue-400/10 to-indigo-500/10 backdrop-blur-xl rounded-2xl border border-blue-200/30 shadow-xl" />
                      <div className="relative p-6">
                        <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
                          <Phone className="h-5 w-5 mr-2 text-blue-600" />
                          Contact Information
                        </h3>
                        <div className="space-y-3">
                          <div className="flex items-center space-x-3">
                            <Phone className="h-4 w-4 text-gray-500" />
                            <span className="text-gray-700">{_selectedStaff.phone}</span>
                          </div>
                          <div className="flex items-center space-x-3">
                            <span className="text-gray-500">📧</span>
                            <span className="text-gray-700">{_selectedStaff.email}</span>
                          </div>
                          {_selectedStaff.address && (
                            <div className="flex items-center space-x-3">
                              <span className="text-gray-500">📍</span>
                              <span className="text-gray-700">{_selectedStaff.address}</span>
                            </div>
                          )}
                          {_selectedStaff.emergencyContact && (
                            <div className="flex items-center space-x-3">
                              <span className="text-gray-500">🚨</span>
                              <span className="text-gray-700">
                                {_selectedStaff.emergencyContact}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Performance Metrics */}
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-br from-emerald-400/10 to-green-500/10 backdrop-blur-xl rounded-2xl border border-emerald-200/30 shadow-xl" />
                      <div className="relative p-6">
                        <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
                          <Target className="h-5 w-5 mr-2 text-emerald-600" />
                          Performance Metrics
                        </h3>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="text-center">
                            <div className="text-2xl font-black text-emerald-600">
                              {_selectedStaff.rating}
                            </div>
                            <div className="text-sm text-gray-600">Rating</div>
                          </div>
                          {_selectedStaff.role !== 'kitchen' && (
                            <>
                              <div className="text-center">
                                <div className="text-2xl font-black text-emerald-600">
                                  {_selectedStaff.totalOrders}
                                </div>
                                <div className="text-sm text-gray-600">Orders</div>
                              </div>
                              <div className="text-center">
                                <div className="text-2xl font-black text-emerald-600">
                                  ${_selectedStaff.totalRevenue.toFixed(0)}
                                </div>
                                <div className="text-sm text-gray-600">Revenue</div>
                              </div>
                            </>
                          )}
                          <div className="text-center">
                            <div className="text-2xl font-black text-emerald-600">
                              {Math.floor(
                                (Date.now() - _selectedStaff.joinDate.getTime()) /
                                  (1000 * 60 * 60 * 24),
                              )}
                            </div>
                            <div className="text-sm text-gray-600">Days Active</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Permissions */}
                  <div className="space-y-6">
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-br from-purple-400/10 to-pink-500/10 backdrop-blur-xl rounded-2xl border border-purple-200/30 shadow-xl" />
                      <div className="relative p-6">
                        <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
                          <Shield className="h-5 w-5 mr-2 text-purple-600" />
                          Permissions
                        </h3>
                        <div className="space-y-3">
                          {Object.entries(_selectedStaff.permissions).map(([key, value]) => (
                            <div key={key} className="flex items-center justify-between">
                              <span className="text-sm text-gray-700 capitalize">
                                {key
                                  .replace(/([A-Z])/g, ' $1')
                                  .replace(/^./, (str) => str.toUpperCase())}
                              </span>
                              <div
                                className={`w-4 h-4 rounded-full flex items-center justify-center ${
                                  value ? 'bg-emerald-500' : 'bg-gray-300'
                                }`}
                              >
                                {value && <CheckCircle className="h-3 w-3 text-white" />}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-br from-amber-400/10 to-orange-500/10 backdrop-blur-xl rounded-2xl border border-amber-200/30 shadow-xl" />
                      <div className="relative p-6">
                        <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
                          <Settings className="h-5 w-5 mr-2 text-amber-600" />
                          Quick Actions
                        </h3>
                        <div className="space-y-3">
                          <button className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-400/20 to-indigo-500/20 hover:from-blue-400/30 hover:to-indigo-500/30 border border-blue-200/50 rounded-xl transition-all duration-300 group">
                            <Edit className="h-4 w-4 text-blue-600" />
                            <span className="text-sm font-semibold text-blue-700">
                              Edit Staff Member
                            </span>
                          </button>
                          <button className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-gradient-to-r from-emerald-400/20 to-green-500/20 hover:from-emerald-400/30 hover:to-green-500/30 border border-emerald-200/50 rounded-xl transition-all duration-300 group">
                            <Shield className="h-4 w-4 text-emerald-600" />
                            <span className="text-sm font-semibold text-emerald-700">
                              Manage Permissions
                            </span>
                          </button>
                          <button className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-400/20 to-pink-500/20 hover:from-purple-400/30 hover:to-pink-500/30 border border-purple-200/50 rounded-xl transition-all duration-300 group">
                            <BarChart3 className="h-4 w-4 text-purple-600" />
                            <span className="text-sm font-semibold text-purple-700">
                              View Performance
                            </span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Add Staff Modal */}
        {_showAddStaff && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="relative w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-white/95 to-amber-50/95 backdrop-blur-xl rounded-2xl border border-amber-200/30 shadow-2xl" />
              <div className="relative p-8">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-amber-400 to-orange-600 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25">
                      <UserPlus className="h-6 w-6 text-white drop-shadow-lg" />
                    </div>
                    <div>
                      <h2 className="text-3xl font-black text-gray-800">Add New Staff Member</h2>
                      <p className="text-gray-600">
                        Create a new staff account with role-based permissions
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowAddStaff(false)}
                    className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100/50 rounded-xl transition-all duration-300"
                  >
                    <XCircle className="h-6 w-6" />
                  </button>
                </div>

                <form className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <label className="text-sm font-bold text-gray-700">Full Name</label>
                      <input
                        type="text"
                        placeholder="Enter full name"
                        className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-bold text-gray-700">Email Address</label>
                      <input
                        type="email"
                        placeholder="Enter email address"
                        className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-bold text-gray-700">Phone Number</label>
                      <input
                        type="tel"
                        placeholder="Enter phone number"
                        className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-bold text-gray-700">Role</label>
                      <select className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 transition-all duration-300 text-gray-700 shadow-lg">
                        <option value="">Select a role</option>
                        <option value="waiter">Waiter</option>
                        <option value="cashier">Cashier</option>
                        <option value="kitchen">Kitchen Staff</option>
                        <option value="manager">Manager</option>
                        <option value="admin">Admin</option>
                      </select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-bold text-gray-700">Address (Optional)</label>
                    <input
                      type="text"
                      placeholder="Enter address"
                      className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-bold text-gray-700">
                      Emergency Contact (Optional)
                    </label>
                    <input
                      type="tel"
                      placeholder="Enter emergency contact number"
                      className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg"
                    />
                  </div>

                  <div className="flex items-center justify-end space-x-4 pt-6">
                    <button
                      type="button"
                      onClick={() => setShowAddStaff(false)}
                      className="px-6 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl hover:bg-white/90 hover:border-amber-300/70 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
                    >
                      <span className="font-semibold text-gray-700">Cancel</span>
                    </button>
                    <button
                      type="submit"
                      className="group flex items-center px-6 py-3 bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 text-white rounded-xl hover:from-amber-500 hover:via-orange-600 hover:to-red-600 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-orange-400/50"
                    >
                      <UserPlus className="h-4 w-4 mr-2 group-hover:rotate-12 transition-transform duration-300" />
                      <span className="font-bold">Create Staff Account</span>
                      <Zap className="h-4 w-4 ml-2 animate-pulse" />
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  )
}

export { AccountsPageClient }
