'use client'

import React, { useState } from 'react'
import { Layout } from '@/components/Merchant/Layout'
import {
  HelpCircle,
  MessageSquare,
  Phone,
  Mail,
  FileText,
  Search,
  Filter,
  Plus,
  Clock,
  CheckCircle,
  AlertTriangle,
  Star,
  Zap,
  Sparkles,
  TrendingUp,
  BarChart3,
  ChevronRight,
  XCircle,
  ExternalLink,
  BookOpen,
  Headphones,
  Activity,
  Eye,
  Edit,
  Trash2,
} from 'lucide-react'

// TypeScript interfaces
interface SupportTicket {
  id: string
  title: string
  description: string
  status: 'open' | 'in_progress' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  category: string
  createdAt: string
  updatedAt: string
  assignedTo?: string
  customerName: string
  customerEmail: string
}

interface FAQItem {
  id: string
  question: string
  answer: string
  category: string
  helpful: number
  notHelpful: number
}

interface KnowledgeBaseArticle {
  id: string
  title: string
  content: string
  category: string
  tags: string[]
  lastUpdated: string
  views: number
}

export const SupportPageClient: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'tickets' | 'knowledge' | 'contact'>(
    'overview',
  )
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')

  // Mock data
  const tickets: SupportTicket[] = [
    {
      id: 'TKT-001',
      title: 'Payment processing issue',
      description: 'Customer unable to complete payment for order #12345',
      status: 'open',
      priority: 'high',
      category: 'Payment',
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-15T10:30:00Z',
      assignedTo: 'Support Team',
      customerName: 'John Doe',
      customerEmail: '<EMAIL>',
    },
    {
      id: 'TKT-002',
      title: 'Menu item not displaying correctly',
      description: 'New menu item not showing up on customer app',
      status: 'in_progress',
      priority: 'medium',
      category: 'Technical',
      createdAt: '2024-01-14T14:20:00Z',
      updatedAt: '2024-01-15T09:15:00Z',
      assignedTo: 'Tech Team',
      customerName: 'Sarah Wilson',
      customerEmail: '<EMAIL>',
    },
    {
      id: 'TKT-003',
      title: 'Order delivery tracking',
      description: 'Customer wants to track their order status',
      status: 'resolved',
      priority: 'low',
      category: 'Order Management',
      createdAt: '2024-01-13T16:45:00Z',
      updatedAt: '2024-01-14T11:30:00Z',
      assignedTo: 'Support Team',
      customerName: 'Mike Johnson',
      customerEmail: '<EMAIL>',
    },
  ]

  const faqItems: FAQItem[] = [
    {
      id: 'faq-1',
      question: 'How do I add new menu items?',
      answer:
        'To add new menu items, go to Menu Management > Add Item. Fill in the details including name, description, price, and upload an image. Make sure to set availability and category.',
      category: 'Menu Management',
      helpful: 45,
      notHelpful: 2,
    },
    {
      id: 'faq-2',
      question: 'How can I track my daily sales?',
      answer:
        'You can view your daily sales in the Reports section. Go to Reports > Sales Analytics to see detailed breakdowns by time period, payment method, and menu items.',
      category: 'Reports',
      helpful: 38,
      notHelpful: 1,
    },
    {
      id: 'faq-3',
      question: 'What payment methods do you support?',
      answer:
        'We support credit cards, debit cards, digital wallets (Apple Pay, Google Pay), and cash on delivery. All payments are processed securely through our payment partners.',
      category: 'Payment',
      helpful: 52,
      notHelpful: 3,
    },
  ]

  const knowledgeBase: KnowledgeBaseArticle[] = [
    {
      id: 'kb-1',
      title: 'Getting Started with YoFood Merchant Dashboard',
      content:
        'Complete guide to setting up your merchant account and configuring your restaurant profile.',
      category: 'Getting Started',
      tags: ['setup', 'onboarding', 'configuration'],
      lastUpdated: '2024-01-10',
      views: 1250,
    },
    {
      id: 'kb-2',
      title: 'Menu Management Best Practices',
      content:
        'Tips and tricks for managing your menu effectively to increase sales and customer satisfaction.',
      category: 'Menu Management',
      tags: ['menu', 'optimization', 'sales'],
      lastUpdated: '2024-01-08',
      views: 890,
    },
    {
      id: 'kb-3',
      title: 'Understanding Your Analytics Dashboard',
      content:
        'Learn how to interpret your sales data, customer insights, and performance metrics.',
      category: 'Analytics',
      tags: ['analytics', 'reports', 'insights'],
      lastUpdated: '2024-01-05',
      views: 756,
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'from-red-400/20 to-red-500/20 text-red-800 border-red-200/50'
      case 'in_progress':
        return 'from-yellow-400/20 to-amber-500/20 text-amber-800 border-amber-200/50'
      case 'resolved':
        return 'from-emerald-400/20 to-green-500/20 text-emerald-800 border-emerald-200/50'
      case 'closed':
        return 'from-gray-400/20 to-gray-500/20 text-gray-800 border-gray-200/50'
      default:
        return 'from-gray-400/20 to-gray-500/20 text-gray-800 border-gray-200/50'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'from-red-500 to-red-600'
      case 'high':
        return 'from-orange-500 to-red-600'
      case 'medium':
        return 'from-yellow-500 to-amber-600'
      case 'low':
        return 'from-emerald-500 to-green-600'
      default:
        return 'from-gray-500 to-gray-600'
    }
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-amber-500/10 via-orange-500/10 to-red-500/10 backdrop-blur-xl rounded-2xl border border-amber-200/30 shadow-xl" />
          <div className="relative p-6 lg:p-8">
            <div className="flex items-center space-x-3 mb-2">
              <div className="w-12 h-12 bg-gradient-to-br from-amber-400 via-orange-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25">
                <Headphones className="h-6 w-6 text-white drop-shadow-lg" />
              </div>
              <div>
                <h1 className="text-4xl font-black bg-gradient-to-r from-amber-600 via-orange-600 to-red-600 bg-clip-text text-transparent drop-shadow-sm">
                  Support Center
                </h1>
                <p className="text-gray-600 font-medium">
                  Get help and manage your support requests
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Open Tickets */}
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-red-400/20 to-red-500/20 backdrop-blur-xl rounded-2xl border border-red-200/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
            <div className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-bold text-red-600 uppercase tracking-wide">
                    Open Tickets
                  </p>
                  <div className="flex items-center mt-1">
                    <AlertTriangle className="h-3 w-3 text-red-600 mr-1" />
                    <span className="text-xs text-red-600 font-medium">Needs Attention</span>
                  </div>
                </div>
                <div className="relative w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg shadow-red-500/25 group-hover:scale-110 transition-transform duration-300">
                  <MessageSquare className="h-6 w-6 text-white drop-shadow-lg" />
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full animate-pulse" />
                </div>
              </div>
              <div className="text-3xl font-black text-red-600 mb-2">3</div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <TrendingUp className="h-4 w-4 text-red-600 mr-2" />
                  <span className="text-sm text-red-600 font-bold">+1 this week</span>
                </div>
                <Sparkles className="h-4 w-4 text-red-600 animate-pulse" />
              </div>
            </div>
          </div>

          {/* Resolved Today */}
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-400/20 to-green-500/20 backdrop-blur-xl rounded-2xl border border-emerald-200/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
            <div className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-bold text-emerald-600 uppercase tracking-wide">
                    Resolved Today
                  </p>
                  <div className="flex items-center mt-1">
                    <CheckCircle className="h-3 w-3 text-emerald-600 mr-1" />
                    <span className="text-xs text-emerald-600 font-medium">Great Job!</span>
                  </div>
                </div>
                <div className="relative w-12 h-12 bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg shadow-emerald-500/25 group-hover:scale-110 transition-transform duration-300">
                  <CheckCircle className="h-6 w-6 text-white drop-shadow-lg" />
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full animate-pulse" />
                </div>
              </div>
              <div className="text-3xl font-black text-emerald-600 mb-2">12</div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <TrendingUp className="h-4 w-4 text-emerald-600 mr-2" />
                  <span className="text-sm text-emerald-600 font-bold">+3 from yesterday</span>
                </div>
                <Sparkles className="h-4 w-4 text-emerald-600 animate-pulse" />
              </div>
            </div>
          </div>

          {/* Average Response Time */}
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-indigo-500/20 backdrop-blur-xl rounded-2xl border border-blue-200/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
            <div className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-bold text-blue-600 uppercase tracking-wide">
                    Avg Response
                  </p>
                  <div className="flex items-center mt-1">
                    <Clock className="h-3 w-3 text-blue-600 mr-1" />
                    <span className="text-xs text-blue-600 font-medium">Fast Response</span>
                  </div>
                </div>
                <div className="relative w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25 group-hover:scale-110 transition-transform duration-300">
                  <Clock className="h-6 w-6 text-white drop-shadow-lg" />
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full animate-pulse" />
                </div>
              </div>
              <div className="text-3xl font-black text-blue-600 mb-2">2.4h</div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <TrendingUp className="h-4 w-4 text-blue-600 mr-2" />
                  <span className="text-sm text-blue-600 font-bold">-0.5h improvement</span>
                </div>
                <Sparkles className="h-4 w-4 text-blue-600 animate-pulse" />
              </div>
            </div>
          </div>

          {/* Customer Satisfaction */}
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-amber-400/20 to-orange-500/20 backdrop-blur-xl rounded-2xl border border-amber-200/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
            <div className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-bold text-amber-600 uppercase tracking-wide">
                    Satisfaction
                  </p>
                  <div className="flex items-center mt-1">
                    <Star className="h-3 w-3 text-amber-600 mr-1" />
                    <span className="text-xs text-amber-600 font-medium">Excellent</span>
                  </div>
                </div>
                <div className="relative w-12 h-12 bg-gradient-to-br from-amber-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg shadow-amber-500/25 group-hover:scale-110 transition-transform duration-300">
                  <Star className="h-6 w-6 text-white drop-shadow-lg" />
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full animate-pulse" />
                </div>
              </div>
              <div className="text-3xl font-black text-amber-600 mb-2">4.8/5</div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <TrendingUp className="h-4 w-4 text-amber-600 mr-2" />
                  <span className="text-sm text-amber-600 font-bold">+0.2 this month</span>
                </div>
                <Sparkles className="h-4 w-4 text-amber-600 animate-pulse" />
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="relative">
          <div className="absolute inset-0 bg-white/40 backdrop-blur-sm rounded-2xl border border-white/50 shadow-xl" />
          <div className="relative p-6">
            <div className="flex flex-wrap gap-2 mb-6">
              {[
                { id: 'overview', label: 'Overview', icon: BarChart3 },
                { id: 'tickets', label: 'Support Tickets', icon: MessageSquare },
                { id: 'knowledge', label: 'Knowledge Base', icon: BookOpen },
                { id: 'contact', label: 'Contact Us', icon: Phone },
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() =>
                    setActiveTab(tab.id as 'overview' | 'tickets' | 'knowledge' | 'contact')
                  }
                  className={`group flex items-center px-6 py-3 rounded-xl font-bold transition-all duration-300 ${
                    activeTab === tab.id
                      ? 'bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 text-white shadow-xl hover:shadow-2xl transform hover:scale-105 border border-orange-400/50'
                      : 'bg-white/80 backdrop-blur-sm border border-amber-200/50 text-gray-700 hover:bg-white/90 hover:border-amber-300/70 shadow-lg hover:shadow-xl transform hover:scale-105'
                  }`}
                >
                  <tab.icon className="h-4 w-4 mr-2 group-hover:rotate-12 transition-transform duration-300" />
                  <span>{tab.label}</span>
                  {activeTab === tab.id && <Zap className="h-4 w-4 ml-2 animate-pulse" />}
                </button>
              ))}
            </div>

            {/* Tab Content */}
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* Recent Tickets */}
                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-br from-white/40 to-white/60 backdrop-blur-xl rounded-2xl border border-white/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
                  <div className="relative p-6">
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-amber-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg shadow-amber-500/25">
                          <MessageSquare className="h-5 w-5 text-white drop-shadow-lg" />
                        </div>
                        <div>
                          <h3 className="text-lg font-bold text-gray-800">
                            Recent Support Tickets
                          </h3>
                          <div className="flex items-center space-x-1">
                            <Activity className="h-3 w-3 text-amber-600 animate-pulse" />
                            <span className="text-xs text-amber-600 font-medium">
                              3 Active Tickets
                            </span>
                          </div>
                        </div>
                      </div>
                      <button className="group flex items-center px-4 py-2 bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 text-white rounded-xl hover:from-amber-500 hover:via-orange-600 hover:to-red-600 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-orange-400/50">
                        <Plus className="h-4 w-4 mr-2 group-hover:rotate-90 transition-transform duration-300" />
                        <span className="font-bold">New Ticket</span>
                        <Zap className="h-4 w-4 ml-2 animate-pulse" />
                      </button>
                    </div>

                    <div className="space-y-4">
                      {tickets.slice(0, 3).map((ticket) => (
                        <div key={ticket.id} className="relative group">
                          <div className="absolute inset-0 bg-gradient-to-r from-white/60 to-white/80 backdrop-blur-sm rounded-xl border border-white/70 shadow-lg group-hover:shadow-xl transition-all duration-300" />
                          <div className="relative p-4">
                            <div className="flex items-center justify-between mb-3">
                              <div className="flex items-center space-x-3">
                                <div className="w-8 h-8 bg-gradient-to-br from-amber-400 to-orange-500 rounded-lg flex items-center justify-center shadow-md">
                                  <MessageSquare className="h-4 w-4 text-white" />
                                </div>
                                <div>
                                  <h4 className="font-bold text-gray-800">{ticket.title}</h4>
                                  <p className="text-sm text-gray-600">
                                    #{ticket.id} • {ticket.customerName}
                                  </p>
                                </div>
                              </div>
                              <div className="flex items-center space-x-2">
                                <span
                                  className={`px-3 py-1 text-xs font-bold rounded-full border shadow-lg bg-gradient-to-r ${getStatusColor(ticket.status)}`}
                                >
                                  {ticket.status.toUpperCase()}
                                </span>
                                <span
                                  className={`px-3 py-1 text-xs font-bold rounded-full border shadow-lg bg-gradient-to-r ${getPriorityColor(ticket.priority)} text-white`}
                                >
                                  {ticket.priority.toUpperCase()}
                                </span>
                              </div>
                            </div>
                            <p className="text-sm text-gray-600 mb-3">{ticket.description}</p>
                            <div className="flex items-center justify-between text-xs text-gray-500">
                              <span>
                                Created: {new Date(ticket.createdAt).toLocaleDateString()}
                              </span>
                              <span>Assigned to: {ticket.assignedTo}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[
                    {
                      title: 'Create Support Ticket',
                      description: 'Submit a new support request',
                      icon: Plus,
                      color: 'from-amber-400 to-orange-500',
                    },
                    {
                      title: 'Browse Knowledge Base',
                      description: 'Find answers to common questions',
                      icon: BookOpen,
                      color: 'from-blue-400 to-indigo-500',
                    },
                    {
                      title: 'Contact Support Team',
                      description: 'Get immediate help from our team',
                      icon: Phone,
                      color: 'from-emerald-400 to-green-500',
                    },
                  ].map((action, index) => (
                    <div key={index} className="relative group">
                      <div className="absolute inset-0 bg-gradient-to-br from-white/40 to-white/60 backdrop-blur-xl rounded-2xl border border-white/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
                      <div className="relative p-6">
                        <div className="flex items-center space-x-3 mb-4">
                          <div
                            className={`w-10 h-10 bg-gradient-to-br ${action.color} rounded-xl flex items-center justify-center shadow-lg`}
                          >
                            <action.icon className="h-5 w-5 text-white drop-shadow-lg" />
                          </div>
                          <div>
                            <h4 className="font-bold text-gray-800">{action.title}</h4>
                            <p className="text-sm text-gray-600">{action.description}</p>
                          </div>
                        </div>
                        <button className="w-full group flex items-center justify-center px-4 py-2 bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 text-white rounded-xl hover:from-amber-500 hover:via-orange-600 hover:to-red-600 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-orange-400/50">
                          <span className="font-bold">Get Started</span>
                          <ChevronRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Support Tickets Tab */}
            {activeTab === 'tickets' && (
              <div className="space-y-6">
                {/* Search and Filter */}
                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-r from-white/60 to-white/80 backdrop-blur-sm rounded-xl border border-white/70 shadow-lg" />
                  <div className="relative p-6">
                    <div className="flex flex-col lg:flex-row gap-4">
                      <div className="flex-1 relative group">
                        <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-orange-500/20 rounded-xl blur-sm group-focus-within:blur-md group-focus-within:from-amber-400/30 group-focus-within:to-orange-500/30 transition-all duration-300" />
                        <div className="relative flex items-center">
                          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-amber-600 group-focus-within:text-orange-600 transition-colors z-10" />
                          <input
                            type="text"
                            placeholder="Search tickets..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="pl-12 pr-4 py-3 w-full bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg focus:shadow-xl"
                          />
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <select
                          value={selectedCategory}
                          onChange={(e) => setSelectedCategory(e.target.value)}
                          className="px-4 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 transition-all duration-300 text-gray-700 shadow-lg"
                        >
                          <option value="all">All Categories</option>
                          <option value="Payment">Payment</option>
                          <option value="Technical">Technical</option>
                          <option value="Order Management">Order Management</option>
                          <option value="Account">Account</option>
                        </select>
                        <button className="group flex items-center px-6 py-3 bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 text-white rounded-xl hover:from-amber-500 hover:via-orange-600 hover:to-red-600 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-orange-400/50">
                          <Filter className="h-4 w-4 mr-2 group-hover:rotate-12 transition-transform duration-300" />
                          <span className="font-bold">Filter</span>
                          <Zap className="h-4 w-4 ml-2 animate-pulse" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Tickets List */}
                <div className="space-y-4">
                  {tickets.map((ticket) => (
                    <div key={ticket.id} className="relative group">
                      <div className="absolute inset-0 bg-gradient-to-r from-white/60 to-white/80 backdrop-blur-sm rounded-xl border border-white/70 shadow-lg group-hover:shadow-xl transition-all duration-300" />
                      <div className="relative p-6">
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center space-x-4">
                            <div className="w-12 h-12 bg-gradient-to-br from-amber-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg">
                              <MessageSquare className="h-6 w-6 text-white drop-shadow-lg" />
                            </div>
                            <div>
                              <h4 className="text-lg font-bold text-gray-800">{ticket.title}</h4>
                              <div className="flex items-center space-x-4 text-sm text-gray-600">
                                <span>#{ticket.id}</span>
                                <span>•</span>
                                <span>{ticket.customerName}</span>
                                <span>•</span>
                                <span>{ticket.category}</span>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-3">
                            <span
                              className={`px-3 py-1 text-xs font-bold rounded-full border shadow-lg bg-gradient-to-r ${getStatusColor(ticket.status)}`}
                            >
                              {ticket.status.toUpperCase()}
                            </span>
                            <span
                              className={`px-3 py-1 text-xs font-bold rounded-full border shadow-lg bg-gradient-to-r ${getPriorityColor(ticket.priority)} text-white`}
                            >
                              {ticket.priority.toUpperCase()}
                            </span>
                            <button className="group flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl hover:bg-white/90 hover:border-amber-300/70 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                              <Eye className="h-4 w-4 mr-2 text-amber-600 group-hover:text-orange-600 transition-colors" />
                              <span className="font-semibold text-gray-700 group-hover:text-gray-800">
                                View
                              </span>
                            </button>
                          </div>
                        </div>
                        <p className="text-gray-600 mb-4">{ticket.description}</p>
                        <div className="flex items-center justify-between text-sm text-gray-500">
                          <div className="flex items-center space-x-6">
                            <span>Created: {new Date(ticket.createdAt).toLocaleDateString()}</span>
                            <span>Updated: {new Date(ticket.updatedAt).toLocaleDateString()}</span>
                            <span>Assigned to: {ticket.assignedTo}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <button className="p-2 text-amber-600 hover:text-orange-600 hover:bg-amber-50 rounded-lg transition-all duration-300">
                              <Edit className="h-4 w-4" />
                            </button>
                            <button className="p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-all duration-300">
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Knowledge Base Tab */}
            {activeTab === 'knowledge' && (
              <div className="space-y-6">
                {/* Search Knowledge Base */}
                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-r from-white/60 to-white/80 backdrop-blur-sm rounded-xl border border-white/70 shadow-lg" />
                  <div className="relative p-6">
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-xl flex items-center justify-center shadow-lg">
                        <BookOpen className="h-5 w-5 text-white drop-shadow-lg" />
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-gray-800">Knowledge Base</h3>
                        <p className="text-sm text-gray-600">
                          Find answers to common questions and learn best practices
                        </p>
                      </div>
                    </div>
                    <div className="relative group">
                      <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-orange-500/20 rounded-xl blur-sm group-focus-within:blur-md group-focus-within:from-amber-400/30 group-focus-within:to-orange-500/30 transition-all duration-300" />
                      <div className="relative flex items-center">
                        <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-amber-600 group-focus-within:text-orange-600 transition-colors z-10" />
                        <input
                          type="text"
                          placeholder="Search knowledge base..."
                          className="pl-12 pr-4 py-3 w-full bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg focus:shadow-xl"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* FAQ Section */}
                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-br from-white/40 to-white/60 backdrop-blur-xl rounded-2xl border border-white/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
                  <div className="relative p-6">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-gradient-to-br from-emerald-400 to-green-500 rounded-xl flex items-center justify-center shadow-lg">
                        <HelpCircle className="h-5 w-5 text-white drop-shadow-lg" />
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-gray-800">
                          Frequently Asked Questions
                        </h3>
                        <p className="text-sm text-gray-600">Quick answers to common questions</p>
                      </div>
                    </div>
                    <div className="space-y-4">
                      {faqItems.map((faq) => (
                        <div key={faq.id} className="relative group">
                          <div className="absolute inset-0 bg-gradient-to-r from-white/60 to-white/80 backdrop-blur-sm rounded-xl border border-white/70 shadow-lg group-hover:shadow-xl transition-all duration-300" />
                          <div className="relative p-4">
                            <div className="flex items-start justify-between mb-3">
                              <h4 className="font-bold text-gray-800 flex-1">{faq.question}</h4>
                              <span className="px-2 py-1 text-xs font-bold bg-gradient-to-r from-amber-400/20 to-orange-500/20 text-amber-800 border border-amber-200/50 rounded-full">
                                {faq.category}
                              </span>
                            </div>
                            <p className="text-gray-600 mb-4">{faq.answer}</p>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-4 text-sm text-gray-500">
                                <span className="flex items-center">
                                  <CheckCircle className="h-4 w-4 text-emerald-600 mr-1" />
                                  {faq.helpful} helpful
                                </span>
                                <span className="flex items-center">
                                  <XCircle className="h-4 w-4 text-red-600 mr-1" />
                                  {faq.notHelpful} not helpful
                                </span>
                              </div>
                              <button className="group flex items-center px-3 py-1 text-amber-600 hover:text-orange-600 hover:bg-amber-50 rounded-lg transition-all duration-300">
                                <span className="text-sm font-medium">Was this helpful?</span>
                                <ChevronRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" />
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Knowledge Base Articles */}
                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-br from-white/40 to-white/60 backdrop-blur-xl rounded-2xl border border-white/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
                  <div className="relative p-6">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-gradient-to-br from-purple-400 to-indigo-500 rounded-xl flex items-center justify-center shadow-lg">
                        <FileText className="h-5 w-5 text-white drop-shadow-lg" />
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-gray-800">Knowledge Base Articles</h3>
                        <p className="text-sm text-gray-600">
                          Comprehensive guides and documentation
                        </p>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {knowledgeBase.map((article) => (
                        <div key={article.id} className="relative group">
                          <div className="absolute inset-0 bg-gradient-to-br from-white/60 to-white/80 backdrop-blur-sm rounded-xl border border-white/70 shadow-lg group-hover:shadow-xl transition-all duration-300" />
                          <div className="relative p-4">
                            <div className="flex items-start justify-between mb-3">
                              <h4 className="font-bold text-gray-800 flex-1">{article.title}</h4>
                              <span className="px-2 py-1 text-xs font-bold bg-gradient-to-r from-purple-400/20 to-indigo-500/20 text-purple-800 border border-purple-200/50 rounded-full">
                                {article.category}
                              </span>
                            </div>
                            <p className="text-gray-600 text-sm mb-4">{article.content}</p>
                            <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                              <span>{article.views} views</span>
                              <span>Updated: {article.lastUpdated}</span>
                            </div>
                            <div className="flex flex-wrap gap-1 mb-4">
                              {article.tags.map((tag) => (
                                <span
                                  key={tag}
                                  className="px-2 py-1 text-xs bg-gradient-to-r from-amber-400/20 to-orange-500/20 text-amber-800 border border-amber-200/50 rounded-full"
                                >
                                  #{tag}
                                </span>
                              ))}
                            </div>
                            <button className="w-full group flex items-center justify-center px-4 py-2 bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 text-white rounded-xl hover:from-amber-500 hover:via-orange-600 hover:to-red-600 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-orange-400/50">
                              <span className="font-bold">Read Article</span>
                              <ExternalLink className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Contact Us Tab */}
            {activeTab === 'contact' && (
              <div className="space-y-6">
                {/* Contact Methods */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[
                    {
                      title: 'Phone Support',
                      description: 'Call us for immediate assistance',
                      icon: Phone,
                      color: 'from-emerald-400 to-green-500',
                      contact: '+****************',
                      availability: '24/7 Available',
                      action: 'Call Now',
                    },
                    {
                      title: 'Email Support',
                      description: 'Send us a detailed message',
                      icon: Mail,
                      color: 'from-blue-400 to-indigo-500',
                      contact: '<EMAIL>',
                      availability: 'Response within 2 hours',
                      action: 'Send Email',
                    },
                    {
                      title: 'Live Chat',
                      description: 'Chat with our support team',
                      icon: MessageSquare,
                      color: 'from-amber-400 to-orange-500',
                      contact: 'Start Chat',
                      availability: 'Mon-Fri 9AM-6PM',
                      action: 'Start Chat',
                    },
                  ].map((method, index) => (
                    <div key={index} className="relative group">
                      <div className="absolute inset-0 bg-gradient-to-br from-white/40 to-white/60 backdrop-blur-xl rounded-2xl border border-white/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
                      <div className="relative p-6">
                        <div className="flex items-center space-x-3 mb-4">
                          <div
                            className={`w-12 h-12 bg-gradient-to-br ${method.color} rounded-xl flex items-center justify-center shadow-lg`}
                          >
                            <method.icon className="h-6 w-6 text-white drop-shadow-lg" />
                          </div>
                          <div>
                            <h4 className="font-bold text-gray-800">{method.title}</h4>
                            <p className="text-sm text-gray-600">{method.description}</p>
                          </div>
                        </div>
                        <div className="mb-4">
                          <p className="text-lg font-bold text-gray-800 mb-1">{method.contact}</p>
                          <p className="text-sm text-gray-600">{method.availability}</p>
                        </div>
                        <button className="w-full group flex items-center justify-center px-4 py-2 bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 text-white rounded-xl hover:from-amber-500 hover:via-orange-600 hover:to-red-600 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-orange-400/50">
                          <span className="font-bold">{method.action}</span>
                          <ChevronRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Contact Form */}
                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-br from-white/40 to-white/60 backdrop-blur-xl rounded-2xl border border-white/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
                  <div className="relative p-6">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="w-10 h-10 bg-gradient-to-br from-amber-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg">
                        <FileText className="h-5 w-5 text-white drop-shadow-lg" />
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-gray-800">Send us a Message</h3>
                        <p className="text-sm text-gray-600">
                          Fill out the form below and we&apos;ll get back to you
                        </p>
                      </div>
                    </div>
                    <form className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="relative group">
                          <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-orange-500/20 rounded-xl blur-sm group-focus-within:blur-md group-focus-within:from-amber-400/30 group-focus-within:to-orange-500/30 transition-all duration-300" />
                          <div className="relative">
                            <input
                              type="text"
                              placeholder="Your Name"
                              className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg focus:shadow-xl"
                            />
                          </div>
                        </div>
                        <div className="relative group">
                          <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-orange-500/20 rounded-xl blur-sm group-focus-within:blur-md group-focus-within:from-amber-400/30 group-focus-within:to-orange-500/30 transition-all duration-300" />
                          <div className="relative">
                            <input
                              type="email"
                              placeholder="Your Email"
                              className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg focus:shadow-xl"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="relative group">
                        <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-orange-500/20 rounded-xl blur-sm group-focus-within:blur-md group-focus-within:from-amber-400/30 group-focus-within:to-orange-500/30 transition-all duration-300" />
                        <div className="relative">
                          <select className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 shadow-lg focus:shadow-xl">
                            <option value="">Select Category</option>
                            <option value="technical">Technical Support</option>
                            <option value="billing">Billing & Payments</option>
                            <option value="account">Account Management</option>
                            <option value="feature">Feature Request</option>
                            <option value="other">Other</option>
                          </select>
                        </div>
                      </div>
                      <div className="relative group">
                        <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-orange-500/20 rounded-xl blur-sm group-focus-within:blur-md group-focus-within:from-amber-400/30 group-focus-within:to-orange-500/30 transition-all duration-300" />
                        <div className="relative">
                          <textarea
                            placeholder="Describe your issue or question..."
                            rows={4}
                            className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg focus:shadow-xl resize-none"
                          />
                        </div>
                      </div>
                      <div className="flex justify-end">
                        <button className="group flex items-center px-8 py-3 bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 text-white rounded-xl hover:from-amber-500 hover:via-orange-600 hover:to-red-600 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-orange-400/50">
                          <span className="font-bold">Send Message</span>
                          <Zap className="h-4 w-4 ml-2 animate-pulse" />
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  )
}
