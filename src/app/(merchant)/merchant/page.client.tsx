'use client'

import React from 'react'
import { useRouter } from 'next/navigation'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  ChefHat,
  BarChart3,
  Users,
  Smartphone,
  CheckCircle,
  PlayCircle,
  Star,
  ArrowRight,
  TrendingUp,
  Clock,
  Shield,
  Phone,
  Mail,
  MapPin,
  Facebook,
  Twitter,
  Instagram,
  Youtube,
  Linkedin,
  LogIn,
  UserPlus,
} from 'lucide-react'

const MerchantPageClient = () => {
  const router = useRouter()
  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50">
      {/* Hero Section */}
      <section className="relative overflow-hidden min-h-screen flex items-center">
        {/* Dynamic Background */}
        <div className="absolute inset-0">
          {/* Primary Background Image */}
          <div
            className="absolute inset-0 bg-cover bg-center bg-no-repeat scale-105 transition-transform duration-[20s] ease-linear hover:scale-110"
            style={{
              backgroundImage:
                'url("https://images.unsplash.com/photo-1554118811-1e0d58224f24?ixlib=rb-4.0.3&auto=format&fit=crop&w=2047&q=80")',
            }}
          />

          {/* Gradient Overlays */}
          <div className="absolute inset-0 bg-gradient-to-br from-black/70 via-amber-900/50 to-orange-900/70"></div>
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent"></div>

          {/* Animated Mesh Background */}
          <div className="absolute inset-0 opacity-30">
            <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-amber-500/20 to-orange-500/20 animate-pulse"></div>
            <div className="absolute top-20 left-20 w-96 h-96 bg-amber-400/10 rounded-full blur-3xl animate-float"></div>
            <div className="absolute bottom-32 right-20 w-80 h-80 bg-orange-400/10 rounded-full blur-3xl animate-float-delayed"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-radial from-amber-300/5 to-transparent rounded-full animate-spin-slow"></div>
          </div>
        </div>

        {/* Floating Particles */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-amber-300 rounded-full animate-ping"></div>
          <div className="absolute top-3/4 left-3/4 w-1 h-1 bg-orange-300 rounded-full animate-ping delay-1000"></div>
          <div className="absolute top-1/2 left-1/6 w-1.5 h-1.5 bg-yellow-300 rounded-full animate-ping delay-2000"></div>
          <div className="absolute top-1/6 right-1/4 w-2 h-2 bg-amber-400 rounded-full animate-ping delay-500"></div>
        </div>

        <div className="relative z-20 container mx-auto px-4">
          <div className="max-w-6xl mx-auto text-center text-white">
            {/* Announcement Badge */}
            <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-md border border-white/20 rounded-full text-sm font-medium mb-8 animate-fade-in-down">
              <span className="w-2 h-2 bg-green-400 rounded-full mr-3 animate-pulse"></span>
              🎉 Join 1000+ successful restaurants
            </div>

            <div className="space-y-8">
              {/* Main Headline */}
              <h1 className="text-5xl md:text-7xl lg:text-8xl font-black leading-[0.9] tracking-tight animate-fade-in-up">
                <span className="block">Transform Your</span>
                <span className="block bg-gradient-to-r from-amber-300 via-orange-300 to-red-300 bg-clip-text text-transparent animate-gradient-x">
                  Restaurant
                </span>
                <span className="block text-4xl md:text-5xl lg:text-6xl font-light text-gray-200 mt-2">
                  Into a Digital Empire
                </span>
              </h1>

              {/* Subtitle */}
              <p className="text-xl md:text-2xl lg:text-3xl text-gray-300 max-w-4xl mx-auto leading-relaxed animate-fade-in-up delay-200 font-light">
                Streamline operations, boost sales, and create unforgettable customer experiences
                with our all-in-one restaurant management platform.
              </p>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-6 justify-center items-center pt-4 animate-fade-in-up delay-400">
                <button
                  onClick={() => router.push('/merchant/dashboard')}
                  className="group relative px-10 py-5 bg-gradient-to-r from-amber-500 to-orange-500 text-white font-bold text-lg rounded-2xl hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 hover:-translate-y-1 transition-all duration-300 shadow-2xl overflow-hidden"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative flex items-center">
                    <ChefHat className="mr-3 h-6 w-6 group-hover:rotate-12 transition-transform duration-300" />
                    Access Dashboard
                    <ArrowRight className="ml-3 h-5 w-5 group-hover:translate-x-2 transition-transform duration-300" />
                  </div>
                  <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-yellow-400 to-red-400 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
                </button>

                <button className="group px-10 py-5 bg-white/10 backdrop-blur-md border-2 border-white/30 text-white font-semibold text-lg rounded-2xl hover:bg-white/20 hover:border-white/50 transform hover:scale-105 transition-all duration-300">
                  <div className="flex items-center">
                    <svg
                      className="mr-3 h-6 w-6 group-hover:scale-110 transition-transform duration-300"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 10V3L4 14h7v7l9-11h-7z"
                      />
                    </svg>
                    Watch Demo
                  </div>
                </button>
              </div>

              {/* Login/Register Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-8 animate-fade-in-up delay-600">
                <p className="text-gray-300 text-sm">Quick Access:</p>
                <div className="flex gap-3">
                  <button
                    onClick={() => router.push('/merchant/dashboard')}
                    className="flex items-center px-6 py-2 bg-white/20 backdrop-blur-md border border-white/40 text-white font-medium rounded-xl hover:bg-white/30 transform hover:scale-105 transition-all duration-300"
                  >
                    <LogIn className="h-4 w-4 mr-2" />
                    Login
                  </button>
                  <button
                    onClick={() => router.push('/merchant/dashboard')}
                    className="flex items-center px-6 py-2 bg-white text-gray-900 font-medium rounded-xl hover:bg-gray-100 transform hover:scale-105 transition-all duration-300"
                  >
                    <UserPlus className="h-4 w-4 mr-2" />
                    Register
                  </button>
                </div>
              </div>

              {/* Trust Indicators */}
              <div className="flex flex-wrap justify-center items-center gap-8 pt-8 text-gray-400 text-sm animate-fade-in-up delay-600">
                <div className="flex items-center">
                  <svg
                    className="w-5 h-5 mr-2 text-green-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  No credit card required
                </div>
                <div className="flex items-center">
                  <svg
                    className="w-5 h-5 mr-2 text-green-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  Setup in 5 minutes
                </div>
                <div className="flex items-center">
                  <svg
                    className="w-5 h-5 mr-2 text-green-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  24/7 support included
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Arrow Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/60 animate-bounce">
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 14l-7 7m0 0l-7-7m7 7V3"
            />
          </svg>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-32 bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50 relative overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-72 h-72 bg-blue-200/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-indigo-200/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-purple-200/15 rounded-full blur-2xl animate-pulse delay-2000"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-20">
            <div className="inline-flex items-center px-6 py-3 bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-full text-sm font-medium mb-8 shadow-lg text-black">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 animate-pulse "></span>⚡
              Powerful Features
            </div>
            <h2 className="text-5xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-8 leading-tight">
              Why Choose
              <span className="block bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
                Our Platform?
              </span>
            </h2>
            <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              Everything you need to transform your restaurant into a modern, efficient, and
              profitable business
            </p>
          </div>

          <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-8 max-w-7xl mx-auto">
            {/* Card 1 - Order Management */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-indigo-600/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500 opacity-0 group-hover:opacity-100"></div>
              <Card className="relative bg-white/90 backdrop-blur-sm border-0 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 rounded-3xl overflow-hidden h-full">
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-indigo-500"></div>
                <CardHeader className="text-center p-8">
                  <div className="mx-auto mb-6 p-5 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-3xl w-20 h-20 flex items-center justify-center group-hover:scale-110 group-hover:rotate-12 transition-all duration-500 shadow-lg">
                    <Clock className="h-10 w-10 text-white" />
                  </div>
                  <CardTitle className="text-2xl font-bold text-gray-900 mb-4">
                    Lightning Fast Orders
                  </CardTitle>
                </CardHeader>
                <CardContent className="px-8 pb-8">
                  <CardDescription className="text-center text-gray-600 text-lg leading-relaxed">
                    Streamline your ordering process with instant notifications, real-time updates,
                    and automated workflows.
                  </CardDescription>
                  <div className="mt-6 space-y-3">
                    <div className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                      Real-time order tracking
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                      Instant notifications
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                      Queue management
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Card 2 - Analytics */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-br from-emerald-400/20 to-green-600/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500 opacity-0 group-hover:opacity-100"></div>
              <Card className="relative bg-white/90 backdrop-blur-sm border-0 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 rounded-3xl overflow-hidden h-full">
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-emerald-500 to-green-500"></div>
                <CardHeader className="text-center p-8">
                  <div className="mx-auto mb-6 p-5 bg-gradient-to-br from-emerald-500 to-green-600 rounded-3xl w-20 h-20 flex items-center justify-center group-hover:scale-110 group-hover:rotate-12 transition-all duration-500 shadow-lg">
                    <BarChart3 className="h-10 w-10 text-white" />
                  </div>
                  <CardTitle className="text-2xl font-bold text-gray-900 mb-4">
                    Smart Analytics
                  </CardTitle>
                </CardHeader>
                <CardContent className="px-8 pb-8">
                  <CardDescription className="text-center text-gray-600 text-lg leading-relaxed">
                    Make data-driven decisions with comprehensive insights, trends, and performance
                    metrics.
                  </CardDescription>
                  <div className="mt-6 space-y-3">
                    <div className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
                      Sales & revenue tracking
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
                      Customer behavior insights
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
                      Popular item analysis
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Card 3 - Customer Engagement */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-400/20 to-pink-600/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500 opacity-0 group-hover:opacity-100"></div>
              <Card className="relative bg-white/90 backdrop-blur-sm border-0 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 rounded-3xl overflow-hidden h-full">
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 to-pink-500"></div>
                <CardHeader className="text-center p-8">
                  <div className="mx-auto mb-6 p-5 bg-gradient-to-br from-purple-500 to-pink-600 rounded-3xl w-20 h-20 flex items-center justify-center group-hover:scale-110 group-hover:rotate-12 transition-all duration-500 shadow-lg">
                    <Users className="h-10 w-10 text-white" />
                  </div>
                  <CardTitle className="text-2xl font-bold text-gray-900 mb-4">
                    Customer Loyalty
                  </CardTitle>
                </CardHeader>
                <CardContent className="px-8 pb-8">
                  <CardDescription className="text-center text-gray-600 text-lg leading-relaxed">
                    Build lasting relationships with personalized experiences, rewards, and
                    engagement tools.
                  </CardDescription>
                  <div className="mt-6 space-y-3">
                    <div className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                      Loyalty programs
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                      Personalized offers
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                      Customer feedback
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Card 4 - Easy to Use */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-br from-orange-400/20 to-red-600/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500 opacity-0 group-hover:opacity-100"></div>
              <Card className="relative bg-white/90 backdrop-blur-sm border-0 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 rounded-3xl overflow-hidden h-full">
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-orange-500 to-red-500"></div>
                <CardHeader className="text-center p-8">
                  <div className="mx-auto mb-6 p-5 bg-gradient-to-br from-orange-500 to-red-600 rounded-3xl w-20 h-20 flex items-center justify-center group-hover:scale-110 group-hover:rotate-12 transition-all duration-500 shadow-lg">
                    <Smartphone className="h-10 w-10 text-white" />
                  </div>
                  <CardTitle className="text-2xl font-bold text-gray-900 mb-4">
                    Simple & Intuitive
                  </CardTitle>
                </CardHeader>
                <CardContent className="px-8 pb-8">
                  <CardDescription className="text-center text-gray-600 text-lg leading-relaxed">
                    No technical expertise required. Beautiful, user-friendly interface that anyone
                    can master.
                  </CardDescription>
                  <div className="mt-6 space-y-3">
                    <div className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-orange-500 rounded-full mr-3"></div>
                      Drag & drop setup
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-orange-500 rounded-full mr-3"></div>
                      Mobile responsive
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-orange-500 rounded-full mr-3"></div>
                      24/7 support included
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Bottom CTA */}
          <div className="text-center mt-20">
            <div className="inline-flex flex-col sm:flex-row gap-6 items-center">
              <button className="px-10 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold text-lg rounded-2xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-xl">
                Explore All Features
              </button>
              <button className="px-10 py-4 border-2 border-gray-300 text-gray-700 font-semibold text-lg rounded-2xl hover:border-blue-500 hover:text-blue-600 transition-all duration-300 backdrop-blur-sm">
                See Live Demo
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Demo Section */}
      <section className="py-32 bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900 relative overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-radial from-indigo-500/5 to-transparent rounded-full"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full text-sm font-medium mb-8 text-white">
                <span className="w-2 h-2 bg-blue-400 rounded-full mr-3 animate-pulse"></span>
                🚀 Live Dashboard Preview
              </div>
              <h2 className="text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-8 leading-tight">
                Your Restaurant
                <span className="block bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  Command Center
                </span>
              </h2>
              <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
                Experience the power of real-time insights, streamlined operations, and data-driven
                decisions all in one beautiful interface
              </p>
            </div>

            <div className="relative perspective-1000">
              {/* Dashboard Container */}
              <div className="relative transform hover:scale-105 transition-all duration-700 hover:rotate-x-5">
                <div className="bg-gradient-to-br from-white via-gray-50 to-blue-50/50 rounded-3xl shadow-2xl overflow-hidden border border-gray-200/50 backdrop-blur-sm">
                  {/* Browser Header */}
                  <div className="bg-gradient-to-r from-slate-800 via-gray-800 to-slate-900 p-6 flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-4 h-4 bg-red-400 rounded-full animate-pulse"></div>
                      <div className="w-4 h-4 bg-yellow-400 rounded-full animate-pulse delay-200"></div>
                      <div className="w-4 h-4 bg-green-400 rounded-full animate-pulse delay-400"></div>
                    </div>
                    <div className="flex-1 mx-6">
                      <div className="bg-gray-700 rounded-lg px-4 py-2 text-sm text-gray-300 text-center">
                        dashboard.yourrestaurant.com
                      </div>
                    </div>
                    <div className="text-gray-300 text-sm font-medium">Restaurant Dashboard</div>
                  </div>

                  {/* Dashboard Content */}
                  <div className="p-8 lg:p-12">
                    {/* Top Stats Row */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-10">
                      <div className="group bg-gradient-to-br from-emerald-50 to-green-50 p-6 rounded-2xl border border-emerald-100 hover:shadow-lg transition-all duration-300">
                        <div className="flex items-center justify-between mb-4">
                          <div className="p-3 bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl">
                            <TrendingUp className="h-6 w-6 text-white" />
                          </div>
                          <span className="text-xs font-semibold text-emerald-600 bg-emerald-100 px-2 py-1 rounded-full">
                            +24%
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mb-1">Today&apos;s Revenue</p>
                        <p className="text-3xl font-bold text-gray-900">$2,847</p>
                      </div>

                      <div className="group bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-2xl border border-blue-100 hover:shadow-lg transition-all duration-300">
                        <div className="flex items-center justify-between mb-4">
                          <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl">
                            <Clock className="h-6 w-6 text-white" />
                          </div>
                          <span className="text-xs font-semibold text-blue-600 bg-blue-100 px-2 py-1 rounded-full">
                            Live
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mb-1">Active Orders</p>
                        <p className="text-3xl font-bold text-gray-900">47</p>
                      </div>

                      <div className="group bg-gradient-to-br from-purple-50 to-pink-50 p-6 rounded-2xl border border-purple-100 hover:shadow-lg transition-all duration-300">
                        <div className="flex items-center justify-between mb-4">
                          <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl">
                            <Users className="h-6 w-6 text-white" />
                          </div>
                          <span className="text-xs font-semibold text-purple-600 bg-purple-100 px-2 py-1 rounded-full">
                            +12
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mb-1">Happy Customers</p>
                        <p className="text-3xl font-bold text-gray-900">324</p>
                      </div>

                      <div className="group bg-gradient-to-br from-orange-50 to-red-50 p-6 rounded-2xl border border-orange-100 hover:shadow-lg transition-all duration-300">
                        <div className="flex items-center justify-between mb-4">
                          <div className="p-3 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl">
                            <BarChart3 className="h-6 w-6 text-white" />
                          </div>
                          <span className="text-xs font-semibold text-orange-600 bg-orange-100 px-2 py-1 rounded-full">
                            +8%
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mb-1">Avg Order Value</p>
                        <p className="text-3xl font-bold text-gray-900">$28.50</p>
                      </div>
                    </div>

                    {/* Chart Section */}
                    <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 mb-8">
                      <div className="flex items-center justify-between mb-6">
                        <h3 className="text-xl font-bold text-gray-900">Weekly Performance</h3>
                        <div className="flex space-x-2">
                          <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                          <div className="w-3 h-3 bg-emerald-500 rounded-full"></div>
                          <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                        </div>
                      </div>
                      <div className="h-32 bg-gradient-to-r from-blue-100 via-emerald-100 to-purple-100 rounded-xl flex items-end justify-between p-4">
                        <div className="bg-blue-500 w-8 h-16 rounded-t-lg"></div>
                        <div className="bg-emerald-500 w-8 h-20 rounded-t-lg"></div>
                        <div className="bg-purple-500 w-8 h-12 rounded-t-lg"></div>
                        <div className="bg-blue-500 w-8 h-24 rounded-t-lg"></div>
                        <div className="bg-emerald-500 w-8 h-28 rounded-t-lg"></div>
                        <div className="bg-purple-500 w-8 h-18 rounded-t-lg"></div>
                        <div className="bg-blue-500 w-8 h-22 rounded-t-lg"></div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                      <button className="group px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-bold text-lg rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 shadow-xl">
                        <PlayCircle className="mr-3 h-6 w-6 group-hover:scale-110 transition-transform duration-300" />
                        Try Interactive Demo
                      </button>
                      <button className="px-8 py-4 bg-white/90 text-gray-700 font-semibold text-lg rounded-xl hover:bg-white transition-all duration-300 border border-gray-200 hover:shadow-lg">
                        <Smartphone className="mr-3 h-5 w-5" />
                        View on Mobile
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full opacity-20 animate-bounce"></div>
              <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-full opacity-20 animate-bounce delay-1000"></div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-24 bg-gradient-to-br from-slate-50 via-white to-blue-50 relative overflow-hidden">
        {/* Background decorations */}
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-20 left-10 w-72 h-72 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
          <div className="absolute top-40 right-10 w-72 h-72 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse delay-1000"></div>
          <div className="absolute bottom-20 left-1/2 w-72 h-72 bg-green-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse delay-2000"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-20">
            <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-6">
              ✨ Simple & Efficient
            </div>
            <h2 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
              How It
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                {' '}
                Works
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Transform your restaurant into a digital powerhouse with our streamlined 4-step
              process
            </p>
          </div>

          <div className="max-w-7xl mx-auto">
            <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-8">
              {/* Step 1 */}
              <div className="group relative">
                <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 h-full">
                  <div className="relative mb-8">
                    <div className="mx-auto w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-all duration-300 shadow-lg rotate-3 group-hover:rotate-6">
                      <svg
                        className="w-10 h-10 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                        />
                      </svg>
                    </div>
                    {/* Connecting line */}
                    <div className="absolute top-10 -right-4 w-8 h-0.5 bg-gradient-to-r from-blue-500 to-indigo-500 hidden lg:block opacity-50"></div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <span className="inline-flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full text-sm font-bold">
                        1
                      </span>
                      <h3 className="text-xl font-bold text-gray-900">Register Account</h3>
                    </div>
                    <p className="text-gray-600 leading-relaxed">
                      Create your merchant account in minutes with our simple registration process.
                      Get verified instantly and start your digital journey.
                    </p>
                  </div>
                </div>
              </div>

              {/* Step 2 */}
              <div className="group relative">
                <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 h-full">
                  <div className="relative mb-8">
                    <div className="mx-auto w-20 h-20 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-all duration-300 shadow-lg -rotate-3 group-hover:-rotate-6">
                      <svg
                        className="w-10 h-10 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                        />
                      </svg>
                    </div>
                    <div className="absolute top-10 -right-4 w-8 h-0.5 bg-gradient-to-r from-emerald-500 to-green-500 hidden lg:block opacity-50"></div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <span className="inline-flex items-center justify-center w-8 h-8 bg-emerald-100 text-emerald-600 rounded-full text-sm font-bold">
                        2
                      </span>
                      <h3 className="text-xl font-bold text-gray-900">Setup Store</h3>
                    </div>
                    <p className="text-gray-600 leading-relaxed">
                      Add your restaurant details, upload your menu, set operating hours, and
                      customize your digital storefront to match your brand.
                    </p>
                  </div>
                </div>
              </div>

              {/* Step 3 */}
              <div className="group relative">
                <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 h-full">
                  <div className="relative mb-8">
                    <div className="mx-auto w-20 h-20 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-all duration-300 shadow-lg rotate-3 group-hover:rotate-6">
                      <svg
                        className="w-10 h-10 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
                        />
                      </svg>
                    </div>
                    <div className="absolute top-10 -right-4 w-8 h-0.5 bg-gradient-to-r from-orange-500 to-red-500 hidden lg:block opacity-50"></div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <span className="inline-flex items-center justify-center w-8 h-8 bg-orange-100 text-orange-600 rounded-full text-sm font-bold">
                        3
                      </span>
                      <h3 className="text-xl font-bold text-gray-900">Receive Orders</h3>
                    </div>
                    <p className="text-gray-600 leading-relaxed">
                      Start receiving orders instantly through our app, QR codes, or web platform.
                      Manage everything from one intuitive dashboard.
                    </p>
                  </div>
                </div>
              </div>

              {/* Step 4 */}
              <div className="group relative">
                <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 h-full">
                  <div className="relative mb-8">
                    <div className="mx-auto w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-all duration-300 shadow-lg -rotate-3 group-hover:-rotate-6">
                      <svg
                        className="w-10 h-10 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <span className="inline-flex items-center justify-center w-8 h-8 bg-purple-100 text-purple-600 rounded-full text-sm font-bold">
                        4
                      </span>
                      <h3 className="text-xl font-bold text-gray-900">Get Paid Fast</h3>
                    </div>
                    <p className="text-gray-600 leading-relaxed">
                      Enjoy transparent, lightning-fast payments with competitive rates. Track your
                      earnings in real-time with detailed analytics.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Call to action */}
            <div className="text-center mt-16">
              <div className="inline-flex flex-col sm:flex-row gap-4 items-center">
                <button className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg">
                  Start Your Journey Today
                </button>
                <span className="text-gray-500">or</span>
                <button className="px-8 py-4 border-2 border-gray-300 text-gray-700 font-semibold rounded-xl hover:border-blue-500 hover:text-blue-600 transition-all duration-300">
                  Schedule a Demo
                </button>
              </div>
              <p className="text-sm text-gray-500 mt-4">
                No setup fees • Cancel anytime • 24/7 support
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-32 bg-gradient-to-br from-orange-50 via-amber-50/50 to-yellow-50/30 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-amber-200/20 to-orange-300/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 left-20 w-80 h-80 bg-gradient-to-br from-yellow-200/20 to-orange-200/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-orange-200/15 to-amber-300/15 rounded-full blur-2xl"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-20">
            <div className="inline-flex items-center px-6 py-3 bg-white/80 backdrop-blur-sm border border-orange-200/50 rounded-full text-sm font-medium mb-8 shadow-lg text-black">
              <span className="w-2 h-2 bg-orange-500 rounded-full mr-3 animate-pulse"></span>
              💫 Success Stories
            </div>
            <h2 className="text-5xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-8 leading-tight">
              Loved by
              <span className="block bg-gradient-to-r from-amber-600 via-orange-600 to-red-600 bg-clip-text text-transparent">
                Restaurant Owners
              </span>
            </h2>
            <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              Join thousands of successful restaurants transforming their business with our platform
            </p>
          </div>

          <div className="max-w-7xl mx-auto">
            <div className="grid lg:grid-cols-3 md:grid-cols-2 gap-8">
              {/* Testimonial 1 */}
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-indigo-600/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500 opacity-0 group-hover:opacity-100"></div>
                <Card className="relative bg-white/95 backdrop-blur-sm border-0 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 rounded-3xl overflow-hidden h-full">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-indigo-500"></div>
                  <CardContent className="p-8">
                    {/* Quote Icon */}
                    <div className="mb-6">
                      <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                        <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z" />
                        </svg>
                      </div>
                      <div className="flex mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                        ))}
                      </div>
                    </div>

                    <p className="text-gray-700 mb-8 italic text-lg leading-relaxed">
                      &ldquo;Since using this platform, our team saves hours daily and customers
                      love the faster service. Our revenue increased by 35% in just 3 months!&rdquo;
                    </p>

                    <div className="flex items-center">
                      <div className="relative">
                        <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                          <span className="text-white font-bold text-lg">SM</span>
                        </div>
                        <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white"></div>
                      </div>
                      <div>
                        <p className="font-bold text-gray-900 text-lg">Sarah Martinez</p>
                        <p className="text-gray-600">Owner, Brew & Beans Café</p>
                        <div className="flex items-center mt-1">
                          <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                            Verified Customer
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Testimonial 2 */}
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-400/20 to-green-600/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500 opacity-0 group-hover:opacity-100"></div>
                <Card className="relative bg-white/95 backdrop-blur-sm border-0 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 rounded-3xl overflow-hidden h-full">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-emerald-500 to-green-500"></div>
                  <CardContent className="p-8">
                    {/* Quote Icon */}
                    <div className="mb-6">
                      <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                        <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z" />
                        </svg>
                      </div>
                      <div className="flex mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                        ))}
                      </div>
                    </div>

                    <p className="text-gray-700 mb-8 italic text-lg leading-relaxed">
                      &ldquo;The analytics dashboard is incredible. I can see exactly what&apos;s
                      working and what&apos;s not. Best investment we&apos;ve made for our
                      restaurant.&rdquo;
                    </p>

                    <div className="flex items-center">
                      <div className="relative">
                        <div className="w-14 h-14 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                          <span className="text-white font-bold text-lg">MJ</span>
                        </div>
                        <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white"></div>
                      </div>
                      <div>
                        <p className="font-bold text-gray-900 text-lg">Michael Johnson</p>
                        <p className="text-gray-600">Manager, Urban Bistro</p>
                        <div className="flex items-center mt-1">
                          <span className="text-xs bg-emerald-100 text-emerald-700 px-2 py-1 rounded-full">
                            Verified Customer
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Testimonial 3 */}
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-400/20 to-pink-600/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500 opacity-0 group-hover:opacity-100"></div>
                <Card className="relative bg-white/95 backdrop-blur-sm border-0 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 rounded-3xl overflow-hidden h-full lg:col-span-1 md:col-span-2">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 to-pink-500"></div>
                  <CardContent className="p-8">
                    {/* Quote Icon */}
                    <div className="mb-6">
                      <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                        <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z" />
                        </svg>
                      </div>
                      <div className="flex mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                        ))}
                      </div>
                    </div>

                    <p className="text-gray-700 mb-8 italic text-lg leading-relaxed">
                      &ldquo;Setup was so easy! Within an hour we were taking digital orders. Our
                      customers love the convenience and we love the efficiency.&rdquo;
                    </p>

                    <div className="flex items-center">
                      <div className="relative">
                        <div className="w-14 h-14 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                          <span className="text-white font-bold text-lg">AL</span>
                        </div>
                        <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white"></div>
                      </div>
                      <div>
                        <p className="font-bold text-gray-900 text-lg">Anna Lee</p>
                        <p className="text-gray-600">Co-founder, Morning Glory Coffee</p>
                        <div className="flex items-center mt-1">
                          <span className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full">
                            Verified Customer
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Bottom Stats */}
            <div className="mt-20 text-center">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
                <div className="group">
                  <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform duration-300">
                    1000+
                  </div>
                  <p className="text-gray-600 font-medium">Happy Restaurants</p>
                </div>
                <div className="group">
                  <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform duration-300">
                    4.9/5
                  </div>
                  <p className="text-gray-600 font-medium">Average Rating</p>
                </div>
                <div className="group">
                  <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform duration-300">
                    35%
                  </div>
                  <p className="text-gray-600 font-medium">Revenue Increase</p>
                </div>
                <div className="group">
                  <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform duration-300">
                    24/7
                  </div>
                  <p className="text-gray-600 font-medium">Support Available</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Preview */}
      <section className="py-32 bg-gradient-to-br from-slate-900 via-gray-900 to-black relative overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-96 h-96 bg-emerald-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-80 h-80 bg-teal-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-radial from-emerald-500/5 to-transparent rounded-full animate-spin-slow"></div>
        </div>

        {/* Grid Background */}
        <div className="absolute inset-0 opacity-20">
          <div
            className="h-full w-full bg-gradient-to-br from-emerald-500/5 to-teal-500/5"
            style={{
              backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0)`,
              backgroundSize: '20px 20px',
            }}
          ></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-6xl mx-auto">
            {/* Header */}
            <div className="text-center mb-20">
              <div className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 backdrop-blur-sm border border-emerald-400/30 rounded-full text-sm font-medium mb-8 text-white shadow-xl">
                <Shield className="h-5 w-5 text-emerald-400 mr-3" />
                <span className="bg-gradient-to-r from-emerald-300 to-teal-300 bg-clip-text text-transparent font-bold">
                  🚀 COMPLETELY FREE FOREVER
                </span>
              </div>
              <h2 className="text-5xl md:text-6xl lg:text-8xl font-bold text-white mb-8 leading-tight">
                Everything You Need
                <span className="block bg-gradient-to-r from-emerald-400 via-teal-400 to-cyan-400 bg-clip-text text-transparent animate-gradient-x">
                  Absolutely Free
                </span>
              </h2>
              <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
                No hidden fees, no premium features locked away. Get the complete restaurant
                management platform at zero cost.
              </p>
            </div>

            {/* Pricing Card */}
            <div className="max-w-2xl mx-auto">
              <div className="relative group">
                {/* Animated Glow Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-emerald-400 via-teal-400 to-cyan-400 rounded-3xl blur-2xl opacity-30 group-hover:opacity-50 transition-all duration-700 animate-pulse"></div>

                <Card className="relative bg-gradient-to-br from-white via-emerald-50/90 to-teal-50/90 border-0 shadow-2xl hover:shadow-3xl transition-all duration-700 rounded-3xl overflow-hidden backdrop-blur-sm group-hover:scale-105">
                  {/* Top Gradient Bar */}
                  <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-emerald-400 via-teal-400 to-cyan-400"></div>

                  {/* Floating Elements */}
                  <div className="absolute top-8 right-8 w-3 h-3 bg-emerald-400 rounded-full animate-ping"></div>
                  <div className="absolute top-16 left-8 w-2 h-2 bg-teal-400 rounded-full animate-ping delay-500"></div>
                  <div className="absolute bottom-16 right-12 w-2.5 h-2.5 bg-cyan-400 rounded-full animate-ping delay-1000"></div>

                  {/* Featured Badge */}
                  {/* <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-amber-400 to-orange-500 text-white px-8 py-3 rounded-full text-sm font-bold shadow-xl rotate-1 group-hover:rotate-2 transition-transform duration-300">
                    ⭐ MOST POPULAR
                  </div> */}

                  <CardHeader className="text-center p-12 pt-16 relative z-10">
                    <div className="w-24 h-24 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-xl group-hover:scale-110 group-hover:rotate-6 transition-all duration-500">
                      <ChefHat className="h-12 w-12 text-white" />
                    </div>
                    <CardTitle className="text-4xl font-bold text-gray-900 mb-4">
                      Complete Restaurant Platform
                    </CardTitle>
                    <CardDescription className="text-xl text-gray-600 mb-8">
                      Every feature you need to run a modern restaurant
                    </CardDescription>

                    {/* Price Display */}
                    <div className="relative mb-8">
                      <div className="flex items-center justify-center">
                        <span className="text-8xl font-black bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                          $0
                        </span>
                        <div className="ml-4 text-left">
                          <p className="text-gray-500 text-lg line-through">$199/mo</p>
                          <p className="text-gray-600 font-semibold">Forever Free</p>
                        </div>
                      </div>
                      <div className="absolute -top-8 -right-8 bg-gradient-to-r from-red-500 to-pink-500 text-white px-4 py-2 rounded-full text-sm font-bold transform rotate-12 animate-bounce">
                        SAVE $2,388/year
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="p-12 pt-0 relative z-10">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-10">
                      <div className="flex items-center p-4 bg-white/80 rounded-2xl shadow-sm border border-emerald-100/70 group-hover:shadow-md transition-all duration-300">
                        <CheckCircle className="h-6 w-6 text-emerald-500 mr-3 flex-shrink-0" />
                        <span className="font-semibold text-gray-800">
                          Unlimited orders & customers
                        </span>
                      </div>
                      <div className="flex items-center p-4 bg-white/80 rounded-2xl shadow-sm border border-emerald-100/70 group-hover:shadow-md transition-all duration-300">
                        <CheckCircle className="h-6 w-6 text-emerald-500 mr-3 flex-shrink-0" />
                        <span className="font-semibold text-gray-800">
                          Real-time analytics dashboard
                        </span>
                      </div>
                      <div className="flex items-center p-4 bg-white/80 rounded-2xl shadow-sm border border-emerald-100/70 group-hover:shadow-md transition-all duration-300">
                        <CheckCircle className="h-6 w-6 text-emerald-500 mr-3 flex-shrink-0" />
                        <span className="font-semibold text-gray-800">
                          Menu management & QR codes
                        </span>
                      </div>
                      <div className="flex items-center p-4 bg-white/80 rounded-2xl shadow-sm border border-emerald-100/70 group-hover:shadow-md transition-all duration-300">
                        <CheckCircle className="h-6 w-6 text-emerald-500 mr-3 flex-shrink-0" />
                        <span className="font-semibold text-gray-800">
                          Customer loyalty programs
                        </span>
                      </div>
                      <div className="flex items-center p-4 bg-white/80 rounded-2xl shadow-sm border border-emerald-100/70 group-hover:shadow-md transition-all duration-300">
                        <CheckCircle className="h-6 w-6 text-emerald-500 mr-3 flex-shrink-0" />
                        <span className="font-semibold text-gray-800">24/7 priority support</span>
                      </div>
                      <div className="flex items-center p-4 bg-white/80 rounded-2xl shadow-sm border border-emerald-100/70 group-hover:shadow-md transition-all duration-300">
                        <CheckCircle className="h-6 w-6 text-emerald-500 mr-3 flex-shrink-0" />
                        <span className="font-semibold text-gray-800">Mobile app included</span>
                      </div>
                    </div>

                    {/* CTA Button */}
                    <button className="group w-full bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white font-bold py-6 px-8 text-xl rounded-2xl shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300 overflow-hidden relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="relative flex items-center justify-center">
                        <ChefHat className="mr-4 h-8 w-8 group-hover:rotate-12 transition-transform duration-300" />
                        Start Your Free Restaurant Now
                        <ArrowRight className="ml-4 h-8 w-8 group-hover:translate-x-2 transition-transform duration-300" />
                      </div>
                    </button>

                    {/* Trust Indicators */}
                    <div className="mt-8 text-center">
                      <p className="text-gray-500 mb-4 text-lg">
                        ✨ No credit card required • ✨ Setup in 5 minutes • ✨ Cancel anytime
                      </p>
                      <div className="flex items-center justify-center space-x-6 text-sm">
                        <span className="flex items-center text-emerald-600">
                          <Shield className="h-4 w-4 mr-2" />
                          SSL Secured
                        </span>
                        <span className="flex items-center text-emerald-600">
                          <CheckCircle className="h-4 w-4 mr-2" />
                          1,000+ Active Restaurants
                        </span>
                        <span className="flex items-center text-emerald-600">
                          <Star className="h-4 w-4 mr-2 fill-current" />
                          4.9/5 Rating
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Bottom Features Grid */}
            <div className="mt-20">
              <h3 className="text-3xl font-bold text-white text-center mb-12">
                Why Choose Our Free Platform?
              </h3>
              <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
                <div className="group bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:bg-white/15 transition-all duration-300 hover:-translate-y-2">
                  <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Shield className="h-8 w-8 text-white" />
                  </div>
                  <h4 className="font-bold text-white text-xl mb-4">No Hidden Costs</h4>
                  <p className="text-gray-300 leading-relaxed">
                    Truly free forever. No surprise fees, no premium features locked away.
                    Everything is included from day one.
                  </p>
                </div>
                <div className="group bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:bg-white/15 transition-all duration-300 hover:-translate-y-2">
                  <div className="w-16 h-16 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    <TrendingUp className="h-8 w-8 text-white" />
                  </div>
                  <h4 className="font-bold text-white text-xl mb-4">Grow Your Revenue</h4>
                  <p className="text-gray-300 leading-relaxed">
                    All the tools you need to increase sales, improve efficiency, and boost customer
                    satisfaction.
                  </p>
                </div>
                <div className="group bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:bg-white/15 transition-all duration-300 hover:-translate-y-2">
                  <div className="w-16 h-16 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Users className="h-8 w-8 text-white" />
                  </div>
                  <h4 className="font-bold text-white text-xl mb-4">Join the Community</h4>
                  <p className="text-gray-300 leading-relaxed">
                    Connect with thousands of successful restaurant owners already growing with our
                    platform.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA */}
      <section className="relative py-32 overflow-hidden">
        {/* Background Image with Overlay */}
        <div className="absolute inset-0">
          <div
            className="absolute inset-0 bg-cover bg-center bg-no-repeat"
            style={{
              backgroundImage: `url('data:image/svg+xml,${encodeURIComponent(`
                <svg viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
                  <defs>
                    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
                      <stop offset="100%" style="stop-color:#f093fb;stop-opacity:1" />
                    </linearGradient>
                    <pattern id="grid" width="60" height="60" patternUnits="userSpaceOnUse">
                      <path d="M 60 0 L 0 0 0 60" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
                    </pattern>
                  </defs>
                  <rect width="100%" height="100%" fill="url(#bg)"/>
                  <rect width="100%" height="100%" fill="url(#grid)"/>
                  <g opacity="0.3">
                    <circle cx="200" cy="150" r="80" fill="rgba(255,255,255,0.1)"/>
                    <circle cx="900" cy="200" r="60" fill="rgba(255,255,255,0.1)"/>
                    <circle cx="1000" cy="600" r="100" fill="rgba(255,255,255,0.1)"/>
                    <circle cx="300" cy="650" r="40" fill="rgba(255,255,255,0.1)"/>
                    <circle cx="600" cy="100" r="50" fill="rgba(255,255,255,0.1)"/>
                  </g>
                </svg>
              `)}')`,
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-br from-indigo-900/60 via-purple-900/40 to-pink-900/60"></div>
          <div className="absolute inset-0 bg-black/20"></div>
        </div>

        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-20 left-20 w-32 h-32 bg-white/10 rounded-full blur-xl animate-pulse"></div>
          <div className="absolute top-40 right-32 w-24 h-24 bg-blue-300/20 rounded-full blur-lg animate-pulse delay-1000"></div>
          <div className="absolute bottom-32 left-1/3 w-40 h-40 bg-purple-300/10 rounded-full blur-2xl animate-pulse delay-2000"></div>
          <div className="absolute bottom-20 right-20 w-28 h-28 bg-pink-300/15 rounded-full blur-xl animate-pulse delay-500"></div>
        </div>

        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="max-w-5xl mx-auto">
            {/* Badge */}
            <div className="inline-flex items-center px-6 py-3 bg-white/20 backdrop-blur-sm border border-white/30 rounded-full text-white font-medium mb-8 shadow-lg">
              <span className="animate-pulse mr-2">🚀</span>
              Join 500+ Restaurants Already Growing
            </div>

            {/* Main Heading */}
            <h2 className="text-5xl md:text-7xl font-bold text-white mb-8 leading-tight">
              Ready to Transform
              <br />
              <span className="bg-gradient-to-r from-yellow-300 via-pink-300 to-blue-300 bg-clip-text text-transparent">
                Your Business?
              </span>
            </h2>

            {/* Subheading */}
            <p className="text-xl md:text-2xl text-white/90 mb-12 max-w-3xl mx-auto leading-relaxed font-light">
              Join thousands of restaurants already using our platform to increase sales, streamline
              operations, and delight customers.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12">
              <button className="group relative px-10 py-5 bg-white text-gray-900 font-bold text-lg rounded-2xl hover:bg-gray-50 transform hover:scale-105 transition-all duration-300 shadow-2xl overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                <div className="relative flex items-center">
                  <ChefHat className="mr-3 h-6 w-6" />
                  Start Your Free Trial
                  <ArrowRight className="ml-3 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                </div>
              </button>

              <button className="group px-10 py-5 border-2 border-white/50 text-white font-semibold text-lg rounded-2xl hover:bg-white/10 backdrop-blur-sm transform hover:scale-105 transition-all duration-300">
                <div className="flex items-center">
                  <svg
                    className="mr-3 h-5 w-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  Schedule a Demo
                </div>
              </button>
            </div>

            {/* Trust Indicators */}
            <div className="flex flex-wrap justify-center items-center gap-8 text-white/80 text-sm">
              <div className="flex items-center">
                <svg
                  className="w-5 h-5 mr-2 text-green-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
                No setup fees
              </div>
              <div className="flex items-center">
                <svg
                  className="w-5 h-5 mr-2 text-green-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
                30-day free trial
              </div>
              <div className="flex items-center">
                <svg
                  className="w-5 h-5 mr-2 text-green-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
                Cancel anytime
              </div>
              <div className="flex items-center">
                <svg
                  className="w-5 h-5 mr-2 text-green-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
                24/7 support
              </div>
            </div>

            {/* Social Proof */}
            <div className="mt-16 pt-12 border-t border-white/20">
              <p className="text-white/70 text-sm mb-6">Trusted by restaurants worldwide</p>
              <div className="flex justify-center items-center space-x-8 opacity-60">
                {/* Mock logos - these would be replaced with actual partner logos */}
                <div className="text-white text-xs font-semibold px-4 py-2 border border-white/30 rounded-lg">
                  RESTAURANT A
                </div>
                <div className="text-white text-xs font-semibold px-4 py-2 border border-white/30 rounded-lg">
                  CAFE CHAIN B
                </div>
                <div className="text-white text-xs font-semibold px-4 py-2 border border-white/30 rounded-lg">
                  BISTRO C
                </div>
                <div className="text-white text-xs font-semibold px-4 py-2 border border-white/30 rounded-lg">
                  EATERY D
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Wave */}
        <div className="absolute bottom-0 left-0 w-full">
          <svg
            viewBox="0 0 1200 120"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="w-full h-12"
          >
            <path
              d="M0 120L50 105C100 90 200 60 300 45C400 30 500 30 600 37.5C700 45 800 60 900 67.5C1000 75 1100 75 1150 75L1200 75V120H1150C1100 120 1000 120 900 120C800 120 700 120 600 120C500 120 400 120 300 120C200 120 100 120 50 120H0Z"
              fill="white"
            />
          </svg>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900 text-white relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-10 left-10 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-10 right-10 w-48 h-48 bg-purple-500/5 rounded-full blur-2xl"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="py-16">
            <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-12">
              {/* Company Info */}
              <div className="lg:col-span-1 md:col-span-2">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-gradient-to-br from-amber-500 to-orange-600 rounded-2xl flex items-center justify-center mr-4">
                    <ChefHat className="h-7 w-7 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold bg-gradient-to-r from-amber-400 to-orange-400 bg-clip-text text-transparent">
                    YoFood
                  </h3>
                </div>
                <p className="text-gray-300 mb-6 leading-relaxed">
                  Transform your restaurant into a digital empire with our all-in-one management
                  platform. Streamline operations, boost sales, and create unforgettable customer
                  experiences.
                </p>

                {/* Social Media Links */}
                <div className="flex space-x-4">
                  <a
                    href="#"
                    className="group w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-blue-500 transition-all duration-300"
                  >
                    <Facebook className="h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
                  </a>
                  <a
                    href="#"
                    className="group w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-blue-400 transition-all duration-300"
                  >
                    <Twitter className="h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
                  </a>
                  <a
                    href="#"
                    className="group w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-pink-500 transition-all duration-300"
                  >
                    <Instagram className="h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
                  </a>
                  <a
                    href="#"
                    className="group w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-red-500 transition-all duration-300"
                  >
                    <Youtube className="h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
                  </a>
                  <a
                    href="#"
                    className="group w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-blue-600 transition-all duration-300"
                  >
                    <Linkedin className="h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
                  </a>
                </div>
              </div>

              {/* Quick Links */}
              <div>
                <h4 className="text-lg font-semibold mb-6 text-white">Quick Links</h4>
                <ul className="space-y-3">
                  <li>
                    <a
                      href="#"
                      className="text-gray-300 hover:text-amber-400 transition-colors duration-300 flex items-center group"
                    >
                      <ArrowRight className="h-4 w-4 mr-2 opacity-0 group-hover:opacity-100 group-hover:translate-x-1 transition-all duration-300" />
                      Features
                    </a>
                  </li>
                  <li>
                    <a
                      href="#"
                      className="text-gray-300 hover:text-amber-400 transition-colors duration-300 flex items-center group"
                    >
                      <ArrowRight className="h-4 w-4 mr-2 opacity-0 group-hover:opacity-100 group-hover:translate-x-1 transition-all duration-300" />
                      Pricing
                    </a>
                  </li>
                  <li>
                    <a
                      href="#"
                      className="text-gray-300 hover:text-amber-400 transition-colors duration-300 flex items-center group"
                    >
                      <ArrowRight className="h-4 w-4 mr-2 opacity-0 group-hover:opacity-100 group-hover:translate-x-1 transition-all duration-300" />
                      Demo
                    </a>
                  </li>
                  <li>
                    <a
                      href="#"
                      className="text-gray-300 hover:text-amber-400 transition-colors duration-300 flex items-center group"
                    >
                      <ArrowRight className="h-4 w-4 mr-2 opacity-0 group-hover:opacity-100 group-hover:translate-x-1 transition-all duration-300" />
                      Case Studies
                    </a>
                  </li>
                  <li>
                    <a
                      href="#"
                      className="text-gray-300 hover:text-amber-400 transition-colors duration-300 flex items-center group"
                    >
                      <ArrowRight className="h-4 w-4 mr-2 opacity-0 group-hover:opacity-100 group-hover:translate-x-1 transition-all duration-300" />
                      Blog
                    </a>
                  </li>
                </ul>
              </div>

              {/* Support */}
              <div>
                <h4 className="text-lg font-semibold mb-6 text-white">Support</h4>
                <ul className="space-y-3">
                  <li>
                    <a
                      href="#"
                      className="text-gray-300 hover:text-amber-400 transition-colors duration-300 flex items-center group"
                    >
                      <ArrowRight className="h-4 w-4 mr-2 opacity-0 group-hover:opacity-100 group-hover:translate-x-1 transition-all duration-300" />
                      Help Center
                    </a>
                  </li>
                  <li>
                    <a
                      href="#"
                      className="text-gray-300 hover:text-amber-400 transition-colors duration-300 flex items-center group"
                    >
                      <ArrowRight className="h-4 w-4 mr-2 opacity-0 group-hover:opacity-100 group-hover:translate-x-1 transition-all duration-300" />
                      Contact Support
                    </a>
                  </li>
                  <li>
                    <a
                      href="#"
                      className="text-gray-300 hover:text-amber-400 transition-colors duration-300 flex items-center group"
                    >
                      <ArrowRight className="h-4 w-4 mr-2 opacity-0 group-hover:opacity-100 group-hover:translate-x-1 transition-all duration-300" />
                      API Documentation
                    </a>
                  </li>
                  <li>
                    <a
                      href="#"
                      className="text-gray-300 hover:text-amber-400 transition-colors duration-300 flex items-center group"
                    >
                      <ArrowRight className="h-4 w-4 mr-2 opacity-0 group-hover:opacity-100 group-hover:translate-x-1 transition-all duration-300" />
                      System Status
                    </a>
                  </li>
                </ul>
              </div>

              {/* Contact Info */}
              <div>
                <h4 className="text-lg font-semibold mb-6 text-white">Contact Info</h4>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                      <Phone className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <p className="text-white font-medium">Support Hotline</p>
                      <p className="text-gray-300 text-sm">24/7 Available</p>
                      <a
                        href="tel:******-YO-FOOD"
                        className="text-amber-400 hover:text-amber-300 transition-colors duration-300"
                      >
                        +1 (800) YO-FOOD
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                      <Mail className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <p className="text-white font-medium">Email Support</p>
                      <a
                        href="mailto:<EMAIL>"
                        className="text-amber-400 hover:text-amber-300 transition-colors duration-300"
                      >
                        <EMAIL>
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                      <MapPin className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <p className="text-white font-medium">Office</p>
                      <p className="text-gray-300 text-sm">
                        123 Tech Street
                        <br />
                        San Francisco, CA 94105
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="border-t border-gray-700/50 py-8">
            <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
              <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6">
                <p className="text-gray-400 text-sm">© 2024 YoFood. All rights reserved.</p>
                <div className="flex space-x-6">
                  <a
                    href="#"
                    className="text-gray-400 hover:text-white text-sm transition-colors duration-300"
                  >
                    Privacy Policy
                  </a>
                  <a
                    href="#"
                    className="text-gray-400 hover:text-white text-sm transition-colors duration-300"
                  >
                    Terms of Service
                  </a>
                  <a
                    href="#"
                    className="text-gray-400 hover:text-white text-sm transition-colors duration-300"
                  >
                    Cookie Policy
                  </a>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <span className="text-gray-400 text-sm">Made with</span>
                <div className="w-4 h-4 bg-red-500 rounded-full animate-pulse"></div>
                <span className="text-gray-400 text-sm">for restaurants worldwide</span>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default MerchantPageClient
