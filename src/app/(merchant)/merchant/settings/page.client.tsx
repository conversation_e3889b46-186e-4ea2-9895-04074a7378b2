'use client'

import React, { useState } from 'react'
import { Layout } from '@/components/Merchant/Layout'
import {
  User,
  Building2,
  Bell,
  Shield,
  CreditCard,
  Globe,
  Palette,
  Database,
  Zap,
  Save,
  Camera,
  Check,
  Sparkles,
  Settings as SettingsIcon,
  ChevronRight,
  Edit,
  Mail,
  Phone,
} from 'lucide-react'

// TypeScript interfaces
interface MerchantProfile {
  id: string
  businessName: string
  businessType: string
  email: string
  phone: string
  address: string
  city: string
  state: string
  zipCode: string
  country: string
  website: string
  description: string
  logo: string
  coverImage: string
  taxId: string
  businessLicense: string
}

// These interfaces will be used in future tabs
// interface NotificationSettings {
//   emailNotifications: boolean
//   smsNotifications: boolean
//   orderUpdates: boolean
//   paymentUpdates: boolean
//   marketingEmails: boolean
//   systemAlerts: boolean
//   weeklyReports: boolean
//   monthlyReports: boolean
// }

// interface SecuritySettings {
//   twoFactorAuth: boolean
//   sessionTimeout: number
//   loginAlerts: boolean
//   passwordExpiry: number
//   apiAccess: boolean
//   webhookUrl: string
// }

// interface PaymentSettings {
//   primaryMethod: string
//   autoPayout: boolean
//   payoutSchedule: string
//   minimumPayout: number
//   currency: string
//   taxRate: number
// }

// interface ThemeSettings {
//   primaryColor: string
//   secondaryColor: string
//   darkMode: boolean
//   language: string
//   timezone: string
//   dateFormat: string
// }

const SettingsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('profile')
  const [isEditing, setIsEditing] = useState(false)
  const [loading, setLoading] = useState(false)
  const [saved, setSaved] = useState(false)

  // Sample data - in real app, this would come from API
  const [profile, setProfile] = useState<MerchantProfile>({
    id: '1',
    businessName: "Bella's Bistro",
    businessType: 'Restaurant',
    email: '<EMAIL>',
    phone: '+****************',
    address: '123 Main Street',
    city: 'New York',
    state: 'NY',
    zipCode: '10001',
    country: 'United States',
    website: 'https://bellasbistro.com',
    description: 'A cozy family restaurant serving authentic Italian cuisine with a modern twist.',
    logo: '',
    coverImage: '',
    taxId: '12-3456789',
    businessLicense: 'BL-2024-001',
  })

  // These will be used in future tabs
  // const [notifications, setNotifications] = useState<NotificationSettings>({
  //   emailNotifications: true,
  //   smsNotifications: false,
  //   orderUpdates: true,
  //   paymentUpdates: true,
  //   marketingEmails: false,
  //   systemAlerts: true,
  //   weeklyReports: true,
  //   monthlyReports: true,
  // })

  // const [security, setSecurity] = useState<SecuritySettings>({
  //   twoFactorAuth: false,
  //   sessionTimeout: 30,
  //   loginAlerts: true,
  //   passwordExpiry: 90,
  //   apiAccess: false,
  //   webhookUrl: '',
  // })

  // const [payment, setPayment] = useState<PaymentSettings>({
  //   primaryMethod: 'bank_transfer',
  //   autoPayout: true,
  //   payoutSchedule: 'weekly',
  //   minimumPayout: 100,
  //   currency: 'USD',
  //   taxRate: 8.25,
  // })

  // const [theme, setTheme] = useState<ThemeSettings>({
  //   primaryColor: '#f59e0b',
  //   secondaryColor: '#ea580c',
  //   darkMode: false,
  //   language: 'en',
  //   timezone: 'America/New_York',
  //   dateFormat: 'MM/DD/YYYY',
  // })

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'business', label: 'Business', icon: Building2 },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'payments', label: 'Payments', icon: CreditCard },
    { id: 'appearance', label: 'Appearance', icon: Palette },
    { id: 'integrations', label: 'Integrations', icon: Globe },
    { id: 'advanced', label: 'Advanced', icon: Database },
  ]

  const handleSave = async () => {
    setLoading(true)
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))
    setLoading(false)
    setSaved(true)
    setTimeout(() => setSaved(false), 3000)
  }

  const renderProfileTab = () => (
    <div className="space-y-6">
      {/* Profile Header */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-amber-500/10 via-orange-500/10 to-red-500/10 backdrop-blur-xl rounded-2xl border border-amber-200/30 shadow-xl" />
        <div className="relative p-6 lg:p-8">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <div className="w-20 h-20 bg-gradient-to-br from-amber-400 via-orange-500 to-red-600 rounded-2xl flex items-center justify-center shadow-lg shadow-orange-500/25">
                <User className="h-10 w-10 text-white drop-shadow-lg" />
              </div>
              <button className="absolute -bottom-1 -right-1 w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg hover:scale-110 transition-transform duration-300">
                <Camera className="h-4 w-4 text-white" />
              </button>
            </div>
            <div className="flex-1">
              <h2 className="text-2xl font-black bg-gradient-to-r from-amber-600 via-orange-600 to-red-600 bg-clip-text text-transparent">
                Profile Settings
              </h2>
              <p className="text-gray-600 mt-1">Manage your personal and business information</p>
            </div>
            <button
              onClick={() => setIsEditing(!isEditing)}
              className="group flex items-center px-6 py-3 bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 text-white rounded-xl hover:from-amber-500 hover:via-orange-600 hover:to-red-600 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-orange-400/50"
            >
              <Edit className="h-4 w-4 mr-2 group-hover:rotate-12 transition-transform duration-300" />
              <span className="font-bold">{isEditing ? 'Cancel' : 'Edit'}</span>
              <Zap className="h-4 w-4 ml-2 animate-pulse" />
            </button>
          </div>
        </div>
      </div>

      {/* Profile Form */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Personal Information */}
        <div className="relative group">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-600/10 backdrop-blur-xl rounded-2xl border border-blue-200/30 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
          <div className="relative p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25">
                <User className="h-5 w-5 text-white drop-shadow-lg" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-800">Personal Information</h3>
                <div className="flex items-center space-x-1">
                  <div className="w-2.5 h-2.5 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full animate-pulse" />
                  <span className="text-xs text-blue-600 font-medium">Required</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Email Address
                </label>
                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-orange-500/20 rounded-xl blur-sm group-focus-within:blur-md group-focus-within:from-amber-400/30 group-focus-within:to-orange-500/30 transition-all duration-300" />
                  <div className="relative flex items-center">
                    <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-amber-600 group-focus-within:text-orange-600 transition-colors z-10" />
                    <input
                      type="email"
                      value={profile.email}
                      onChange={(e) => setProfile({ ...profile, email: e.target.value })}
                      disabled={!isEditing}
                      className="pl-12 pr-4 py-3 w-full bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg focus:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                      placeholder="Enter your email"
                    />
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Phone Number
                </label>
                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-orange-500/20 rounded-xl blur-sm group-focus-within:blur-md group-focus-within:from-amber-400/30 group-focus-within:to-orange-500/30 transition-all duration-300" />
                  <div className="relative flex items-center">
                    <Phone className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-amber-600 group-focus-within:text-orange-600 transition-colors z-10" />
                    <input
                      type="tel"
                      value={profile.phone}
                      onChange={(e) => setProfile({ ...profile, phone: e.target.value })}
                      disabled={!isEditing}
                      className="pl-12 pr-4 py-3 w-full bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg focus:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                      placeholder="Enter your phone number"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Business Information */}
        <div className="relative group">
          <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/10 to-green-600/10 backdrop-blur-xl rounded-2xl border border-emerald-200/30 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
          <div className="relative p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg shadow-emerald-500/25">
                <Building2 className="h-5 w-5 text-white drop-shadow-lg" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-800">Business Information</h3>
                <div className="flex items-center space-x-1">
                  <div className="w-2.5 h-2.5 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse" />
                  <span className="text-xs text-emerald-600 font-medium">Required</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Business Name
                </label>
                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-orange-500/20 rounded-xl blur-sm group-focus-within:blur-md group-focus-within:from-amber-400/30 group-focus-within:to-orange-500/30 transition-all duration-300" />
                  <div className="relative flex items-center">
                    <Building2 className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-amber-600 group-focus-within:text-orange-600 transition-colors z-10" />
                    <input
                      type="text"
                      value={profile.businessName}
                      onChange={(e) => setProfile({ ...profile, businessName: e.target.value })}
                      disabled={!isEditing}
                      className="pl-12 pr-4 py-3 w-full bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg focus:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                      placeholder="Enter business name"
                    />
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Business Type
                </label>
                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-orange-500/20 rounded-xl blur-sm group-focus-within:blur-md group-focus-within:from-amber-400/30 group-focus-within:to-orange-500/30 transition-all duration-300" />
                  <div className="relative flex items-center">
                    <Building2 className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-amber-600 group-focus-within:text-orange-600 transition-colors z-10" />
                    <select
                      value={profile.businessType}
                      onChange={(e) => setProfile({ ...profile, businessType: e.target.value })}
                      disabled={!isEditing}
                      className="pl-12 pr-4 py-3 w-full bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 shadow-lg focus:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed appearance-none"
                    >
                      <option value="Restaurant">Restaurant</option>
                      <option value="Cafe">Cafe</option>
                      <option value="Food Truck">Food Truck</option>
                      <option value="Bakery">Bakery</option>
                      <option value="Catering">Catering</option>
                      <option value="Other">Other</option>
                    </select>
                    <ChevronRight className="absolute right-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-amber-600 rotate-90" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Address Information */}
      <div className="relative group">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-600/10 backdrop-blur-xl rounded-2xl border border-purple-200/30 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
        <div className="relative p-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg shadow-purple-500/25">
              <Globe className="h-5 w-5 text-white drop-shadow-lg" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-gray-800">Address Information</h3>
              <div className="flex items-center space-x-1">
                <div className="w-2.5 h-2.5 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full animate-pulse" />
                <span className="text-xs text-purple-600 font-medium">Required</span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Street Address
              </label>
              <input
                type="text"
                value={profile.address}
                onChange={(e) => setProfile({ ...profile, address: e.target.value })}
                disabled={!isEditing}
                className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg focus:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                placeholder="Enter street address"
              />
            </div>
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">City</label>
              <input
                type="text"
                value={profile.city}
                onChange={(e) => setProfile({ ...profile, city: e.target.value })}
                disabled={!isEditing}
                className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg focus:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                placeholder="Enter city"
              />
            </div>
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">State</label>
              <input
                type="text"
                value={profile.state}
                onChange={(e) => setProfile({ ...profile, state: e.target.value })}
                disabled={!isEditing}
                className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg focus:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                placeholder="Enter state"
              />
            </div>
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">ZIP Code</label>
              <input
                type="text"
                value={profile.zipCode}
                onChange={(e) => setProfile({ ...profile, zipCode: e.target.value })}
                disabled={!isEditing}
                className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg focus:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                placeholder="Enter ZIP code"
              />
            </div>
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">Country</label>
              <input
                type="text"
                value={profile.country}
                onChange={(e) => setProfile({ ...profile, country: e.target.value })}
                disabled={!isEditing}
                className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg focus:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                placeholder="Enter country"
              />
            </div>
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">Website</label>
              <input
                type="url"
                value={profile.website}
                onChange={(e) => setProfile({ ...profile, website: e.target.value })}
                disabled={!isEditing}
                className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg focus:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                placeholder="https://yourwebsite.com"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <Layout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-amber-500/10 via-orange-500/10 to-red-500/10 backdrop-blur-xl rounded-2xl border border-amber-200/30 shadow-xl" />
          <div className="relative p-6 lg:p-8">
            <div className="flex items-center space-x-3 mb-2">
              <div className="w-12 h-12 bg-gradient-to-br from-amber-400 via-orange-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25">
                <SettingsIcon className="h-6 w-6 text-white drop-shadow-lg" />
              </div>
              <h1 className="text-4xl font-black bg-gradient-to-r from-amber-600 via-orange-600 to-red-600 bg-clip-text text-transparent drop-shadow-sm">
                Settings
              </h1>
            </div>
            <p className="text-gray-600 text-lg">Manage your account and business preferences</p>
          </div>
        </div>

        {/* Settings Tabs */}
        <div className="relative">
          <div className="absolute inset-0 bg-white/40 backdrop-blur-xl rounded-2xl border border-white/50 shadow-xl" />
          <div className="relative p-6">
            <div className="flex flex-wrap gap-2 mb-8">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`group flex items-center px-6 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 ${
                      activeTab === tab.id
                        ? 'bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 text-white shadow-xl shadow-orange-500/30 border border-orange-400/50'
                        : 'bg-white/80 backdrop-blur-sm border border-amber-200/50 text-gray-700 hover:bg-white/90 hover:border-amber-300/70 hover:shadow-lg'
                    }`}
                  >
                    <Icon
                      className={`h-4 w-4 mr-2 ${activeTab === tab.id ? 'text-white' : 'text-amber-600 group-hover:text-orange-600'} transition-colors`}
                    />
                    <span>{tab.label}</span>
                    {activeTab === tab.id && <Sparkles className="h-4 w-4 ml-2 animate-pulse" />}
                  </button>
                )
              })}
            </div>

            {/* Tab Content */}
            <div className="min-h-[600px]">
              {activeTab === 'profile' && renderProfileTab()}
              {activeTab === 'business' && (
                <div className="space-y-6">
                  {/* Business Header */}
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/10 via-teal-500/10 to-green-500/10 backdrop-blur-xl rounded-2xl border border-emerald-200/30 shadow-xl" />
                    <div className="relative p-6 lg:p-8">
                      <div className="flex items-center space-x-4">
                        <div className="w-20 h-20 bg-gradient-to-br from-emerald-400 via-teal-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg shadow-emerald-500/25">
                          <Building2 className="h-10 w-10 text-white drop-shadow-lg" />
                        </div>
                        <div className="flex-1">
                          <h2 className="text-2xl font-black bg-gradient-to-r from-emerald-600 via-teal-600 to-green-600 bg-clip-text text-transparent">
                            Business Settings
                          </h2>
                          <p className="text-gray-600 mt-1">
                            Manage your business information and preferences
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Business Information */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Business Details */}
                    <div className="relative group">
                      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-600/10 backdrop-blur-xl rounded-2xl border border-blue-200/30 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
                      <div className="relative p-6">
                        <div className="flex items-center space-x-3 mb-6">
                          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25">
                            <Building2 className="h-5 w-5 text-white drop-shadow-lg" />
                          </div>
                          <div>
                            <h3 className="text-lg font-bold text-gray-800">Business Details</h3>
                            <div className="flex items-center space-x-1">
                              <div className="w-2.5 h-2.5 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full animate-pulse" />
                              <span className="text-xs text-blue-600 font-medium">Required</span>
                            </div>
                          </div>
                        </div>

                        <div className="space-y-4">
                          <div>
                            <label className="block text-sm font-semibold text-gray-700 mb-2">
                              Business Description
                            </label>
                            <textarea
                              value={profile.description}
                              onChange={(e) =>
                                setProfile({ ...profile, description: e.target.value })
                              }
                              disabled={!isEditing}
                              rows={4}
                              className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg focus:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed resize-none"
                              placeholder="Describe your business..."
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-semibold text-gray-700 mb-2">
                              Tax ID
                            </label>
                            <input
                              type="text"
                              value={profile.taxId}
                              onChange={(e) => setProfile({ ...profile, taxId: e.target.value })}
                              disabled={!isEditing}
                              className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg focus:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                              placeholder="Enter Tax ID"
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-semibold text-gray-700 mb-2">
                              Business License
                            </label>
                            <input
                              type="text"
                              value={profile.businessLicense}
                              onChange={(e) =>
                                setProfile({ ...profile, businessLicense: e.target.value })
                              }
                              disabled={!isEditing}
                              className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg focus:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                              placeholder="Enter Business License"
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Business Hours */}
                    <div className="relative group">
                      <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-red-600/10 backdrop-blur-xl rounded-2xl border border-orange-200/30 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
                      <div className="relative p-6">
                        <div className="flex items-center space-x-3 mb-6">
                          <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25">
                            <Zap className="h-5 w-5 text-white drop-shadow-lg" />
                          </div>
                          <div>
                            <h3 className="text-lg font-bold text-gray-800">Business Hours</h3>
                            <div className="flex items-center space-x-1">
                              <div className="w-2.5 h-2.5 bg-gradient-to-r from-orange-400 to-red-500 rounded-full animate-pulse" />
                              <span className="text-xs text-orange-600 font-medium">Optional</span>
                            </div>
                          </div>
                        </div>

                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <label className="block text-sm font-semibold text-gray-700 mb-2">
                                Opening Time
                              </label>
                              <input
                                type="time"
                                disabled={!isEditing}
                                className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 shadow-lg focus:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                                defaultValue="09:00"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-semibold text-gray-700 mb-2">
                                Closing Time
                              </label>
                              <input
                                type="time"
                                disabled={!isEditing}
                                className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 shadow-lg focus:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                                defaultValue="22:00"
                              />
                            </div>
                          </div>

                          <div>
                            <label className="block text-sm font-semibold text-gray-700 mb-2">
                              Operating Days
                            </label>
                            <div className="grid grid-cols-2 gap-2">
                              {[
                                'Monday',
                                'Tuesday',
                                'Wednesday',
                                'Thursday',
                                'Friday',
                                'Saturday',
                                'Sunday',
                              ].map((day) => (
                                <label
                                  key={day}
                                  className="flex items-center space-x-2 cursor-pointer"
                                >
                                  <input
                                    type="checkbox"
                                    defaultChecked={day !== 'Sunday'}
                                    disabled={!isEditing}
                                    className="w-4 h-4 text-amber-600 bg-white border-amber-300 rounded focus:ring-amber-500 focus:ring-2 disabled:opacity-50 disabled:cursor-not-allowed"
                                  />
                                  <span className="text-sm text-gray-700">{day}</span>
                                </label>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              {activeTab === 'notifications' && (
                <div className="text-center py-20">
                  <Bell className="h-16 w-16 text-amber-400 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-gray-800 mb-2">Notification Settings</h3>
                  <p className="text-gray-600">Notification preferences coming soon...</p>
                </div>
              )}
              {activeTab === 'security' && (
                <div className="text-center py-20">
                  <Shield className="h-16 w-16 text-amber-400 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-gray-800 mb-2">Security Settings</h3>
                  <p className="text-gray-600">Security and privacy options coming soon...</p>
                </div>
              )}
              {activeTab === 'payments' && (
                <div className="text-center py-20">
                  <CreditCard className="h-16 w-16 text-amber-400 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-gray-800 mb-2">Payment Settings</h3>
                  <p className="text-gray-600">Payment and billing options coming soon...</p>
                </div>
              )}
              {activeTab === 'appearance' && (
                <div className="text-center py-20">
                  <Palette className="h-16 w-16 text-amber-400 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-gray-800 mb-2">Appearance Settings</h3>
                  <p className="text-gray-600">Theme and customization options coming soon...</p>
                </div>
              )}
              {activeTab === 'integrations' && (
                <div className="text-center py-20">
                  <Globe className="h-16 w-16 text-amber-400 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-gray-800 mb-2">Integrations</h3>
                  <p className="text-gray-600">Third-party integrations coming soon...</p>
                </div>
              )}
              {activeTab === 'advanced' && (
                <div className="text-center py-20">
                  <Database className="h-16 w-16 text-amber-400 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-gray-800 mb-2">Advanced Settings</h3>
                  <p className="text-gray-600">Advanced configuration options coming soon...</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Save Button */}
        {isEditing && (
          <div className="flex justify-end">
            <button
              onClick={handleSave}
              disabled={loading}
              className="group flex items-center px-8 py-4 bg-gradient-to-r from-emerald-400 via-teal-500 to-green-600 text-white rounded-xl hover:from-emerald-500 hover:via-teal-600 hover:to-green-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-emerald-400/50 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3" />
                  <span className="font-bold">Saving...</span>
                </>
              ) : saved ? (
                <>
                  <Check className="h-5 w-5 mr-2" />
                  <span className="font-bold">Saved!</span>
                </>
              ) : (
                <>
                  <Save className="h-5 w-5 mr-2 group-hover:rotate-12 transition-transform duration-300" />
                  <span className="font-bold">Save Changes</span>
                  <Sparkles className="h-5 w-5 ml-2 animate-pulse" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </Layout>
  )
}

export default SettingsPage
