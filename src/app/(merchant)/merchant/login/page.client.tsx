'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Mail,
  KeyRound,
  ArrowRight,
  Loader2,
  CheckCircle,
  ChefHat,
  Shield,
  Star,
} from 'lucide-react'
import Link from 'next/link'

const MerchantLoginPageClient = () => {
  const [step, setStep] = useState<'email' | 'code'>('email')
  const [email, setEmail] = useState('')
  const [code, setCode] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isEmailSent, setIsEmailSent] = useState(false)

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!email) return

    setIsLoading(true)

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1500))

    setIsEmailSent(true)
    setStep('code')
    setIsLoading(false)
  }

  const handleCodeSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!code) return

    setIsLoading(true)

    // Simulate API call for code verification
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // Here you would typically redirect to dashboard
    console.log('Login successful!')
    setIsLoading(false)
  }

  const resendCode = async () => {
    setIsLoading(true)
    await new Promise((resolve) => setTimeout(resolve, 1000))
    setIsEmailSent(true)
    setIsLoading(false)
  }

  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-amber-50 via-white to-orange-50">
      {/* Dynamic Background */}
      <div className="absolute inset-0">
        {/* Primary Background Image */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat scale-105 transition-transform duration-[20s] ease-linear"
          style={{
            backgroundImage:
              'url("https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80")',
          }}
        />

        {/* Gradient Overlays */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/60 via-amber-900/40 to-orange-900/60"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent"></div>

        {/* Animated Mesh Background */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-amber-500/20 to-orange-500/20 animate-pulse"></div>
          <div className="absolute top-20 left-20 w-96 h-96 bg-amber-400/10 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-32 right-20 w-80 h-80 bg-orange-400/10 rounded-full blur-3xl animate-float-delayed"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-radial from-amber-300/5 to-transparent rounded-full animate-spin-slow"></div>
        </div>
      </div>

      {/* Floating Particles */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-amber-300 rounded-full animate-ping"></div>
        <div className="absolute top-3/4 left-3/4 w-1 h-1 bg-orange-300 rounded-full animate-ping delay-1000"></div>
        <div className="absolute top-1/2 left-1/6 w-1.5 h-1.5 bg-yellow-300 rounded-full animate-ping delay-2000"></div>
        <div className="absolute top-1/6 right-1/4 w-2 h-2 bg-amber-400 rounded-full animate-ping delay-500"></div>
      </div>

      <div className="relative z-20 min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          {/* Announcement Badge */}
          <div className="flex items-center px-6 py-3 bg-white/10 backdrop-blur-md border border-white/20 rounded-full text-sm font-medium mb-8 animate-fade-in-down text-white mx-auto w-fit">
            <Shield className="w-4 h-4 mr-2 text-green-400" />
            Secure merchant portal
          </div>

          <Card className="shadow-2xl bg-white/10 backdrop-blur-md border border-white/20 overflow-hidden animate-fade-in-up">
            <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-white/5"></div>

            <CardHeader className="text-center pb-6 relative">
              {/* Hero Icon */}
              <div className="relative mx-auto mb-6">
                <div className="w-20 h-20 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto shadow-2xl animate-fade-in-down">
                  {step === 'email' ? (
                    <ChefHat className="w-10 h-10 text-white" />
                  ) : (
                    <KeyRound className="w-10 h-10 text-white" />
                  )}
                </div>
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-400 rounded-full flex items-center justify-center animate-bounce">
                  <Star className="w-3 h-3 text-white" />
                </div>
              </div>

              <CardTitle className="text-3xl font-black text-white leading-tight animate-fade-in-up">
                {step === 'email' ? (
                  <>
                    <span className="block">Welcome Back</span>
                    <span className="block bg-gradient-to-r from-amber-300 via-orange-300 to-red-300 bg-clip-text text-transparent animate-gradient-x text-2xl">
                      Chef
                    </span>
                  </>
                ) : (
                  <>
                    <span className="block">Verify Your</span>
                    <span className="block bg-gradient-to-r from-amber-300 via-orange-300 to-red-300 bg-clip-text text-transparent animate-gradient-x text-2xl">
                      Identity
                    </span>
                  </>
                )}
              </CardTitle>

              <CardDescription className="text-gray-300 text-lg mt-4 animate-fade-in-up delay-200">
                {step === 'email'
                  ? 'Enter your email to receive a secure login code'
                  : `We've sent a verification code to ${email}`}
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-6 relative">
              {step === 'email' ? (
                <form onSubmit={handleEmailSubmit} className="space-y-6">
                  <div className="space-y-3 animate-fade-in-up delay-300">
                    <Label htmlFor="email" className="text-sm font-semibold text-white">
                      Email Address
                    </Label>
                    <div className="relative group">
                      <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-amber-400/70 group-focus-within:text-amber-300 group-hover:text-amber-300 transition-all duration-300" />
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="pl-12 h-14 bg-black/20 backdrop-blur-md border-2 border-white/20 text-white placeholder-gray-400 focus:border-amber-500/50 focus:ring-2 focus:ring-amber-500/30 focus:bg-black/30 hover:border-white/30 hover:bg-black/25 transition-all duration-300 rounded-xl shadow-lg"
                        required
                      />
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-amber-500/5 to-orange-500/5 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                      <div className="absolute inset-0 rounded-xl border border-amber-500/20 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full h-14 bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white font-bold text-lg rounded-xl transform hover:scale-105 hover:-translate-y-1 transition-all duration-300 shadow-2xl overflow-hidden group animate-fade-in-up delay-400"
                    disabled={isLoading || !email}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative flex items-center justify-center">
                      {isLoading ? (
                        <>
                          <Loader2 className="w-6 h-6 mr-3 animate-spin" />
                          Sending Code...
                        </>
                      ) : (
                        <>
                          <Mail className="w-6 h-6 mr-3 group-hover:rotate-12 transition-transform duration-300" />
                          Send Login Code
                          <ArrowRight className="w-5 h-5 ml-3 group-hover:translate-x-2 transition-transform duration-300" />
                        </>
                      )}
                    </div>
                    <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-yellow-400 to-red-400 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
                  </Button>
                </form>
              ) : (
                <form onSubmit={handleCodeSubmit} className="space-y-6">
                  {isEmailSent && (
                    <div className="flex items-center justify-center space-x-3 text-green-300 bg-green-500/20 backdrop-blur-sm border border-green-400/30 p-4 rounded-xl animate-fade-in-up">
                      <CheckCircle className="w-6 h-6" />
                      <span className="font-medium">Code sent successfully!</span>
                    </div>
                  )}

                  <div className="space-y-3 animate-fade-in-up delay-200">
                    <Label htmlFor="code" className="text-sm font-semibold text-white">
                      Verification Code
                    </Label>
                    <div className="relative group">
                      <KeyRound className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-amber-400/70 group-focus-within:text-amber-300 group-hover:text-amber-300 transition-all duration-300" />
                      <Input
                        id="code"
                        type="text"
                        placeholder="Enter 6-digit code"
                        value={code}
                        onChange={(e) => setCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                        className="pl-12 h-14 bg-black/20 backdrop-blur-md border-2 border-white/20 text-white placeholder-gray-400 focus:border-amber-500/50 focus:ring-2 focus:ring-amber-500/30 focus:bg-black/30 hover:border-white/30 hover:bg-black/25 transition-all duration-300 text-center text-xl tracking-widest rounded-xl shadow-lg"
                        maxLength={6}
                        required
                      />
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-amber-500/5 to-orange-500/5 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                      <div className="absolute inset-0 rounded-xl border border-amber-500/20 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full h-14 bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white font-bold text-lg rounded-xl transform hover:scale-105 hover:-translate-y-1 transition-all duration-300 shadow-2xl overflow-hidden group animate-fade-in-up delay-300"
                    disabled={isLoading || code.length !== 6}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative flex items-center justify-center">
                      {isLoading ? (
                        <>
                          <Loader2 className="w-6 h-6 mr-3 animate-spin" />
                          Verifying...
                        </>
                      ) : (
                        <>
                          <Shield className="w-6 h-6 mr-3 group-hover:rotate-12 transition-transform duration-300" />
                          Verify & Login
                        </>
                      )}
                    </div>
                    <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-yellow-400 to-red-400 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
                  </Button>

                  <div className="text-center space-y-4 animate-fade-in-up delay-400">
                    <button
                      type="button"
                      onClick={resendCode}
                      className="text-amber-300 hover:text-orange-300 transition-colors duration-200 hover:underline font-medium"
                      disabled={isLoading}
                    >
                      Didn&apos;t receive the code? Resend
                    </button>

                    <button
                      type="button"
                      onClick={() => {
                        setStep('email')
                        setCode('')
                        setIsEmailSent(false)
                      }}
                      className="block w-full text-gray-300 hover:text-white transition-colors duration-200 text-sm"
                    >
                      ← Change email address
                    </button>
                  </div>
                </form>
              )}

              {/* Trust Indicators */}
              <div className="flex justify-center items-center gap-6 pt-6 text-gray-300 text-sm border-t border-white/20 animate-fade-in-up delay-500">
                <div className="flex items-center">
                  <CheckCircle className="w-4 h-4 mr-2 text-green-400" />
                  Secure login
                </div>
                <div className="flex items-center">
                  <Shield className="w-4 h-4 mr-2 text-green-400" />
                  Encrypted
                </div>
              </div>

              <div className="text-center pt-4 animate-fade-in-up delay-600">
                <p className="text-gray-300">
                  Don&apos;t have an account?{' '}
                  <Link
                    href="/merchant/register"
                    className="text-amber-300 hover:text-orange-300 font-semibold transition-colors duration-200 hover:underline"
                  >
                    Join our platform
                  </Link>
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Additional decorative elements */}
          <div className="absolute -z-10 top-4 left-4 w-32 h-32 bg-gradient-to-br from-amber-500/20 to-orange-500/20 rounded-full blur-2xl animate-float"></div>
          <div className="absolute -z-10 bottom-4 right-4 w-40 h-40 bg-gradient-to-br from-orange-500/20 to-red-500/20 rounded-full blur-2xl animate-float-delayed"></div>
        </div>
      </div>
    </div>
  )
}

export default MerchantLoginPageClient
