'use client'

import React, { useState, useEffect } from 'react'
import { Layout } from '@/components/Merchant/Layout'
import {
  TrendingUp,
  TrendingDown,
  ShoppingBag,
  Users,
  DollarSign,
  Clock,
  AlertTriangle,
  Plus,
  Eye,
  Star,
  Package,
  Bell,
  ArrowUpRight,
  Activity,
  CalendarDays,
  ChefHat,
  CreditCard,
  Sparkles,
  Shield,
  Crown,
  Zap,
} from 'lucide-react'

const DashboardClient = () => {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [todayOrders] = useState(47)
  const [revenue] = useState(2847.5)
  const [customers] = useState(324)

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000)
    return () => clearInterval(timer)
  }, [])

  // Mock data for demonstration
  const alerts = [
    {
      id: 1,
      type: 'warning',
      title: 'Low Stock Alert',
      message: 'Chicken Wings running low (5 left)',
      time: '10 min ago',
    },
    {
      id: 2,
      type: 'info',
      title: 'Subscription Renewal',
      message: 'Your plan renews in 5 days',
      time: '2 hours ago',
    },
    {
      id: 3,
      type: 'success',
      title: 'New Review',
      message: '5-star review from <PERSON> <PERSON>.',
      time: '1 hour ago',
    },
  ]

  const recentOrders = [
    {
      id: '#1234',
      customer: 'John Doe',
      items: 'Burger + Fries',
      amount: '$24.50',
      status: 'preparing',
      time: '5 min ago',
    },
    {
      id: '#1235',
      customer: '<PERSON>',
      items: 'Caesar Salad',
      amount: '$16.00',
      status: 'served',
      time: '12 min ago',
    },
    {
      id: '#1236',
      customer: 'Mike Johnson',
      items: 'Pizza Margherita',
      amount: '$28.00',
      status: 'completed',
      time: '18 min ago',
    },
    {
      id: '#1237',
      customer: 'Emma Brown',
      items: 'Pasta Carbonara',
      amount: '$22.50',
      status: 'pending',
      time: '25 min ago',
    },
  ]

  const topItems = [
    { name: 'Margherita Pizza', orders: 24, revenue: '$336.00', trend: 'up' },
    { name: 'Chicken Burger', orders: 18, revenue: '$270.00', trend: 'up' },
    { name: 'Caesar Salad', orders: 15, revenue: '$240.00', trend: 'down' },
    { name: 'Pasta Carbonara', orders: 12, revenue: '$270.00', trend: 'up' },
  ]

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="relative">
          {/* Background with glassmorphic effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-amber-500/10 via-orange-500/10 to-red-500/10 backdrop-blur-xl rounded-2xl border border-amber-200/30 shadow-xl" />

          <div className="relative p-6 lg:p-8">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
              <div>
                <div className="flex items-center space-x-3 mb-2">
                  <div className="w-12 h-12 bg-gradient-to-br from-amber-400 via-orange-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25">
                    <ChefHat className="h-6 w-6 text-white drop-shadow-lg" />
                  </div>
                  <div>
                    <h1 className="text-4xl font-black bg-gradient-to-r from-amber-600 via-orange-600 to-red-600 bg-clip-text text-transparent drop-shadow-sm">
                      Dashboard Overview
                    </h1>
                    <div className="flex items-center space-x-2 mt-1">
                      <Shield className="h-4 w-4 text-blue-600" />
                      <Star className="h-4 w-4 text-amber-500 animate-pulse" />
                      <Crown className="h-4 w-4 text-yellow-600" />
                    </div>
                  </div>
                </div>
                <p className="text-gray-700 mt-2 font-medium">
                  Welcome back! Here&apos;s what&apos;s happening at your restaurant today.
                </p>
                <div className="flex items-center mt-3 px-3 py-2 bg-white/60 backdrop-blur-sm border border-amber-200/50 rounded-xl shadow-lg w-fit">
                  <Clock className="h-4 w-4 mr-2 text-amber-600" />
                  <span className="text-sm font-semibold text-gray-700">
                    {currentTime.toLocaleString()}
                  </span>
                  <Sparkles className="h-3 w-3 ml-2 text-amber-500 animate-pulse" />
                </div>
              </div>
              <div className="flex flex-col sm:flex-row gap-3 mt-6 lg:mt-0">
                <button className="group flex items-center px-6 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl hover:bg-white/90 hover:border-amber-300/70 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                  <Eye className="h-4 w-4 mr-2 text-amber-600 group-hover:text-orange-600 transition-colors" />
                  <span className="font-semibold text-gray-700 group-hover:text-gray-800">
                    View Today&apos;s Orders
                  </span>
                </button>
                <button className="group flex items-center px-6 py-3 bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 text-white rounded-xl hover:from-amber-500 hover:via-orange-600 hover:to-red-600 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-orange-400/50">
                  <Plus className="h-4 w-4 mr-2 group-hover:rotate-90 transition-transform duration-300" />
                  <span className="font-bold">Add Menu Item</span>
                  <Zap className="h-4 w-4 ml-2 animate-pulse" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Revenue Card */}
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-400/20 to-green-600/20 backdrop-blur-xl rounded-2xl border border-emerald-200/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
            <div className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-bold text-emerald-800 uppercase tracking-wide">
                    Today&apos;s Revenue
                  </p>
                  <div className="flex items-center mt-1">
                    <Star className="h-3 w-3 text-emerald-500 mr-1" />
                    <span className="text-xs text-emerald-600 font-medium">Premium</span>
                  </div>
                </div>
                <div className="relative w-12 h-12 bg-gradient-to-br from-emerald-400 to-green-600 rounded-xl flex items-center justify-center shadow-lg shadow-emerald-500/25 group-hover:scale-110 transition-transform duration-300">
                  <DollarSign className="h-6 w-6 text-white drop-shadow-lg" />
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full animate-pulse" />
                </div>
              </div>
              <div className="text-3xl font-black text-emerald-900 mb-2">
                ${revenue.toLocaleString()}
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <TrendingUp className="h-4 w-4 text-emerald-600 mr-2" />
                  <span className="text-sm text-emerald-700 font-bold">+24% from yesterday</span>
                </div>
                <Sparkles className="h-4 w-4 text-emerald-500 animate-pulse" />
              </div>
            </div>
          </div>

          {/* Orders Card */}
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-indigo-600/20 backdrop-blur-xl rounded-2xl border border-blue-200/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
            <div className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-bold text-blue-800 uppercase tracking-wide">
                    Active Orders
                  </p>
                  <div className="flex items-center mt-1">
                    <Activity className="h-3 w-3 text-blue-500 mr-1 animate-pulse" />
                    <span className="text-xs text-blue-600 font-medium">Live</span>
                  </div>
                </div>
                <div className="relative w-12 h-12 bg-gradient-to-br from-blue-400 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25 group-hover:scale-110 transition-transform duration-300">
                  <ShoppingBag className="h-6 w-6 text-white drop-shadow-lg" />
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-red-400 to-red-500 rounded-full animate-pulse flex items-center justify-center">
                    <span className="text-xs text-white font-bold">12</span>
                  </div>
                </div>
              </div>
              <div className="text-3xl font-black text-blue-900 mb-2">{todayOrders}</div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Activity className="h-4 w-4 text-blue-600 mr-2 animate-pulse" />
                  <span className="text-sm text-blue-700 font-bold">12 pending</span>
                </div>
                <Bell className="h-4 w-4 text-blue-500 animate-pulse" />
              </div>
            </div>
          </div>

          {/* Customers Card */}
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-400/20 to-pink-600/20 backdrop-blur-xl rounded-2xl border border-purple-200/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
            <div className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-bold text-purple-800 uppercase tracking-wide">
                    Happy Customers
                  </p>
                  <div className="flex items-center mt-1">
                    <Star className="h-3 w-3 text-purple-500 mr-1" />
                    <span className="text-xs text-purple-600 font-medium">5.0★ Rating</span>
                  </div>
                </div>
                <div className="relative w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-600 rounded-xl flex items-center justify-center shadow-lg shadow-purple-500/25 group-hover:scale-110 transition-transform duration-300">
                  <Users className="h-6 w-6 text-white drop-shadow-lg" />
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-emerald-400 to-green-500 rounded-full animate-pulse">
                    <Crown className="h-2.5 w-2.5 text-white absolute inset-0.5" />
                  </div>
                </div>
              </div>
              <div className="text-3xl font-black text-purple-900 mb-2">{customers}</div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <TrendingUp className="h-4 w-4 text-purple-600 mr-2" />
                  <span className="text-sm text-purple-700 font-bold">+12 new today</span>
                </div>
                <Sparkles className="h-4 w-4 text-purple-500 animate-pulse" />
              </div>
            </div>
          </div>

          {/* Average Order Value Card */}
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-orange-400/20 to-red-600/20 backdrop-blur-xl rounded-2xl border border-orange-200/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
            <div className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-bold text-orange-800 uppercase tracking-wide">
                    Avg Order Value
                  </p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-orange-500 mr-1" />
                    <span className="text-xs text-orange-600 font-medium">Growing</span>
                  </div>
                </div>
                <div className="relative w-12 h-12 bg-gradient-to-br from-orange-400 to-red-600 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25 group-hover:scale-110 transition-transform duration-300">
                  <TrendingUp className="h-6 w-6 text-white drop-shadow-lg" />
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full animate-pulse">
                    <ArrowUpRight className="h-2.5 w-2.5 text-white absolute inset-0.5" />
                  </div>
                </div>
              </div>
              <div className="text-3xl font-black text-orange-900 mb-2">$28.50</div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <TrendingUp className="h-4 w-4 text-orange-600 mr-2" />
                  <span className="text-sm text-orange-700 font-bold">+8% this week</span>
                </div>
                <Zap className="h-4 w-4 text-orange-500 animate-pulse" />
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Alerts Section */}
          <div className="lg:col-span-1">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-amber-400/10 via-orange-500/10 to-red-500/10 backdrop-blur-xl rounded-2xl border border-amber-200/30 shadow-xl" />
              <div className="relative p-6">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-10 h-10 bg-gradient-to-br from-amber-400 to-orange-600 rounded-xl flex items-center justify-center shadow-lg shadow-amber-500/25">
                    <AlertTriangle className="h-5 w-5 text-white drop-shadow-lg" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-800">Alerts & Notifications</h3>
                    <div className="flex items-center space-x-1">
                      <Bell className="h-3 w-3 text-amber-500 animate-pulse" />
                      <span className="text-xs text-amber-600 font-medium">Live Updates</span>
                    </div>
                  </div>
                </div>
                <div className="space-y-3">
                  {alerts.map((alert) => (
                    <div key={alert.id} className="relative group">
                      <div
                        className={`absolute inset-0 ${
                          alert.type === 'warning'
                            ? 'bg-gradient-to-r from-amber-400/20 to-orange-500/20'
                            : alert.type === 'info'
                              ? 'bg-gradient-to-r from-blue-400/20 to-indigo-500/20'
                              : 'bg-gradient-to-r from-emerald-400/20 to-green-500/20'
                        } backdrop-blur-sm rounded-xl border ${
                          alert.type === 'warning'
                            ? 'border-amber-200/50'
                            : alert.type === 'info'
                              ? 'border-blue-200/50'
                              : 'border-emerald-200/50'
                        } group-hover:shadow-lg transition-all duration-300`}
                      />
                      <div className="relative p-4">
                        <div className="flex justify-between items-start">
                          <div className="flex items-start space-x-3 flex-1">
                            <div
                              className={`w-2 h-2 rounded-full mt-2 ${
                                alert.type === 'warning'
                                  ? 'bg-gradient-to-r from-amber-500 to-orange-600'
                                  : alert.type === 'info'
                                    ? 'bg-gradient-to-r from-blue-500 to-indigo-600'
                                    : 'bg-gradient-to-r from-emerald-500 to-green-600'
                              } animate-pulse shadow-lg`}
                            />
                            <div className="flex-1">
                              <h4 className="font-bold text-gray-800 text-sm">{alert.title}</h4>
                              <p className="text-sm text-gray-600 mt-1">{alert.message}</p>
                            </div>
                          </div>
                          <span className="text-xs text-amber-600/70 ml-2 font-medium">
                            {alert.time}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <button className="w-full mt-4 px-4 py-3 bg-gradient-to-r from-amber-400 to-orange-500 text-white text-sm font-bold rounded-xl hover:from-amber-500 hover:to-orange-600 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                  <div className="flex items-center justify-center space-x-2">
                    <Eye className="h-4 w-4" />
                    <span>View All Alerts</span>
                    <ArrowUpRight className="h-4 w-4" />
                  </div>
                </button>
              </div>
            </div>
          </div>

          {/* Recent Orders */}
          <div className="lg:col-span-2">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-400/10 via-indigo-500/10 to-purple-500/10 backdrop-blur-xl rounded-2xl border border-blue-200/30 shadow-xl" />
              <div className="relative p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25">
                      <ShoppingBag className="h-5 w-5 text-white drop-shadow-lg" />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-gray-800">Recent Orders</h3>
                      <div className="flex items-center space-x-1">
                        <Activity className="h-3 w-3 text-blue-500 animate-pulse" />
                        <span className="text-xs text-blue-600 font-medium">Real-time</span>
                      </div>
                    </div>
                  </div>
                  <button className="px-4 py-2 bg-white/60 backdrop-blur-sm border border-blue-200/50 rounded-xl hover:bg-white/80 hover:border-blue-300/70 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                    <span className="text-sm font-bold text-blue-700">View All</span>
                  </button>
                </div>
                <div className="space-y-3">
                  {recentOrders.map((order) => (
                    <div key={order.id} className="relative group">
                      <div className="absolute inset-0 bg-white/40 backdrop-blur-sm rounded-xl border border-white/50 group-hover:bg-white/60 group-hover:shadow-lg transition-all duration-300" />
                      <div className="relative p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <span className="font-bold text-gray-800">{order.id}</span>
                              <span
                                className={`px-3 py-1 text-xs font-bold rounded-full border ${
                                  order.status === 'pending'
                                    ? 'bg-gradient-to-r from-yellow-400/20 to-amber-500/20 text-amber-800 border-amber-200/50'
                                    : order.status === 'preparing'
                                      ? 'bg-gradient-to-r from-blue-400/20 to-indigo-500/20 text-blue-800 border-blue-200/50'
                                      : order.status === 'served'
                                        ? 'bg-gradient-to-r from-emerald-400/20 to-green-500/20 text-emerald-800 border-emerald-200/50'
                                        : 'bg-gradient-to-r from-gray-400/20 to-slate-500/20 text-gray-800 border-gray-200/50'
                                } shadow-lg`}
                              >
                                {order.status.toUpperCase()}
                              </span>
                              {order.status === 'preparing' && (
                                <Zap className="h-3 w-3 text-blue-500 animate-pulse" />
                              )}
                            </div>
                            <div className="text-sm text-gray-600 font-medium">
                              <span className="font-bold text-gray-700">{order.customer}</span> •{' '}
                              {order.items}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-black text-gray-800 text-lg">{order.amount}</div>
                            <div className="text-xs text-blue-600/70 font-medium">{order.time}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Selling Items */}
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-400/10 via-pink-500/10 to-rose-500/10 backdrop-blur-xl rounded-2xl border border-purple-200/30 shadow-xl" />
            <div className="relative p-6">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-purple-400 to-pink-600 rounded-xl flex items-center justify-center shadow-lg shadow-purple-500/25">
                  <ChefHat className="h-5 w-5 text-white drop-shadow-lg" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-800">Top Selling Items Today</h3>
                  <div className="flex items-center space-x-1">
                    <Star className="h-3 w-3 text-purple-500 animate-pulse" />
                    <span className="text-xs text-purple-600 font-medium">Trending</span>
                  </div>
                </div>
              </div>
              <div className="space-y-3">
                {topItems.map((item, index) => (
                  <div key={item.name} className="relative group">
                    <div className="absolute inset-0 bg-white/40 backdrop-blur-sm rounded-xl border border-white/50 group-hover:bg-white/60 group-hover:shadow-lg transition-all duration-300" />
                    <div className="relative p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="relative w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center text-white font-black text-sm shadow-lg shadow-purple-500/25">
                            {index + 1}
                            {index === 0 && (
                              <Crown className="absolute -top-1 -right-1 h-3 w-3 text-yellow-400" />
                            )}
                          </div>
                          <div>
                            <h4 className="font-bold text-gray-800">{item.name}</h4>
                            <p className="text-sm text-purple-600 font-medium">
                              {item.orders} orders today
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-black text-gray-800">{item.revenue}</div>
                          <div className="flex items-center justify-end">
                            {item.trend === 'up' ? (
                              <div className="flex items-center space-x-1">
                                <TrendingUp className="h-3 w-3 text-emerald-500" />
                                <Sparkles className="h-2 w-2 text-emerald-400 animate-pulse" />
                              </div>
                            ) : (
                              <TrendingDown className="h-3 w-3 text-red-500" />
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-400/10 via-teal-500/10 to-green-500/10 backdrop-blur-xl rounded-2xl border border-emerald-200/30 shadow-xl" />
            <div className="relative p-6">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-emerald-400 to-teal-600 rounded-xl flex items-center justify-center shadow-lg shadow-emerald-500/25">
                  <Package className="h-5 w-5 text-white drop-shadow-lg" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-800">Quick Actions</h3>
                  <div className="flex items-center space-x-1">
                    <Zap className="h-3 w-3 text-emerald-500 animate-pulse" />
                    <span className="text-xs text-emerald-600 font-medium">One-Click</span>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <button className="group relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-indigo-500/20 backdrop-blur-sm rounded-xl border border-blue-200/50 group-hover:shadow-xl group-hover:scale-105 transition-all duration-300" />
                  <div className="relative flex flex-col items-center p-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-indigo-600 rounded-xl flex items-center justify-center mb-3 shadow-lg shadow-blue-500/25 group-hover:scale-110 transition-transform duration-300">
                      <Plus className="h-6 w-6 text-white drop-shadow-lg group-hover:rotate-90 transition-transform duration-300" />
                    </div>
                    <span className="text-sm font-bold text-gray-800">Add Menu Item</span>
                  </div>
                </button>

                <button className="group relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-emerald-400/20 to-green-500/20 backdrop-blur-sm rounded-xl border border-emerald-200/50 group-hover:shadow-xl group-hover:scale-105 transition-all duration-300" />
                  <div className="relative flex flex-col items-center p-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-emerald-400 to-green-600 rounded-xl flex items-center justify-center mb-3 shadow-lg shadow-emerald-500/25 group-hover:scale-110 transition-transform duration-300">
                      <Eye className="h-6 w-6 text-white drop-shadow-lg" />
                    </div>
                    <span className="text-sm font-bold text-gray-800">View Orders</span>
                  </div>
                </button>

                <button className="group relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-400/20 to-pink-500/20 backdrop-blur-sm rounded-xl border border-purple-200/50 group-hover:shadow-xl group-hover:scale-105 transition-all duration-300" />
                  <div className="relative flex flex-col items-center p-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-600 rounded-xl flex items-center justify-center mb-3 shadow-lg shadow-purple-500/25 group-hover:scale-110 transition-transform duration-300">
                      <Users className="h-6 w-6 text-white drop-shadow-lg" />
                    </div>
                    <span className="text-sm font-bold text-gray-800">Customers</span>
                  </div>
                </button>

                <button className="group relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-orange-400/20 to-red-500/20 backdrop-blur-sm rounded-xl border border-orange-200/50 group-hover:shadow-xl group-hover:scale-105 transition-all duration-300" />
                  <div className="relative flex flex-col items-center p-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-red-600 rounded-xl flex items-center justify-center mb-3 shadow-lg shadow-orange-500/25 group-hover:scale-110 transition-transform duration-300">
                      <CreditCard className="h-6 w-6 text-white drop-shadow-lg" />
                    </div>
                    <span className="text-sm font-bold text-gray-800">Reports</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Weekly Performance Chart */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-br from-indigo-400/10 via-purple-500/10 to-blue-500/10 backdrop-blur-xl rounded-2xl border border-indigo-200/30 shadow-xl" />
          <div className="relative p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-indigo-400 to-purple-600 rounded-xl flex items-center justify-center shadow-lg shadow-indigo-500/25">
                  <CalendarDays className="h-5 w-5 text-white drop-shadow-lg" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-800">Weekly Performance</h3>
                  <div className="flex items-center space-x-1">
                    <Activity className="h-3 w-3 text-indigo-500 animate-pulse" />
                    <span className="text-xs text-indigo-600 font-medium">Analytics</span>
                  </div>
                </div>
              </div>
              <div className="flex space-x-4">
                <div className="flex items-center space-x-2 px-3 py-1.5 bg-white/60 backdrop-blur-sm border border-blue-200/50 rounded-xl shadow-lg">
                  <div className="w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full shadow-lg" />
                  <span className="text-sm font-bold text-gray-700">Orders</span>
                </div>
                <div className="flex items-center space-x-2 px-3 py-1.5 bg-white/60 backdrop-blur-sm border border-emerald-200/50 rounded-xl shadow-lg">
                  <div className="w-3 h-3 bg-gradient-to-r from-emerald-400 to-emerald-600 rounded-full shadow-lg" />
                  <span className="text-sm font-bold text-gray-700">Revenue</span>
                </div>
              </div>
            </div>
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-50/80 via-indigo-50/80 to-purple-50/80 backdrop-blur-sm rounded-2xl border border-white/50 shadow-inner" />
              <div className="relative h-72 flex items-end justify-between p-8 space-x-4">
                {/* Monday */}
                <div className="flex flex-col items-center space-y-3 group">
                  <div className="relative">
                    <div
                      className="w-10 bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-xl transition-all duration-500 hover:scale-105 hover:shadow-xl shadow-lg shadow-blue-500/30 group-hover:from-blue-600 group-hover:to-blue-500"
                      style={{ height: '120px' }}
                    />
                    <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300">
                      <div className="px-2 py-1 bg-blue-600 text-white text-xs rounded-lg font-bold shadow-lg">
                        42 orders
                      </div>
                    </div>
                  </div>
                  <span className="text-sm font-bold text-gray-700">Mon</span>
                </div>

                {/* Tuesday */}
                <div className="flex flex-col items-center space-y-3 group">
                  <div className="relative">
                    <div
                      className="w-10 bg-gradient-to-t from-emerald-500 to-emerald-400 rounded-t-xl transition-all duration-500 hover:scale-105 hover:shadow-xl shadow-lg shadow-emerald-500/30 group-hover:from-emerald-600 group-hover:to-emerald-500"
                      style={{ height: '150px' }}
                    />
                    <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300">
                      <div className="px-2 py-1 bg-emerald-600 text-white text-xs rounded-lg font-bold shadow-lg">
                        $3,240
                      </div>
                    </div>
                  </div>
                  <span className="text-sm font-bold text-gray-700">Tue</span>
                </div>

                {/* Wednesday */}
                <div className="flex flex-col items-center space-y-3 group">
                  <div className="relative">
                    <div
                      className="w-10 bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-xl transition-all duration-500 hover:scale-105 hover:shadow-xl shadow-lg shadow-blue-500/30 group-hover:from-blue-600 group-hover:to-blue-500"
                      style={{ height: '90px' }}
                    />
                    <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300">
                      <div className="px-2 py-1 bg-blue-600 text-white text-xs rounded-lg font-bold shadow-lg">
                        28 orders
                      </div>
                    </div>
                  </div>
                  <span className="text-sm font-bold text-gray-700">Wed</span>
                </div>

                {/* Thursday */}
                <div className="flex flex-col items-center space-y-3 group">
                  <div className="relative">
                    <div
                      className="w-10 bg-gradient-to-t from-emerald-500 to-emerald-400 rounded-t-xl transition-all duration-500 hover:scale-105 hover:shadow-xl shadow-lg shadow-emerald-500/30 group-hover:from-emerald-600 group-hover:to-emerald-500"
                      style={{ height: '180px' }}
                    />
                    <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300">
                      <div className="px-2 py-1 bg-emerald-600 text-white text-xs rounded-lg font-bold shadow-lg">
                        $4,180
                      </div>
                    </div>
                  </div>
                  <span className="text-sm font-bold text-gray-700">Thu</span>
                </div>

                {/* Friday */}
                <div className="flex flex-col items-center space-y-3 group">
                  <div className="relative">
                    <div
                      className="w-10 bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-xl transition-all duration-500 hover:scale-105 hover:shadow-xl shadow-lg shadow-blue-500/30 group-hover:from-blue-600 group-hover:to-blue-500"
                      style={{ height: '200px' }}
                    />
                    <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300">
                      <div className="px-2 py-1 bg-blue-600 text-white text-xs rounded-lg font-bold shadow-lg">
                        67 orders
                      </div>
                    </div>
                    <Crown className="absolute -top-1 left-1/2 transform -translate-x-1/2 h-4 w-4 text-yellow-400 animate-pulse" />
                  </div>
                  <span className="text-sm font-bold text-gray-700">Fri</span>
                </div>

                {/* Saturday */}
                <div className="flex flex-col items-center space-y-3 group">
                  <div className="relative">
                    <div
                      className="w-10 bg-gradient-to-t from-emerald-500 to-emerald-400 rounded-t-xl transition-all duration-500 hover:scale-105 hover:shadow-xl shadow-lg shadow-emerald-500/30 group-hover:from-emerald-600 group-hover:to-emerald-500"
                      style={{ height: '170px' }}
                    />
                    <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300">
                      <div className="px-2 py-1 bg-emerald-600 text-white text-xs rounded-lg font-bold shadow-lg">
                        $3,890
                      </div>
                    </div>
                  </div>
                  <span className="text-sm font-bold text-gray-700">Sat</span>
                </div>

                {/* Sunday */}
                <div className="flex flex-col items-center space-y-3 group">
                  <div className="relative">
                    <div
                      className="w-10 bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-xl transition-all duration-500 hover:scale-105 hover:shadow-xl shadow-lg shadow-blue-500/30 group-hover:from-blue-600 group-hover:to-blue-500"
                      style={{ height: '160px' }}
                    />
                    <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300">
                      <div className="px-2 py-1 bg-blue-600 text-white text-xs rounded-lg font-bold shadow-lg">
                        52 orders
                      </div>
                    </div>
                  </div>
                  <span className="text-sm font-bold text-gray-700">Sun</span>
                </div>
              </div>
            </div>
            <div className="mt-4 flex items-center justify-center">
              <div className="flex items-center space-x-2 px-4 py-2 bg-white/60 backdrop-blur-sm border border-indigo-200/50 rounded-xl shadow-lg">
                <TrendingUp className="h-4 w-4 text-emerald-600" />
                <span className="text-sm font-bold text-gray-700">+18% vs last week</span>
                <Sparkles className="h-3 w-3 text-indigo-500 animate-pulse" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}

export default DashboardClient
