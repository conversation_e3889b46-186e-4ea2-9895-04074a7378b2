'use client'

import React, { useState, useEffect } from 'react'
import { Layout } from '@/components/Merchant/Layout'
import {
  Users,
  Search,
  Filter,
  Star,
  DollarSign,
  Heart,
  TrendingUp,
  Calendar,
  MapPin,
  Phone,
  Mail,
  Crown,
  Eye,
  Sparkles,
  Zap,
  Award,
  Target,
  BarChart3,
  UserCheck,
  Star as StarIcon,
  RefreshCw,
  Download,
  X,
  CheckCircle,
} from 'lucide-react'

// TypeScript interfaces for customer management
interface CustomerOrder {
  id: string
  orderNumber: string
  totalAmount: number
  status: 'completed' | 'cancelled' | 'pending'
  createdAt: Date
  items: Array<{
    name: string
    quantity: number
    price: number
  }>
}

interface CustomerFeedback {
  id: string
  rating: number
  comment: string
  createdAt: Date
  orderId: string
  isVerified: boolean
}

interface Customer {
  id: string
  name: string
  email: string
  phone: string
  address?: string
  isVip: boolean
  joinDate: Date
  totalOrders: number
  totalSpent: number
  averageOrderValue: number
  lastOrderDate?: Date
  favoriteItems: string[]
  orders: CustomerOrder[]
  feedback: CustomerFeedback[]
  loyaltyPoints: number
  tier: 'bronze' | 'silver' | 'gold' | 'platinum'
}

// Mock data generation
const generateMockCustomers = (): Customer[] => {
  const baseCustomers = [
    {
      id: '1',
      name: '<PERSON> <PERSON>e',
      email: '<EMAIL>',
      phone: '+1234567890',
      address: '123 Main St, City, State 12345',
      isVip: true,
      joinDate: new Date('2023-01-15'),
      totalOrders: 24,
      totalSpent: 1250.5,
      averageOrderValue: 52.1,
      lastOrderDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      favoriteItems: ['Margherita Pizza', 'Caesar Salad', 'Chocolate Cake'],
      loyaltyPoints: 1250,
      tier: 'platinum' as const,
    },
    {
      id: '2',
      name: 'Jane Smith',
      email: '<EMAIL>',
      phone: '+0987654321',
      address: '456 Oak Ave, City, State 12345',
      isVip: false,
      joinDate: new Date('2023-06-20'),
      totalOrders: 12,
      totalSpent: 480.75,
      averageOrderValue: 40.06,
      lastOrderDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      favoriteItems: ['Grilled Chicken', 'Pasta Carbonara'],
      loyaltyPoints: 480,
      tier: 'silver' as const,
    },
    {
      id: '3',
      name: 'Mike Johnson',
      email: '<EMAIL>',
      phone: '+1122334455',
      address: '789 Pine Rd, City, State 12345',
      isVip: true,
      joinDate: new Date('2022-11-10'),
      totalOrders: 45,
      totalSpent: 2100.25,
      averageOrderValue: 46.67,
      lastOrderDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      favoriteItems: ['Margherita Pizza', 'Grilled Chicken', 'Caesar Salad'],
      loyaltyPoints: 2100,
      tier: 'platinum' as const,
    },
    {
      id: '4',
      name: 'Sarah Wilson',
      email: '<EMAIL>',
      phone: '+5566778899',
      address: '321 Elm St, City, State 12345',
      isVip: false,
      joinDate: new Date('2023-09-05'),
      totalOrders: 8,
      totalSpent: 320.4,
      averageOrderValue: 40.05,
      lastOrderDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      favoriteItems: ['Pasta Carbonara', 'Chocolate Cake'],
      loyaltyPoints: 320,
      tier: 'bronze' as const,
    },
  ]

  // Generate mock orders and feedback for each customer
  return baseCustomers.map((customer) => {
    const orders: CustomerOrder[] = Array.from(
      { length: Math.min(customer.totalOrders, 10) },
      (_, i) => ({
        id: `order-${customer.id}-${i + 1}`,
        orderNumber: `ORD-${String(customer.id).padStart(3, '0')}-${String(i + 1).padStart(3, '0')}`,
        totalAmount: Math.random() * 100 + 20,
        status: Math.random() > 0.1 ? 'completed' : 'cancelled',
        createdAt: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),
        items: [
          { name: customer.favoriteItems[0] || 'Pizza', quantity: 1, price: 15.99 },
          { name: 'Drink', quantity: 1, price: 3.99 },
        ],
      }),
    )

    const feedback: CustomerFeedback[] = orders
      .filter((order) => order.status === 'completed')
      .slice(0, Math.floor(Math.random() * 5) + 1)
      .map((order, i) => ({
        id: `feedback-${customer.id}-${i + 1}`,
        rating: Math.floor(Math.random() * 3) + 3, // 3-5 stars
        comment: [
          'Great food and fast delivery!',
          'Excellent service, will order again.',
          'Good quality, reasonable price.',
          'Amazing taste, highly recommended!',
          'Fast delivery and fresh food.',
        ][Math.floor(Math.random() * 5)],
        createdAt: new Date(order.createdAt.getTime() + Math.random() * 24 * 60 * 60 * 1000),
        orderId: order.id,
        isVerified: Math.random() > 0.3,
      }))

    return {
      ...customer,
      orders,
      feedback,
    }
  })
}

const CustomersPageClient = () => {
  const [customers, setCustomers] = useState<Customer[]>([])
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([])
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [showCustomerDetails, setShowCustomerDetails] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterTier, setFilterTier] = useState<string>('all')
  const [sortBy, setSortBy] = useState<'name' | 'totalSpent' | 'totalOrders' | 'lastOrder'>('name')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')

  // Initialize customers data
  useEffect(() => {
    const mockCustomers = generateMockCustomers()
    setCustomers(mockCustomers)
    setFilteredCustomers(mockCustomers)
  }, [])

  // Filter and sort customers
  useEffect(() => {
    let filtered = customers

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(
        (customer) =>
          customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          customer.phone.includes(searchTerm),
      )
    }

    // Filter by tier
    if (filterTier !== 'all') {
      filtered = filtered.filter((customer) => customer.tier === filterTier)
    }

    // Sort customers
    filtered.sort((a, b) => {
      let aValue: string | number, bValue: string | number

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase()
          bValue = b.name.toLowerCase()
          break
        case 'totalSpent':
          aValue = a.totalSpent
          bValue = b.totalSpent
          break
        case 'totalOrders':
          aValue = a.totalOrders
          bValue = b.totalOrders
          break
        case 'lastOrder':
          aValue = a.lastOrderDate?.getTime() || 0
          bValue = b.lastOrderDate?.getTime() || 0
          break
        default:
          aValue = a.name.toLowerCase()
          bValue = b.name.toLowerCase()
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    setFilteredCustomers(filtered)
  }, [customers, searchTerm, filterTier, sortBy, sortOrder])

  const getTierColor = (tier: Customer['tier']) => {
    switch (tier) {
      case 'bronze':
        return 'from-amber-600/20 to-orange-700/20 text-amber-800 border-amber-300/50'
      case 'silver':
        return 'from-gray-400/20 to-gray-600/20 text-gray-800 border-gray-300/50'
      case 'gold':
        return 'from-yellow-400/20 to-yellow-600/20 text-yellow-800 border-yellow-300/50'
      case 'platinum':
        return 'from-purple-400/20 to-purple-600/20 text-purple-800 border-purple-300/50'
      default:
        return 'from-gray-400/20 to-gray-600/20 text-gray-800 border-gray-300/50'
    }
  }

  const getTierIcon = (tier: Customer['tier']) => {
    switch (tier) {
      case 'bronze':
        return <Award className="h-4 w-4" />
      case 'silver':
        return <Award className="h-4 w-4" />
      case 'gold':
        return <Crown className="h-4 w-4" />
      case 'platinum':
        return <Crown className="h-4 w-4" />
      default:
        return <Award className="h-4 w-4" />
    }
  }

  const customerStats = {
    total: customers.length,
    vip: customers.filter((c) => c.isVip).length,
    newThisMonth: customers.filter(
      (c) => c.joinDate > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    ).length,
    totalRevenue: customers.reduce((sum, c) => sum + c.totalSpent, 0),
    averageOrderValue:
      customers.reduce((sum, c) => sum + c.averageOrderValue, 0) / customers.length,
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-amber-500/10 via-orange-500/10 to-red-500/10 backdrop-blur-xl rounded-2xl border border-amber-200/30 shadow-xl" />
          <div className="relative p-6 lg:p-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-amber-400 via-orange-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25">
                  <Users className="h-6 w-6 text-white drop-shadow-lg" />
                </div>
                <div>
                  <h1 className="text-4xl font-black bg-gradient-to-r from-amber-600 via-orange-600 to-red-600 bg-clip-text text-transparent drop-shadow-sm">
                    Customer Management
                  </h1>
                  <div className="flex items-center space-x-1 mt-1">
                    <Star className="h-3 w-3 text-amber-500 animate-pulse" />
                    <span className="text-xs text-amber-600 font-medium">
                      Customer Insights & Analytics
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <button className="group flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl hover:bg-white/90 hover:border-amber-300/70 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                  <Download className="h-4 w-4 mr-2 text-amber-600 group-hover:text-orange-600 transition-colors" />
                  <span className="font-semibold text-gray-700 group-hover:text-gray-800">
                    Export
                  </span>
                </button>
                <button className="group flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl hover:bg-white/90 hover:border-amber-300/70 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                  <RefreshCw className="h-4 w-4 mr-2 text-amber-600 group-hover:text-orange-600 group-hover:rotate-180 transition-all duration-300" />
                  <span className="font-semibold text-gray-700 group-hover:text-gray-800">
                    Refresh
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Customer Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-indigo-600/20 backdrop-blur-xl rounded-2xl border border-blue-500/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
            <div className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-bold text-blue-600 uppercase tracking-wide">
                    Total Customers
                  </p>
                  <div className="flex items-center mt-1">
                    <Star className="h-3 w-3 text-blue-500 mr-1" />
                    <span className="text-xs text-blue-600 font-medium">All Time</span>
                  </div>
                </div>
                <div className="relative w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25 group-hover:scale-110 transition-transform duration-300">
                  <Users className="h-6 w-6 text-white drop-shadow-lg" />
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full animate-pulse" />
                </div>
              </div>
              <div className="text-3xl font-black text-blue-600 mb-2">{customerStats.total}</div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <TrendingUp className="h-4 w-4 text-blue-500 mr-2" />
                  <span className="text-sm text-blue-600 font-bold">+8%</span>
                </div>
                <Sparkles className="h-4 w-4 text-blue-500 animate-pulse" />
              </div>
            </div>
          </div>

          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-pink-600/20 backdrop-blur-xl rounded-2xl border border-purple-500/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
            <div className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-bold text-purple-600 uppercase tracking-wide">
                    VIP Customers
                  </p>
                  <div className="flex items-center mt-1">
                    <Crown className="h-3 w-3 text-purple-500 mr-1 animate-pulse" />
                    <span className="text-xs text-purple-600 font-medium">Premium</span>
                  </div>
                </div>
                <div className="relative w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg shadow-purple-500/25 group-hover:scale-110 transition-transform duration-300">
                  <Crown className="h-6 w-6 text-white drop-shadow-lg" />
                </div>
              </div>
              <div className="text-3xl font-black text-purple-600 mb-2">{customerStats.vip}</div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Heart className="h-4 w-4 text-purple-500 mr-2" />
                  <span className="text-sm text-purple-600 font-bold">Loyal</span>
                </div>
                <Zap className="h-4 w-4 text-purple-500 animate-pulse" />
              </div>
            </div>
          </div>

          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/20 to-green-600/20 backdrop-blur-xl rounded-2xl border border-emerald-500/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
            <div className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-bold text-emerald-600 uppercase tracking-wide">
                    New This Month
                  </p>
                  <div className="flex items-center mt-1">
                    <Calendar className="h-3 w-3 text-emerald-500 mr-1" />
                    <span className="text-xs text-emerald-600 font-medium">Growth</span>
                  </div>
                </div>
                <div className="relative w-12 h-12 bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg shadow-emerald-500/25 group-hover:scale-110 transition-transform duration-300">
                  <UserCheck className="h-6 w-6 text-white drop-shadow-lg" />
                </div>
              </div>
              <div className="text-3xl font-black text-emerald-600 mb-2">
                {customerStats.newThisMonth}
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <TrendingUp className="h-4 w-4 text-emerald-500 mr-2" />
                  <span className="text-sm text-emerald-600 font-bold">+12%</span>
                </div>
                <Sparkles className="h-4 w-4 text-emerald-500 animate-pulse" />
              </div>
            </div>
          </div>

          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-green-500/20 to-emerald-600/20 backdrop-blur-xl rounded-2xl border border-green-500/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
            <div className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-bold text-green-600 uppercase tracking-wide">
                    Total Revenue
                  </p>
                  <div className="flex items-center mt-1">
                    <DollarSign className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600 font-medium">Lifetime</span>
                  </div>
                </div>
                <div className="relative w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg shadow-green-500/25 group-hover:scale-110 transition-transform duration-300">
                  <DollarSign className="h-6 w-6 text-white drop-shadow-lg" />
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full animate-pulse" />
                </div>
              </div>
              <div className="text-3xl font-black text-green-600 mb-2">
                ${customerStats.totalRevenue.toFixed(0)}
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <TrendingUp className="h-4 w-4 text-green-500 mr-2" />
                  <span className="text-sm text-green-600 font-bold">+15%</span>
                </div>
                <Crown className="h-4 w-4 text-green-500 animate-pulse" />
              </div>
            </div>
          </div>

          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-br from-orange-500/20 to-red-600/20 backdrop-blur-xl rounded-2xl border border-orange-500/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
            <div className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-bold text-orange-600 uppercase tracking-wide">
                    Avg Order Value
                  </p>
                  <div className="flex items-center mt-1">
                    <BarChart3 className="h-3 w-3 text-orange-500 mr-1" />
                    <span className="text-xs text-orange-600 font-medium">Per Order</span>
                  </div>
                </div>
                <div className="relative w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25 group-hover:scale-110 transition-transform duration-300">
                  <Target className="h-6 w-6 text-white drop-shadow-lg" />
                </div>
              </div>
              <div className="text-3xl font-black text-orange-600 mb-2">
                ${customerStats.averageOrderValue.toFixed(0)}
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <TrendingUp className="h-4 w-4 text-orange-500 mr-2" />
                  <span className="text-sm text-orange-600 font-bold">+5%</span>
                </div>
                <Sparkles className="h-4 w-4 text-orange-500 animate-pulse" />
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-white/80 via-amber-50/80 to-orange-50/80 backdrop-blur-xl rounded-2xl border border-amber-200/30 shadow-xl" />
          <div className="relative p-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              <div className="flex flex-wrap items-center space-x-2">
                <Filter className="h-5 w-5 text-amber-600" />
                <span className="text-sm font-bold text-gray-700 mr-4">Filter by Tier:</span>
                {['all', 'bronze', 'silver', 'gold', 'platinum'].map((tier) => (
                  <button
                    key={tier}
                    onClick={() => setFilterTier(tier)}
                    className={`px-4 py-2 rounded-xl text-sm font-bold transition-all duration-300 transform hover:scale-105 ${
                      filterTier === tier
                        ? 'bg-gradient-to-r from-amber-400 to-orange-500 text-white shadow-lg shadow-amber-500/25'
                        : 'bg-white/80 text-gray-600 hover:bg-white/90 hover:text-gray-800 border border-amber-200/50'
                    }`}
                  >
                    {tier.charAt(0).toUpperCase() + tier.slice(1)}
                  </button>
                ))}
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-bold text-gray-700">Sort by:</span>
                  <select
                    value={sortBy}
                    onChange={(e) =>
                      setSortBy(
                        e.target.value as 'name' | 'totalSpent' | 'totalOrders' | 'lastOrder',
                      )
                    }
                    className="px-3 py-2 bg-white/80 border border-amber-200/50 rounded-xl text-sm font-semibold text-gray-700 focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50"
                  >
                    <option value="name">Name</option>
                    <option value="totalSpent">Total Spent</option>
                    <option value="totalOrders">Total Orders</option>
                    <option value="lastOrder">Last Order</option>
                  </select>
                  <button
                    onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                    className="p-2 text-amber-600 hover:text-orange-600 hover:bg-white/50 rounded-xl transition-all duration-300"
                  >
                    <TrendingUp
                      className={`h-4 w-4 transition-transform duration-300 ${sortOrder === 'desc' ? 'rotate-180' : ''}`}
                    />
                  </button>
                </div>

                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-orange-500/20 rounded-xl blur-sm group-focus-within:blur-md group-focus-within:from-amber-400/30 group-focus-within:to-orange-500/30 transition-all duration-300" />
                  <div className="relative flex items-center">
                    <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-amber-600 group-focus-within:text-orange-600 transition-colors z-10" />
                    <input
                      type="text"
                      placeholder="Search customers..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-12 pr-4 py-3 w-full lg:w-80 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg focus:shadow-xl"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Customers List */}
        <div className="space-y-4">
          {filteredCustomers.length === 0 ? (
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-gray-100/80 to-gray-200/80 backdrop-blur-xl rounded-2xl border border-gray-200/50 shadow-xl" />
              <div className="relative p-12 text-center">
                <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-600 mb-2">No Customers Found</h3>
                <p className="text-gray-500">No customers match your current filter criteria.</p>
              </div>
            </div>
          ) : (
            filteredCustomers.map((customer, index) => (
              <div
                key={customer.id}
                className="relative group animate-fade-in-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/90 via-amber-50/90 to-orange-50/90 backdrop-blur-xl rounded-2xl border border-amber-200/30 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
                <div className="relative p-6">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                    {/* Customer Info */}
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="flex items-center space-x-2">
                          <h3 className="text-xl font-black text-gray-800">{customer.name}</h3>
                          {customer.isVip && (
                            <div className="flex items-center px-2 py-1 bg-gradient-to-r from-yellow-400/20 to-amber-500/20 rounded-full border border-yellow-400/50">
                              <Crown className="h-3 w-3 text-yellow-600 mr-1" />
                              <span className="text-xs font-bold text-yellow-700">VIP</span>
                            </div>
                          )}
                          <span
                            className={`px-3 py-1 text-xs font-bold rounded-full border shadow-lg ${getTierColor(customer.tier)}`}
                          >
                            <div className="flex items-center space-x-1">
                              {getTierIcon(customer.tier)}
                              <span>{customer.tier.toUpperCase()}</span>
                            </div>
                          </span>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div className="flex items-center space-x-2">
                          <Mail className="h-4 w-4 text-blue-600" />
                          <div>
                            <p className="text-sm font-bold text-gray-800">{customer.email}</p>
                            <p className="text-xs text-gray-600">Email</p>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Phone className="h-4 w-4 text-green-600" />
                          <div>
                            <p className="text-sm font-bold text-gray-800">{customer.phone}</p>
                            <p className="text-xs text-gray-600">Phone</p>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <DollarSign className="h-4 w-4 text-emerald-600" />
                          <div>
                            <p className="text-sm font-bold text-gray-800">
                              ${customer.totalSpent.toFixed(2)}
                            </p>
                            <p className="text-xs text-gray-600">{customer.totalOrders} orders</p>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-orange-600" />
                          <div>
                            <p className="text-sm font-bold text-gray-800">
                              {customer.lastOrderDate
                                ? customer.lastOrderDate.toLocaleDateString()
                                : 'Never'}
                            </p>
                            <p className="text-xs text-gray-600">Last Order</p>
                          </div>
                        </div>
                      </div>

                      {customer.address && (
                        <div className="flex items-center space-x-2 mt-3">
                          <MapPin className="h-4 w-4 text-red-600" />
                          <p className="text-sm text-gray-600">{customer.address}</p>
                        </div>
                      )}

                      <div className="flex items-center space-x-4 mt-3">
                        <div className="flex items-center space-x-1">
                          <StarIcon className="h-4 w-4 text-yellow-500" />
                          <span className="text-sm font-bold text-gray-700">
                            {customer.loyaltyPoints} pts
                          </span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Heart className="h-4 w-4 text-red-500" />
                          <span className="text-sm text-gray-600">
                            {customer.favoriteItems.slice(0, 2).join(', ')}
                            {customer.favoriteItems.length > 2 && '...'}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => {
                          setSelectedCustomer(customer)
                          setShowCustomerDetails(true)
                        }}
                        className="group flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl hover:bg-white/90 hover:border-amber-300/70 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
                      >
                        <Eye className="h-4 w-4 mr-2 text-amber-600 group-hover:text-orange-600 transition-colors" />
                        <span className="font-semibold text-gray-700 group-hover:text-gray-800">
                          View Details
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Customer Details Modal */}
        {showCustomerDetails && selectedCustomer && (
          <div className="fixed inset-0 bg-gradient-to-br from-black/60 via-amber-900/40 to-orange-900/30 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="relative max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-white/95 via-amber-50/95 to-orange-50/95 backdrop-blur-xl rounded-2xl border border-amber-200/50 shadow-2xl" />
              <div className="relative p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-amber-400 via-orange-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25">
                      <Users className="h-5 w-5 text-white drop-shadow-lg" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-black text-gray-800">Customer Details</h3>
                      <p className="text-sm text-gray-600">{selectedCustomer.name}</p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowCustomerDetails(false)}
                    className="p-2 text-gray-500 hover:text-gray-700 hover:bg-white/50 rounded-xl transition-all duration-300"
                  >
                    <X className="h-6 w-6" />
                  </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Customer Information */}
                  <div className="space-y-6">
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-400/10 to-indigo-500/10 backdrop-blur-xl rounded-xl border border-blue-200/30 shadow-lg" />
                      <div className="relative p-4">
                        <h4 className="text-lg font-bold text-gray-800 mb-3">
                          Contact Information
                        </h4>
                        <div className="space-y-3">
                          <div className="flex items-center space-x-2">
                            <Mail className="h-4 w-4 text-blue-600" />
                            <span className="text-gray-700">{selectedCustomer.email}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Phone className="h-4 w-4 text-green-600" />
                            <span className="text-gray-700">{selectedCustomer.phone}</span>
                          </div>
                          {selectedCustomer.address && (
                            <div className="flex items-center space-x-2">
                              <MapPin className="h-4 w-4 text-red-600" />
                              <span className="text-gray-700">{selectedCustomer.address}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-green-400/10 to-emerald-500/10 backdrop-blur-xl rounded-xl border border-green-200/30 shadow-lg" />
                      <div className="relative p-4">
                        <h4 className="text-lg font-bold text-gray-800 mb-3">
                          Loyalty & Preferences
                        </h4>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="text-gray-700">Loyalty Points:</span>
                            <span className="font-bold text-green-600">
                              {selectedCustomer.loyaltyPoints} pts
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-gray-700">Tier:</span>
                            <span
                              className={`px-2 py-1 text-xs font-bold rounded-full ${getTierColor(selectedCustomer.tier)}`}
                            >
                              {selectedCustomer.tier.toUpperCase()}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-700">Favorite Items:</span>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {selectedCustomer.favoriteItems.map((item, index) => (
                                <span
                                  key={index}
                                  className="px-2 py-1 bg-amber-100 text-amber-800 text-xs rounded-full"
                                >
                                  {item}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Order History */}
                  <div className="space-y-6">
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-purple-400/10 to-pink-500/10 backdrop-blur-xl rounded-xl border border-purple-200/30 shadow-lg" />
                      <div className="relative p-4">
                        <h4 className="text-lg font-bold text-gray-800 mb-3">Order History</h4>
                        <div className="space-y-3 max-h-60 overflow-y-auto">
                          {selectedCustomer.orders.slice(0, 5).map((order) => (
                            <div
                              key={order.id}
                              className="flex items-center justify-between p-3 bg-white/50 rounded-lg border border-purple-200/30"
                            >
                              <div>
                                <p className="font-semibold text-gray-800">{order.orderNumber}</p>
                                <p className="text-sm text-gray-600">
                                  {order.createdAt.toLocaleDateString()}
                                </p>
                              </div>
                              <div className="text-right">
                                <p className="font-bold text-gray-800">
                                  ${order.totalAmount.toFixed(2)}
                                </p>
                                <span
                                  className={`px-2 py-1 text-xs font-bold rounded-full ${
                                    order.status === 'completed'
                                      ? 'bg-green-100 text-green-800'
                                      : order.status === 'cancelled'
                                        ? 'bg-red-100 text-red-800'
                                        : 'bg-yellow-100 text-yellow-800'
                                  }`}
                                >
                                  {order.status.toUpperCase()}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Feedback & Ratings */}
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/10 to-orange-500/10 backdrop-blur-xl rounded-xl border border-yellow-200/30 shadow-lg" />
                      <div className="relative p-4">
                        <h4 className="text-lg font-bold text-gray-800 mb-3">Feedback & Ratings</h4>
                        <div className="space-y-3 max-h-60 overflow-y-auto">
                          {selectedCustomer.feedback.length > 0 ? (
                            selectedCustomer.feedback.map((feedback) => (
                              <div
                                key={feedback.id}
                                className="p-3 bg-white/50 rounded-lg border border-yellow-200/30"
                              >
                                <div className="flex items-center justify-between mb-2">
                                  <div className="flex items-center space-x-1">
                                    {[...Array(5)].map((_, i) => (
                                      <StarIcon
                                        key={i}
                                        className={`h-4 w-4 ${
                                          i < feedback.rating
                                            ? 'text-yellow-500 fill-current'
                                            : 'text-gray-300'
                                        }`}
                                      />
                                    ))}
                                  </div>
                                  <span className="text-xs text-gray-500">
                                    {feedback.createdAt.toLocaleDateString()}
                                  </span>
                                </div>
                                <p className="text-sm text-gray-700">{feedback.comment}</p>
                                {feedback.isVerified && (
                                  <div className="flex items-center space-x-1 mt-2">
                                    <CheckCircle className="h-3 w-3 text-green-500" />
                                    <span className="text-xs text-green-600 font-medium">
                                      Verified Purchase
                                    </span>
                                  </div>
                                )}
                              </div>
                            ))
                          ) : (
                            <p className="text-gray-500 text-center py-4">No feedback yet</p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  )
}

export default CustomersPageClient
