'use client'

import React, { useState } from 'react'
import {
  MapPin,
  Search,
  User,
  Bell,
  Heart,
  ChevronDown,
  Navigation,
  Clock,
  Star,
} from 'lucide-react'
import { cn } from '@/utilities/ui'
import { SearchInput } from '@/components/ui/search-input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

interface HeaderProps {
  className?: string
}

const Header: React.FC<HeaderProps> = ({ className }) => {
  const [isSearchFocused, setIsSearchFocused] = useState(false)
  const [currentLocation] = useState('Current Location')
  const [searchQuery, setSearchQuery] = useState('')

  const handleLocationClick = () => {
    // TODO: Implement location selection
    console.log('Location clicked')
  }

  const handleSearchFocus = () => {
    setIsSearchFocused(true)
  }

  const handleSearchBlur = () => {
    setIsSearchFocused(false)
  }

  const handleFilterClick = () => {
    // TODO: Implement filter modal
    console.log('Filter clicked')
  }

  const handleNotificationClick = () => {
    // TODO: Implement notifications
    console.log('Notifications clicked')
  }

  const handleFavoritesClick = () => {
    // TODO: Navigate to favorites
    console.log('Favorites clicked')
  }

  const handleProfileClick = () => {
    // TODO: Navigate to profile
    console.log('Profile clicked')
  }

  return (
    <header
      className={cn(
        'sticky top-0 z-50 w-full backdrop-blur-xl bg-background/80 border-b border-border',
        'transition-all duration-300',
        isSearchFocused && 'shadow-lg',
        className,
      )}
    >
      <div className="px-4 py-4 space-y-4">
        {/* Top Row - Location and User Actions */}
        <div className="flex items-center justify-between">
          {/* Location Selector */}
          <button
            onClick={handleLocationClick}
            className="flex items-center gap-3 backdrop-blur-xl bg-card/50 border border-border rounded-2xl px-4 py-3 transition-all duration-300 hover:-translate-y-1 hover:shadow-xl group"
            aria-label="Change delivery location"
          >
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-primary group-hover:text-orange-600 transition-colors" />
              <div className="text-left">
                <p className="text-xs text-muted-foreground group-hover:text-foreground transition-colors">
                  Deliver to
                </p>
                <p className="text-sm font-semibold text-foreground group-hover:text-primary transition-colors">
                  {currentLocation}
                </p>
              </div>
            </div>
            <ChevronDown className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors" />
          </button>

          {/* User Actions */}
          <div className="flex items-center gap-2">
            {/* Notifications */}
            <button
              onClick={handleNotificationClick}
              className="relative backdrop-blur-xl bg-card/50 border border-border transition-all duration-300 hover:-translate-y-1 hover:shadow-xl h-12 w-12 rounded-2xl flex items-center justify-center group"
              aria-label="Notifications"
            >
              <Bell className="h-5 w-5 text-foreground group-hover:text-primary transition-colors" />
              <span className="absolute -top-1 -right-1 h-5 w-5 bg-gradient-to-br from-red-500 to-red-600 text-white text-xs rounded-full flex items-center justify-center font-bold shadow-lg shadow-red-500/30 animate-pulse border-2 border-background">
                3
              </span>
            </button>

            {/* Favorites */}
            <button
              onClick={handleFavoritesClick}
              className="backdrop-blur-xl bg-card/50 border border-border transition-all duration-300 hover:-translate-y-1 hover:shadow-xl h-12 w-12 rounded-2xl flex items-center justify-center group"
              aria-label="Favorites"
            >
              <Heart className="h-5 w-5 text-foreground group-hover:text-red-500 transition-colors" />
            </button>

            {/* Profile */}
            <button
              onClick={handleProfileClick}
              className="backdrop-blur-xl bg-card/50 border border-border transition-all duration-300 hover:-translate-y-1 hover:shadow-xl h-12 w-12 rounded-2xl flex items-center justify-center group"
              aria-label="Profile"
            >
              <User className="h-5 w-5 text-foreground group-hover:text-primary transition-colors" />
            </button>
          </div>
        </div>

        {/* Search Bar */}
        <div className="relative">
          <SearchInput
            placeholder="Search for restaurants, dishes, or cuisines..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onFocus={handleSearchFocus}
            onBlur={handleSearchBlur}
            onFilterClick={handleFilterClick}
            className={cn(
              'backdrop-blur-xl bg-card/90 border-border text-foreground placeholder:text-muted-foreground shadow-2xl hover:shadow-2xl transition-all duration-300 h-14 text-base',
              isSearchFocused && 'shadow-2xl ring-2 ring-primary/20',
            )}
          />

          {/* Search Suggestions Overlay */}
          {isSearchFocused && (
            <div className="absolute top-full left-0 right-0 mt-2 backdrop-blur-sm bg-card/90 border border-border rounded-2xl shadow-xl animate-in slide-in-from-top-2 duration-300 z-50">
              <div className="p-4 space-y-3">
                <div className="flex items-center gap-3 p-3 rounded-xl hover:bg-accent/50 transition-colors cursor-pointer">
                  <Search className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="font-medium text-foreground">Pizza near me</p>
                    <p className="text-xs text-muted-foreground">Restaurants</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 rounded-xl hover:bg-accent/50 transition-colors cursor-pointer">
                  <Search className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="font-medium text-foreground">Sushi delivery</p>
                    <p className="text-xs text-muted-foreground">Restaurants</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 rounded-xl hover:bg-accent/50 transition-colors cursor-pointer">
                  <Search className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="font-medium text-foreground">Coffee shops</p>
                    <p className="text-xs text-muted-foreground">Cafes</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Quick Actions - Mobile Only */}
        <div className="flex gap-2 md:hidden">
          <Button
            variant="outline"
            size="sm"
            className="flex-1 rounded-full backdrop-blur-xl bg-card/50 border border-border transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"
          >
            <Navigation className="h-4 w-4 mr-2" />
            Navigation
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="flex-1 rounded-full backdrop-blur-xl bg-card/50 border border-border transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"
          >
            <Clock className="h-4 w-4 mr-2" />
            Now
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="flex-1 rounded-full backdrop-blur-xl bg-card/50 border border-border transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"
          >
            <Star className="h-4 w-4 mr-2" />
            Top Rated
          </Button>
        </div>

        {/* Status Bar - Desktop Only */}
        <div className="hidden md:flex items-center justify-between text-sm">
          <div className="flex items-center gap-4">
            <Badge className="bg-success/80 text-success-foreground">
              <div className="w-2 h-2 bg-success rounded-full mr-2 animate-pulse" />
              Live Location
            </Badge>
            <span className="text-muted-foreground">•</span>
            <span className="text-muted-foreground">Delivery in 15-30 min</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-muted-foreground">Free delivery on orders over $25</span>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
