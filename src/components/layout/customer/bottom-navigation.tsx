'use client'

import { Home, Search, Heart, User, MapPin } from 'lucide-react'
import { cn } from '@/utilities/ui'
import { useRouter, usePathname } from 'next/navigation'

interface BottomNavigationProps {
  activeTab?: string
  onTabChange?: (tab: string) => void
}

const navItems = [
  { id: 'home', label: 'Home', icon: Home, path: '/' },
  { id: 'discover', label: 'Discover', icon: Search, path: '/discover' },
  { id: 'nearby', label: 'Nearby', icon: MapPin, path: '/nearby' },
  { id: 'favorites', label: 'Favorites', icon: Heart, path: '/favorites' },
  { id: 'profile', label: 'Profile', icon: User, path: '/profile' },
]

export function BottomNavigation({ activeTab, onTabChange }: BottomNavigationProps) {
  const navigate = useRouter()
  let currentPath = usePathname()
  currentPath = currentPath === '/' ? '/home' : currentPath

  console.log('currentPath', currentPath)

  const handleTabClick = (item: (typeof navItems)[0]) => {
    navigate.push(item.path)
    onTabChange?.(item.id)
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 md:hidden">
      <nav className="glass-nav border-t border-white/20 px-4 py-2">
        <div className="flex items-center justify-around">
          {navItems.map((item) => {
            const Icon = item.icon
            const isActive = currentPath === item.path

            return (
              <button
                key={item.id}
                onClick={() => handleTabClick(item)}
                className={cn(
                  'flex flex-col items-center gap-1 px-3 py-2 rounded-xl transition-all duration-200',
                  isActive ? 'text-primary' : 'text-muted-foreground hover:text-foreground',
                )}
              >
                <div
                  className={cn(
                    'p-2 rounded-lg transition-all duration-200',
                    isActive ? 'bg-primary/10 scale-110' : 'hover:bg-secondary/50',
                  )}
                >
                  <Icon className="h-5 w-5" />
                </div>
                <span className="text-xs font-medium">{item.label}</span>
              </button>
            )
          })}
        </div>
      </nav>
    </div>
  )
}
