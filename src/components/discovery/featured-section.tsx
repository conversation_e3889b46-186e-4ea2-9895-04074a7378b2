import { BusinessCard } from '@/components/ui/business-card'
import { Badge } from '@/components/ui/badge'
import { Flame, TrendingUp, Star } from 'lucide-react'
import burgerImage from '@/assets/food-burger.jpg'
import sushiImage from '@/assets/food-sushi.jpg'
import coffeeImage from '@/assets/food-coffee.jpg'

// Mock data for featured businesses
const featuredBusinesses = [
  {
    id: '1',
    name: 'Artisan Burger Co.',
    category: 'American Cuisine',
    image: burgerImage.src,
    rating: 4.8,
    reviewCount: 234,
    distance: '0.5 km',
    isOpen: true,
    priceRange: '$$',
    isFavorite: false,
    badge: { type: 'trending', label: 'Trending' },
  },
  {
    id: '2',
    name: 'Sakura Sushi Bar',
    category: 'Japanese',
    image: sushiImage.src,
    rating: 4.9,
    reviewCount: 189,
    distance: '0.8 km',
    isOpen: true,
    priceRange: '$$$',
    isFavorite: true,
    badge: { type: 'popular', label: 'Most Popular' },
  },
  {
    id: '3',
    name: 'Roast & Bean Coffee',
    category: 'Cafe & Coffee',
    image: coffeeImage.src,
    rating: 4.7,
    reviewCount: 156,
    distance: '0.3 km',
    isOpen: true,
    priceRange: '$',
    isFavorite: false,
    badge: { type: 'new', label: 'New' },
  },
]

const badgeConfig = {
  trending: { icon: TrendingUp, color: 'bg-gradient-primary' },
  popular: { icon: Star, color: 'bg-gradient-amber' },
  new: { icon: Flame, color: 'bg-success' },
}

interface FeaturedSectionProps {
  onBusinessClick?: (id: string) => void
  onFavoriteToggle?: (id: string) => void
}

export function FeaturedSection({ onBusinessClick, onFavoriteToggle }: FeaturedSectionProps) {
  return (
    <section className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-display font-semibold text-foreground">Featured Near You</h2>
        <button className="text-primary font-medium text-sm hover:text-primary-light transition-colors">
          See All
        </button>
      </div>

      {/* Featured Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {featuredBusinesses.map((business, index) => {
          const BadgeIcon = badgeConfig[business.badge.type as keyof typeof badgeConfig]?.icon

          return (
            <div key={business.id} className="relative">
              {/* Feature Badge */}
              <div className="absolute -top-2 -right-2 z-10">
                <Badge
                  variant="secondary"
                  className={`
                    ${badgeConfig[business.badge.type as keyof typeof badgeConfig]?.color} 
                    text-white border-0 shadow-lg animate-scale-in px-3 py-1
                  `}
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  {BadgeIcon && <BadgeIcon className="h-3 w-3 mr-1" />}
                  {business.badge.label}
                </Badge>
              </div>

              <BusinessCard
                {...business}
                onFavoriteToggle={onFavoriteToggle}
                className="animate-fade-up"
              />
            </div>
          )
        })}
      </div>
    </section>
  )
}
