import { UtensilsCrossed, Coffee, Pizza, IceCream, Soup, Wine, Cake, Fish } from 'lucide-react'
import { cn } from '@/utilities/ui'

const categories = [
  { id: 'restaurants', name: 'Restaurants', icon: UtensilsCrossed, color: 'text-orange-500' },
  { id: 'coffee', name: 'Coffee', icon: Coffee, color: 'text-amber-600' },
  { id: 'pizza', name: 'Pizza', icon: Pizza, color: 'text-red-500' },
  { id: 'desserts', name: '<PERSON>ser<PERSON>', icon: IceCream, color: 'text-pink-500' },
  { id: 'soup', name: 'Comfort', icon: Soup, color: 'text-green-500' },
  { id: 'bars', name: 'Bars', icon: Wine, color: 'text-purple-500' },
  { id: 'bakery', name: 'Bakery', icon: Cake, color: 'text-yellow-500' },
  { id: 'seafood', name: 'Seafood', icon: Fish, color: 'text-blue-500' },
]

interface CategoriesSectionProps {
  onCategorySelect?: (categoryId: string) => void
  selectedCategory?: string
}

export function CategoriesSection({ onCategorySelect, selectedCategory }: CategoriesSectionProps) {
  return (
    <section className="space-y-4">
      <h2 className="text-xl font-display font-semibold text-foreground">Browse Categories</h2>

      <div className="grid grid-cols-4 sm:grid-cols-6 lg:grid-cols-8 gap-3">
        {categories.map((category, index) => {
          const Icon = category.icon
          const isSelected = selectedCategory === category.id

          return (
            <button
              key={category.id}
              onClick={() => onCategorySelect?.(category.id)}
              className={cn(
                'flex flex-col items-center gap-2 p-3 rounded-2xl transition-all duration-200',
                'glass hover-lift hover-glow animate-fade-up',
                isSelected ? 'ring-2 ring-primary shadow-glow' : 'hover:shadow-premium',
              )}
              style={{ animationDelay: `${index * 50}ms` }}
            >
              <div
                className={cn(
                  'p-3 rounded-xl transition-colors duration-200',
                  isSelected ? 'bg-gradient-primary text-white' : 'bg-secondary/50',
                )}
              >
                <Icon className={cn('h-5 w-5', isSelected ? 'text-white' : category.color)} />
              </div>
              <span
                className={cn(
                  'text-xs font-medium text-center leading-tight',
                  isSelected ? 'text-primary' : 'text-muted-foreground',
                )}
              >
                {category.name}
              </span>
            </button>
          )
        })}
      </div>
    </section>
  )
}
