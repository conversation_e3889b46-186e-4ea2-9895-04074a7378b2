import { SearchInput } from '@/components/ui/search-input'
import { MapP<PERSON>, User, Sparkles } from 'lucide-react'
import { Button } from '@/components/ui/button'
import Image from 'next/image'
import heroImage from '@/assets/hero-restaurant.jpg'

interface HeroSectionProps {
  onSearch?: (query: string) => void
  onFilterClick?: () => void
}

export function HeroSection({ onSearch, onFilterClick }: HeroSectionProps) {
  return (
    <section className="relative overflow-hidden min-h-[60vh]">
      {/* Background Hero Image */}
      <div className="absolute inset-0 ">
        <Image
          src={heroImage}
          alt="Restaurant ambiance showcasing local culinary experiences"
          fill
          priority
          className="object-cover transition-transform duration-700 hover:scale-105"
          sizes="100vw"
        />
        <div className="absolute inset-0 bg-gradient-to-b from-black/50 via-black/40 to-black/70" />
        {/* Premium gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-transparent to-amber-500/10" />
      </div>

      {/* Animated background elements */}
      <div className="absolute inset-0 -z-5">
        <div className="absolute top-20 left-10 w-2 h-2 bg-primary/30 rounded-full animate-pulse" />
        <div className="absolute top-32 right-16 w-3 h-3 bg-amber-400/20 rounded-full animate-pulse delay-300" />
        <div className="absolute bottom-40 left-20 w-1.5 h-1.5 bg-primary/40 rounded-full animate-pulse delay-700" />
      </div>

      {/* Content */}
      <div className="relative z-10 px-4 py-12 space-y-8">
        {/* Welcome Text */}
        <div className="text-center space-y-4 py-12 animate-fade-up">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Sparkles className="h-6 w-6 text-amber-400 animate-pulse" />
            <span className="text-amber-400 text-sm font-semibold tracking-wide">FOODIE FIND</span>
            <Sparkles className="h-6 w-6 text-amber-400 animate-pulse" />
          </div>

          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-display font-bold text-white drop-shadow-2xl leading-tight">
            Discover Amazing
            <span className="block bg-gradient-to-r from-white via-amber-100 to-white bg-clip-text text-transparent drop-shadow-2xl">
              Local Flavors
            </span>
          </h1>

          <p className="text-white/90 text-base sm:text-lg max-w-lg mx-auto drop-shadow-lg font-medium leading-relaxed">
            Find the best restaurants, cafes, and local gems near you with authentic reviews
          </p>
        </div>

        {/* Search */}
        <div className="max-w-lg mx-auto animate-fade-up">
          <SearchInput
            placeholder="Search for restaurants, dishes, or cuisines..."
            onFilterClick={onFilterClick}
            onChange={(e) => onSearch?.(e.target.value)}
            className="backdrop-blur-xl bg-white/98 border-white/50 text-gray-800 placeholder:text-gray-500 shadow-2xl hover:shadow-glow transition-all duration-300 h-14 text-base"
          />
        </div>
      </div>
    </section>
  )
}
