import * as React from 'react'
import { Search, Filter } from 'lucide-react'
import { cn } from '@/utilities/ui'

export interface SearchInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  onFilterClick?: () => void
  showFilter?: boolean
}

const SearchInput = React.forwardRef<HTMLInputElement, SearchInputProps>(
  ({ className, onFilterClick, showFilter = true, ...props }, ref) => {
    return (
      <div className="relative flex items-center">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <input
            className={cn(
              'flex h-12 w-full rounded-2xl glass px-11 py-3 text-sm',
              'placeholder:text-muted-foreground',
              'focus:outline-none focus:ring-2 focus:ring-primary/20',
              'disabled:cursor-not-allowed disabled:opacity-50',
              className,
            )}
            ref={ref}
            {...props}
          />
        </div>
        {showFilter && (
          <button
            onClick={onFilterClick}
            className="ml-3 flex h-12 w-12 items-center justify-center rounded-2xl glass hover-lift hover-glow"
          >
            <Filter className="h-4 w-4 text-foreground" />
          </button>
        )}
      </div>
    )
  },
)

SearchInput.displayName = 'SearchInput'

export { SearchInput }
