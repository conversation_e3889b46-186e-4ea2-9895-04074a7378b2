import { Star } from 'lucide-react'
import { cn } from '@/utilities/ui'

interface RatingProps {
  rating: number
  maxRating?: number
  size?: 'sm' | 'md' | 'lg'
  showNumber?: boolean
  className?: string
}

export function Rating({
  rating,
  maxRating = 5,
  size = 'md',
  showNumber = true,
  className,
}: RatingProps) {
  const sizeClasses = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
  }

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  }

  return (
    <div className={cn('flex items-center gap-1', className)}>
      <div className="flex items-center">
        {[...Array(maxRating)].map((_, index) => {
          const isFilled = index < Math.floor(rating)
          const isHalfFilled = index < rating && index >= Math.floor(rating)

          return (
            <Star
              key={index}
              className={cn(
                sizeClasses[size],
                isFilled || isHalfFilled ? 'fill-amber-400 text-amber-400' : 'text-gray-300',
              )}
            />
          )
        })}
      </div>
      {showNumber && (
        <span className={cn('font-medium text-foreground', textSizeClasses[size])}>
          {rating.toFixed(1)}
        </span>
      )}
    </div>
  )
}
