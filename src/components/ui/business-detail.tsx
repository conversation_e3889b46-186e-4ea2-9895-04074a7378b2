'use client'

import { useState } from 'react'
import {
  MapPin,
  Clock,
  Heart,
  Phone,
  Navigation,
  Share2,
  Star,
  ChevronDown,
  ChevronUp,
  Utensils,
  DollarSign,
  Users,
  Award,
  Shield,
  Truck,
  CreditCard,
  Wifi,
  Car,
  Baby,
  Dog,
} from 'lucide-react'
import { Rating } from './rating'
import { Badge } from './badge'
import { Button } from './button'
import { cn } from '@/utilities/ui'

export interface BusinessDetailType {
  id: string
  name: string
  category: string
  image: string
  rating: number
  reviewCount: number
  distance: string
  isOpen: boolean
  priceRange: string
  isFavorite?: boolean
  description?: string
  address?: string
  phone?: string
  hours?: {
    [key: string]: string
  }
  amenities?: string[]
  features?: string[]
  menu?: {
    category: string
    items: {
      name: string
      description: string
      price: string
      image?: string
      isPopular?: boolean
    }[]
  }[]
  reviews?: {
    id: string
    user: string
    rating: number
    comment: string
    date: string
    avatar?: string
  }[]
}

export interface BusinessDetailProps {
  business: BusinessDetailType
  onFavoriteToggle?: (id: string) => void
  onBack?: () => void
  onCall?: (phone: string) => void
  onNavigate?: (address: string) => void
  onShare?: (business: any) => void
}

export function BusinessDetail({
  business,
  onFavoriteToggle,
  onBack,
  onCall,
  onNavigate,
  onShare,
}: BusinessDetailProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'menu' | 'reviews'>('overview')
  const [expandedMenuCategory, setExpandedMenuCategory] = useState<string | null>(null)

  const amenityIcons = {
    wifi: Wifi,
    parking: Car,
    delivery: Truck,
    'credit-cards': CreditCard,
    'family-friendly': Baby,
    'pet-friendly': Dog,
  }

  const getAmenityIcon = (amenity: string) => {
    const key = amenity.toLowerCase().replace(/\s+/g, '-')
    return amenityIcons[key as keyof typeof amenityIcons] || Utensils
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <div className="relative h-80 overflow-hidden">
        <img src={business.image} alt={business.name} className="w-full h-full object-cover" />
        <div className="absolute inset-0 bg-gradient-to-b from-black/50 via-black/30 to-black/70" />

        {/* Header Actions */}
        <div className="absolute top-4 left-4 right-4 flex items-center justify-between z-10">
          <button
            onClick={onBack}
            className="glass hover-lift hover-glow h-10 w-10 rounded-2xl flex items-center justify-center"
          >
            <ChevronDown className="h-5 w-5 text-white rotate-90" />
          </button>

          <div className="flex items-center gap-2">
            <button
              onClick={() => onShare?.(business)}
              className="glass hover-lift hover-glow h-10 w-10 rounded-2xl flex items-center justify-center"
            >
              <Share2 className="h-5 w-5 text-white" />
            </button>
            <button
              onClick={() => onFavoriteToggle?.(business.id)}
              className="glass hover-lift hover-glow h-10 w-10 rounded-2xl flex items-center justify-center"
            >
              <Heart
                className={cn(
                  'h-5 w-5 transition-colors',
                  business.isFavorite ? 'fill-red-500 text-red-500' : 'text-white',
                )}
              />
            </button>
          </div>
        </div>

        {/* Status Badge */}
        <div className="absolute top-20 left-4">
          <Badge
            className={cn(
              'px-3 py-1 text-sm font-bold backdrop-blur-sm',
              business.isOpen
                ? 'bg-success/80 text-success-foreground'
                : 'bg-destructive/80 text-destructive-foreground',
            )}
          >
            {business.isOpen ? 'Open Now' : 'Closed'}
          </Badge>
        </div>

        {/* Business Info Overlay */}
        <div className="absolute bottom-0 left-0 right-0 p-6">
          <h1 className="text-3xl font-display font-bold text-white drop-shadow-2xl mb-2">
            {business.name}
          </h1>
          <div className="flex items-center gap-4 text-white/90">
            <div className="flex items-center gap-1">
              <Star className="h-4 w-4 fill-amber-400 text-amber-400" />
              <span className="font-semibold">{business.rating}</span>
              <span className="text-sm">({business.reviewCount} reviews)</span>
            </div>
            <div className="flex items-center gap-1">
              <MapPin className="h-4 w-4" />
              <span>{business.distance}</span>
            </div>
            <div className="flex items-center gap-1">
              <DollarSign className="h-4 w-4" />
              <span>{business.priceRange}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="px-4 py-4 space-y-3">
        <div className="flex gap-3">
          <Button
            onClick={() => onCall?.(business.phone || '')}
            className="flex-1 bg-primary text-primary-foreground rounded-2xl h-12 font-semibold"
          >
            <Phone className="h-4 w-4 mr-2" />
            Call
          </Button>
          <Button
            onClick={() => onNavigate?.(business.address || '')}
            className="flex-1 bg-secondary text-secondary-foreground rounded-2xl h-12 font-semibold"
          >
            <Navigation className="h-4 w-4 mr-2" />
            Navigate
          </Button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="px-4">
        <div className="flex glass rounded-2xl p-1">
          {[
            { id: 'overview', label: 'Overview' },
            { id: 'menu', label: 'Menu' },
            { id: 'reviews', label: 'Reviews' },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={cn(
                'flex-1 py-3 px-4 rounded-xl font-semibold transition-all duration-200',
                activeTab === tab.id
                  ? 'bg-primary text-primary-foreground shadow-lg'
                  : 'text-muted-foreground hover:text-foreground',
              )}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div className="px-4 py-6 space-y-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Description */}
            {business.description && (
              <div className="glass-card p-6">
                <h3 className="text-lg font-semibold text-foreground mb-3">About</h3>
                <p className="text-muted-foreground leading-relaxed">{business.description}</p>
              </div>
            )}

            {/* Contact Info */}
            <div className="glass-card p-6">
              <h3 className="text-lg font-semibold text-foreground mb-4">Contact Information</h3>
              <div className="space-y-3">
                {business.address && (
                  <div className="flex items-start gap-3">
                    <MapPin className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-foreground">Address</p>
                      <p className="text-muted-foreground text-sm">{business.address}</p>
                    </div>
                  </div>
                )}
                {business.phone && (
                  <div className="flex items-center gap-3">
                    <Phone className="h-5 w-5 text-primary flex-shrink-0" />
                    <div>
                      <p className="font-medium text-foreground">Phone</p>
                      <p className="text-muted-foreground text-sm">{business.phone}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Hours */}
            {business.hours && (
              <div className="glass-card p-6">
                <h3 className="text-lg font-semibold text-foreground mb-4">Hours</h3>
                <div className="space-y-2">
                  {Object.entries(business.hours).map(([day, hours]) => (
                    <div key={day} className="flex justify-between">
                      <span className="font-medium text-foreground capitalize">{day}</span>
                      <span className="text-muted-foreground">{hours}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Amenities */}
            {business.amenities && business.amenities.length > 0 && (
              <div className="glass-card p-6">
                <h3 className="text-lg font-semibold text-foreground mb-4">Amenities</h3>
                <div className="grid grid-cols-2 gap-3">
                  {business.amenities.map((amenity, index) => {
                    const Icon = getAmenityIcon(amenity)
                    return (
                      <div key={index} className="flex items-center gap-2">
                        <Icon className="h-4 w-4 text-primary" />
                        <span className="text-sm text-foreground">{amenity}</span>
                      </div>
                    )
                  })}
                </div>
              </div>
            )}

            {/* Features */}
            {business.features && business.features.length > 0 && (
              <div className="glass-card p-6">
                <h3 className="text-lg font-semibold text-foreground mb-4">Features</h3>
                <div className="flex flex-wrap gap-2">
                  {business.features.map((feature, index) => (
                    <Badge key={index} variant="secondary" className="bg-primary/10 text-primary">
                      {feature}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'menu' && business.menu && (
          <div className="space-y-4">
            {business.menu.map((category, categoryIndex) => (
              <div key={categoryIndex} className="glass-card">
                <button
                  onClick={() =>
                    setExpandedMenuCategory(
                      expandedMenuCategory === category.category ? null : category.category,
                    )
                  }
                  className="w-full p-6 flex items-center justify-between hover:bg-white/5 transition-colors"
                >
                  <h3 className="text-lg font-semibold text-foreground">{category.category}</h3>
                  {expandedMenuCategory === category.category ? (
                    <ChevronUp className="h-5 w-5 text-muted-foreground" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-muted-foreground" />
                  )}
                </button>

                {expandedMenuCategory === category.category && (
                  <div className="px-6 pb-6 space-y-4">
                    {category.items.map((item, itemIndex) => (
                      <div
                        key={itemIndex}
                        className="flex gap-4 py-3 border-b border-white/10 last:border-0"
                      >
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-semibold text-foreground">{item.name}</h4>
                            {item.isPopular && (
                              <Badge className="bg-gradient-primary text-white text-xs">
                                Popular
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">{item.description}</p>
                          <p className="text-lg font-bold text-primary">{item.price}</p>
                        </div>
                        {item.image && (
                          <img
                            src={item.image}
                            alt={item.name}
                            className="w-20 h-20 rounded-xl object-cover"
                          />
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {activeTab === 'reviews' && business.reviews && (
          <div className="space-y-4">
            {/* Overall Rating Summary */}
            <div className="glass-card p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-foreground">Customer Reviews</h3>
                <div className="text-right">
                  <div className="text-3xl font-bold text-foreground">{business.rating}</div>
                  <Rating rating={business.rating} size="lg" />
                  <p className="text-sm text-muted-foreground mt-1">
                    {business.reviewCount} reviews
                  </p>
                </div>
              </div>
            </div>

            {/* Individual Reviews */}
            <div className="space-y-4">
              {business.reviews.map((review) => (
                <div key={review.id} className="glass-card p-6">
                  <div className="flex items-start gap-4">
                    <div className="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center text-white font-semibold">
                      {review.user.charAt(0).toUpperCase()}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-foreground">{review.user}</h4>
                        <span className="text-sm text-muted-foreground">{review.date}</span>
                      </div>
                      <Rating rating={review.rating} size="sm" />
                      <p className="text-muted-foreground mt-2 leading-relaxed">{review.comment}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
