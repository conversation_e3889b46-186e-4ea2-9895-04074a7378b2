import { <PERSON><PERSON><PERSON>, <PERSON>, Heart } from 'lucide-react'
import { Rating } from './rating'
import { cn } from '@/utilities/ui'
import Link from 'next/link'

interface BusinessCardProps {
  id: string
  name: string
  category: string
  image: string
  rating: number
  reviewCount: number
  distance: string
  isOpen: boolean
  priceRange: string
  isFavorite?: boolean
  className?: string
  onClick?: () => void
  onFavoriteToggle?: (id: string) => void
}

export function BusinessCard({
  id,
  name,
  category,
  image,
  rating,
  reviewCount,
  distance,
  isOpen,
  priceRange,
  isFavorite = false,
  className,
  onClick,
  onFavoriteToggle,
}: BusinessCardProps) {
  return (
    <Link href={`/business/${id}`}>
      <div
        className={cn(
          'glass-card hover-lift hover-glow cursor-pointer group animate-fade-up',
          className,
        )}
        onClick={onClick}
      >
        {/* Image Container */}
        <div className="relative overflow-hidden rounded-xl mb-4">
          <img
            src={image}
            alt={name}
            className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
          />
          <div className="absolute inset-0 bg-gradient-overlay opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

          {/* Favorite Button */}
          <button
            onClick={(e) => {
              e.stopPropagation()
              onFavoriteToggle?.(id)
            }}
            className="absolute top-3 right-3 p-2 rounded-full glass hover-glow transition-all duration-200"
          >
            <Heart
              className={cn(
                'h-4 w-4 transition-colors',
                isFavorite ? 'fill-red-500 text-red-500' : 'text-white',
              )}
            />
          </button>

          {/* Status Badge */}
          <div className="absolute top-3 left-3">
            <span
              className={cn(
                'px-2 py-1 rounded-full text-xs font-medium backdrop-blur-sm',
                isOpen
                  ? 'bg-success/80 text-success-foreground'
                  : 'bg-destructive/80 text-destructive-foreground',
              )}
            >
              {isOpen ? 'Open' : 'Closed'}
            </span>
          </div>
        </div>

        {/* Content */}
        <div className="space-y-3">
          <div>
            <h3 className="font-semibold text-lg text-foreground line-clamp-1">{name}</h3>
            <p className="text-muted-foreground text-sm">
              {category} • {priceRange}
            </p>
          </div>

          {/* Rating & Reviews */}
          <div className="flex items-center justify-between">
            <Rating rating={rating} showNumber />
            <span className="text-xs text-muted-foreground">({reviewCount} reviews)</span>
          </div>

          {/* Location & Time */}
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <MapPin className="h-3 w-3" />
              <span>{distance}</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>15-30 min</span>
            </div>
          </div>
        </div>
      </div>
    </Link>
  )
}
