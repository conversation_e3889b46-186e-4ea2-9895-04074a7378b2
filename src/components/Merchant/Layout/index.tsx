'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from './Header'
import { Sidebar } from './Sidebar'
import { Footer } from './Footer'

interface LayoutProps {
  children: React.ReactNode
}

// Dynamic background with floating elements
const DynamicBackground: React.FC = () => {
  const [particles, setParticles] = useState<
    Array<{ id: number; x: number; y: number; delay: number; size: number }>
  >([])

  useEffect(() => {
    const newParticles = Array.from({ length: 25 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      delay: Math.random() * 6,
      size: Math.random() * 3 + 1,
    }))
    setParticles(newParticles)
  }, [])

  return (
    <div className="fixed inset-0 pointer-events-none overflow-hidden">
      {/* Restaurant-inspired gradient background */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-amber-50/80 to-orange-50/60" />

      {/* Subtle texture overlay */}
      <div
        className="absolute inset-0 opacity-30"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f59e0b' fill-opacity='0.02'%3E%3Cpath d='M0 0h40v40H0V0zm40 40h40v40H40V40z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}
      />

      {/* Floating ambient particles */}
      {particles.map((particle) => (
        <div
          key={particle.id}
          className="absolute rounded-full bg-gradient-to-br from-amber-300/20 to-orange-400/20 animate-pulse"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            animationDelay: `${particle.delay}s`,
            animationDuration: '4s',
          }}
        />
      ))}

      {/* Large glowing orbs for depth */}
      <div className="absolute top-1/4 left-1/6 w-64 h-64 bg-gradient-to-br from-amber-400/10 to-orange-600/10 rounded-full blur-3xl animate-pulse" />
      <div
        className="absolute bottom-1/4 right-1/6 w-48 h-48 bg-gradient-to-br from-orange-500/8 to-red-600/8 rounded-full blur-2xl animate-pulse"
        style={{ animationDelay: '2s' }}
      />
      <div
        className="absolute top-1/2 left-1/2 w-32 h-32 bg-gradient-to-br from-amber-300/12 to-yellow-500/12 rounded-full blur-xl animate-pulse"
        style={{ animationDelay: '4s' }}
      />

      {/* Warm glow effects */}
      <div className="absolute inset-0 bg-gradient-radial from-transparent via-amber-500/[0.01] to-orange-600/[0.02]" />
    </div>
  )
}

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  return (
    <div className="min-h-screen relative">
      {/* Dynamic restaurant-inspired background */}
      <DynamicBackground />

      {/* Sidebar */}
      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

      {/* Main Content */}
      <div className="relative flex flex-col min-h-screen lg:ml-64 z-10">
        {/* Header */}
        <Header onMenuClick={() => setSidebarOpen(true)} />

        {/* Page Content */}
        <main className="flex-1 p-4 lg:p-6">
          <div className="relative">
            {/* Content background with subtle glassmorphism */}
            <div className="absolute inset-0 bg-white/40 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50" />
            <div className="relative z-10 p-6 lg:p-8">{children}</div>
          </div>
        </main>

        {/* Footer */}
        <Footer />
      </div>

      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-gradient-to-br from-black/60 via-amber-900/40 to-orange-900/30 backdrop-blur-sm z-40 lg:hidden transition-all duration-300"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  )
}
