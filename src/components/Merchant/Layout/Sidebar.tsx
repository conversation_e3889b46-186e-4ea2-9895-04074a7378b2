'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  LayoutDashboard,
  ShoppingBag,
  Users,
  CreditCard,
  BarChart3,
  Settings,
  HeadphonesIcon,
  ChefHat,
  X,
  ChevronRight,
  Store,
  Shield,
  Star,
  Sparkles,
} from 'lucide-react'

interface SidebarProps {
  isOpen: boolean
  onClose: () => void
}

// Floating particles component for background ambiance
const FloatingParticles: React.FC = () => {
  const [particles, setParticles] = useState<
    Array<{ id: number; x: number; y: number; delay: number }>
  >([])

  useEffect(() => {
    const newParticles = Array.from({ length: 12 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      delay: Math.random() * 4,
    }))
    setParticles(newParticles)
  }, [])

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {particles.map((particle) => (
        <div
          key={particle.id}
          className="absolute w-1 h-1 bg-amber-300/30 rounded-full animate-pulse"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            animationDelay: `${particle.delay}s`,
            animationDuration: '3s',
          }}
        />
      ))}

      {/* Glowing gradient orbs */}
      <div className="absolute top-1/4 left-1/3 w-32 h-32 bg-gradient-to-br from-amber-400/20 to-orange-600/20 rounded-full blur-xl animate-pulse" />
      <div
        className="absolute bottom-1/3 right-1/4 w-24 h-24 bg-gradient-to-br from-orange-500/15 to-red-600/15 rounded-full blur-2xl animate-pulse"
        style={{ animationDelay: '1.5s' }}
      />
      <div
        className="absolute top-2/3 left-1/4 w-20 h-20 bg-gradient-to-br from-amber-300/25 to-yellow-500/25 rounded-full blur-xl animate-pulse"
        style={{ animationDelay: '3s' }}
      />
    </div>
  )
}

export const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose }) => {
  const pathname = usePathname()
  const [expandedMenus, setExpandedMenus] = useState<string[]>([])

  const toggleMenu = (menuId: string) => {
    setExpandedMenus((prev) =>
      prev.includes(menuId) ? prev.filter((id) => id !== menuId) : [...prev, menuId],
    )
  }

  const menuItems = [
    {
      id: 'dashboard',
      title: 'Dashboard',
      icon: LayoutDashboard,
      href: '/merchant/dashboard',
      badge: null,
    },
    {
      id: 'orders',
      title: 'Orders',
      icon: ShoppingBag,
      href: '/merchant/orders',
      badge: '12',
    },
    {
      id: 'customers',
      title: 'Customers',
      icon: Users,
      href: '/merchant/customers',
      badge: null,
    },
    {
      id: 'accounts',
      title: 'Accounts',
      icon: CreditCard,
      href: '/merchant/accounts',
      badge: null,
    },
    {
      id: 'reports',
      title: 'Reports & Analytics',
      icon: BarChart3,
      href: '/merchant/reports',
      badge: null,
      subItems: [
        { title: 'Sales Report', href: '/merchant/reports/sales' },
        { title: 'Customer Analytics', href: '/merchant/reports/customer-analytics' },
        { title: 'Menu Performance', href: '/merchant/reports/menu-performance' },
        { title: 'Commission Report', href: '/merchant/reports/commission' },
      ],
    },
    {
      id: 'support',
      title: 'Support & Help',
      icon: HeadphonesIcon,
      href: '/merchant/support',
      badge: null,
    },
    {
      id: 'settings',
      title: 'Settings',
      icon: Settings,
      href: '/merchant/settings',
      badge: null,
    },
  ]

  const isActiveLink = (href: string) => {
    return pathname === href || pathname.startsWith(href + '/')
  }

  return (
    <>
      {/* Sidebar */}
      <div
        className={`fixed top-0 left-0 h-full w-64 transform transition-all duration-500 ease-in-out z-50 lg:translate-x-0 ${isOpen ? 'translate-x-0' : '-translate-x-full'}`}
      >
        {/* Glassmorphic background with restaurant ambiance */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/90 via-amber-900/95 to-orange-900/90 backdrop-blur-xl">
          {/* Restaurant-inspired texture overlay */}
          <div
            className="absolute inset-0 opacity-50"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }}
          />

          {/* Floating particles */}
          <FloatingParticles />

          {/* Gradient border glow */}
          <div className="absolute inset-0 bg-gradient-to-r from-amber-500/20 via-transparent to-orange-500/20 blur-sm" />
        </div>

        {/* Content overlay */}
        <div className="relative z-10 h-full flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-amber-500/20 bg-white/5 backdrop-blur-sm">
            <div className="flex items-center space-x-3">
              <div className="relative w-12 h-12 bg-gradient-to-br from-amber-400 to-orange-600 rounded-xl flex items-center justify-center shadow-lg shadow-amber-500/25 group-hover:shadow-amber-500/40 transition-all duration-300">
                <ChefHat className="h-7 w-7 text-white drop-shadow-lg" />
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full flex items-center justify-center">
                  <Star className="h-2.5 w-2.5 text-white animate-pulse" />
                </div>
              </div>
              <div>
                <h1 className="text-2xl font-black bg-gradient-to-r from-amber-300 via-orange-400 to-red-500 bg-clip-text text-transparent drop-shadow-sm">
                  YoFood
                </h1>
                <p className="text-xs text-amber-200/80 font-medium tracking-wide">
                  Merchant Portal
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="lg:hidden p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-all duration-200 backdrop-blur-sm border border-white/10"
            >
              <X className="h-5 w-5 text-amber-200" />
            </button>
          </div>

          {/* Restaurant Info */}
          <div className="p-4 border-b border-amber-500/20 bg-gradient-to-r from-amber-500/10 to-orange-500/10 backdrop-blur-sm">
            <div className="flex items-center space-x-3">
              <div className="relative w-14 h-14 bg-gradient-to-br from-amber-400 via-orange-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25 group">
                <Store className="h-7 w-7 text-white drop-shadow-lg" />
                <div className="absolute -top-0.5 -right-0.5 w-3 h-3 bg-gradient-to-br from-emerald-400 to-green-500 rounded-full animate-pulse">
                  <div className="absolute inset-0.5 bg-white rounded-full" />
                </div>
                {/* Trust badge */}
                <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                  <Shield className="h-2.5 w-2.5 text-white" />
                </div>
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="font-bold text-white truncate text-lg drop-shadow-sm">
                  Bella&apos;s Bistro
                </h3>
                <p className="text-sm text-amber-200/90 truncate font-medium">Downtown Location</p>
                <div className="flex items-center mt-1.5">
                  <div className="w-2.5 h-2.5 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full mr-2 animate-pulse shadow-lg shadow-green-500/50" />
                  <span className="text-xs text-emerald-300 font-semibold tracking-wide">
                    OPEN NOW
                  </span>
                  <Sparkles className="h-3 w-3 text-amber-300 ml-2 animate-pulse" />
                </div>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 overflow-y-auto p-4">
            <div className="space-y-2">
              {menuItems.map((item) => (
                <div key={item.id}>
                  <div
                    className={`group flex items-center justify-between w-full px-4 py-3.5 text-sm font-semibold rounded-xl transition-all duration-300 transform hover:scale-[1.02] ${
                      isActiveLink(item.href)
                        ? 'bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 text-white shadow-xl shadow-orange-500/30 border border-orange-400/50'
                        : 'text-amber-100 hover:bg-white/10 hover:text-white hover:shadow-lg hover:shadow-white/10 backdrop-blur-sm border border-white/5'
                    }`}
                  >
                    <Link href={item.href} className="flex items-center flex-1 min-w-0">
                      <div
                        className={`relative mr-4 ${isActiveLink(item.href) ? 'text-white' : 'text-amber-300 group-hover:text-amber-200'}`}
                      >
                        <item.icon className="h-5 w-5 drop-shadow-sm" />
                        {isActiveLink(item.href) && (
                          <div className="absolute -inset-1 bg-white/20 rounded-lg blur-sm animate-pulse" />
                        )}
                      </div>
                      <span className="truncate tracking-wide">{item.title}</span>
                    </Link>

                    <div className="flex items-center space-x-2">
                      {item.badge && (
                        <span
                          className={`px-2.5 py-1 text-xs font-bold rounded-full transition-all duration-300 ${
                            isActiveLink(item.href)
                              ? 'bg-white/25 text-white shadow-lg backdrop-blur-sm'
                              : 'bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg shadow-red-500/30 animate-pulse'
                          }`}
                        >
                          {item.badge}
                        </span>
                      )}
                      {item.subItems && (
                        <button
                          onClick={() => toggleMenu(item.id)}
                          className={`p-1.5 rounded-lg transition-all duration-300 hover:bg-white/10 ${
                            expandedMenus.includes(item.id) ? 'rotate-90' : ''
                          }`}
                        >
                          <ChevronRight
                            className={`h-4 w-4 transition-colors ${isActiveLink(item.href) ? 'text-white' : 'text-amber-300 group-hover:text-amber-200'}`}
                          />
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Sub items */}
                  {item.subItems && expandedMenus.includes(item.id) && (
                    <div className="ml-8 mt-2 space-y-1.5 animate-in slide-in-from-top-2 duration-300">
                      {item.subItems.map((subItem) => (
                        <Link
                          key={subItem.href}
                          href={subItem.href}
                          className={`block px-4 py-2.5 text-sm rounded-lg transition-all duration-200 hover:scale-[1.01] ${
                            isActiveLink(subItem.href)
                              ? 'bg-gradient-to-r from-amber-300/20 to-orange-400/20 text-amber-100 font-semibold backdrop-blur-sm border border-amber-400/30 shadow-lg'
                              : 'text-amber-200/80 hover:bg-white/5 hover:text-amber-100 backdrop-blur-sm border border-transparent hover:border-white/10'
                          }`}
                        >
                          <div className="flex items-center">
                            <div className="w-1.5 h-1.5 bg-amber-400 rounded-full mr-3 animate-pulse" />
                            {subItem.title}
                          </div>
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-amber-500/20">
            <div className="bg-gradient-to-r from-emerald-500/10 via-teal-500/10 to-green-500/10 rounded-xl p-4 backdrop-blur-sm border border-emerald-400/20 shadow-lg">
              <div className="flex items-center space-x-3">
                <div className="relative w-10 h-10 bg-gradient-to-br from-emerald-400 via-teal-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg shadow-emerald-500/25">
                  <BarChart3 className="h-5 w-5 text-white drop-shadow-lg" />
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full animate-pulse">
                    <Star className="h-2 w-2 text-white absolute inset-0.5" />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-bold text-white drop-shadow-sm">Free Plan</p>
                  <p className="text-xs text-emerald-200/90">Upgrade for premium features</p>
                </div>
              </div>
              <button className="w-full mt-4 px-4 py-3 bg-gradient-to-r from-emerald-400 via-teal-500 to-green-600 text-white text-sm font-bold rounded-xl hover:from-emerald-500 hover:via-teal-600 hover:to-green-700 transition-all duration-300 transform hover:scale-[1.02] shadow-lg shadow-emerald-500/30 hover:shadow-emerald-500/40 backdrop-blur-sm border border-emerald-400/50">
                <div className="flex items-center justify-center space-x-2">
                  <Sparkles className="h-4 w-4 animate-pulse" />
                  <span>Upgrade Now</span>
                  <Sparkles className="h-4 w-4 animate-pulse" />
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
