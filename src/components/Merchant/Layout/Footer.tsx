'use client'

import React from 'react'
import { Heart, ExternalLink, Shield, Star, Sparkles } from 'lucide-react'

export const Footer: React.FC = () => {
  return (
    <footer className="relative bg-gradient-to-r from-white/95 via-amber-50/95 to-orange-50/95 backdrop-blur-xl border-t border-amber-200/50 px-4 lg:px-6 py-6 shadow-lg">
      {/* Subtle background pattern */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-amber-500/5 to-orange-500/5" />

      <div className="relative flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0">
        <div className="flex items-center space-x-6 text-sm">
          <div className="flex items-center space-x-2 group">
            <span className="flex items-center text-gray-700 font-medium">
              Made with
              <div className="relative mx-2">
                <Heart className="h-4 w-4 text-red-500 animate-pulse drop-shadow-sm" />
                <div className="absolute inset-0 bg-red-400/30 blur-sm animate-pulse" />
              </div>
              for restaurants
            </span>
            <Sparkles className="h-3 w-3 text-amber-500 animate-pulse" />
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-gray-700 font-medium">© 2024 YoFood</span>
            <div className="flex items-center space-x-1">
              <Shield className="h-3 w-3 text-blue-600" />
              <Star className="h-3 w-3 text-amber-500 animate-pulse" />
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-6 text-sm">
          <a
            href="#"
            className="text-amber-700 hover:text-orange-700 transition-all duration-300 flex items-center group font-medium hover:scale-105"
          >
            <div className="flex items-center space-x-2 px-3 py-1.5 rounded-lg bg-white/50 hover:bg-white/80 backdrop-blur-sm border border-amber-200/30 hover:border-amber-300/50 shadow-lg hover:shadow-xl transition-all duration-300">
              <span>Help & Support</span>
              <ExternalLink className="h-3 w-3 group-hover:rotate-12 transition-transform duration-300" />
            </div>
          </a>
          <a
            href="#"
            className="text-amber-700 hover:text-orange-700 transition-all duration-300 font-medium px-3 py-1.5 rounded-lg hover:bg-white/50 backdrop-blur-sm border border-transparent hover:border-amber-200/30 hover:scale-105"
          >
            Privacy
          </a>
          <a
            href="#"
            className="text-amber-700 hover:text-orange-700 transition-all duration-300 font-medium px-3 py-1.5 rounded-lg hover:bg-white/50 backdrop-blur-sm border border-transparent hover:border-amber-200/30 hover:scale-105"
          >
            Terms
          </a>
        </div>
      </div>

      {/* Trust indicators */}
      <div className="relative mt-4 pt-3 border-t border-amber-200/30">
        <div className="flex justify-center items-center space-x-6 text-xs text-amber-600/80">
          <div className="flex items-center space-x-1">
            <Shield className="h-3 w-3 text-blue-600" />
            <span className="font-medium">SSL Secured</span>
          </div>
          <div className="flex items-center space-x-1">
            <Star className="h-3 w-3 text-amber-500" />
            <span className="font-medium">Premium Platform</span>
          </div>
          <div className="flex items-center space-x-1">
            <Heart className="h-3 w-3 text-red-500" />
            <span className="font-medium">Trusted by 10k+ Restaurants</span>
          </div>
        </div>
      </div>
    </footer>
  )
}
