'use client'

import React, { useState } from 'react'
import {
  Bell,
  Search,
  Menu,
  User,
  Settings,
  LogOut,
  ChevronDown,
  MessageSquare,
  HelpCircle,
  Shield,
  Star,
  Crown,
} from 'lucide-react'

interface HeaderProps {
  onMenuClick: () => void
}

export const Header: React.FC<HeaderProps> = ({ onMenuClick }) => {
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [showNotifications, setShowNotifications] = useState(false)

  const notifications = [
    {
      id: 1,
      title: 'New order received',
      message: 'Order #1234 from <PERSON>',
      time: '2 min ago',
      type: 'order',
    },
    {
      id: 2,
      title: 'Low stock alert',
      message: 'Chicken Wings running low',
      time: '15 min ago',
      type: 'warning',
    },
    {
      id: 3,
      title: 'Payment received',
      message: '$125.50 payment processed',
      time: '1 hour ago',
      type: 'success',
    },
  ]

  return (
    <header className="bg-gradient-to-r from-white/95 via-amber-50/95 to-orange-50/95 backdrop-blur-xl shadow-xl border-b border-amber-200/50 sticky top-0 z-30">
      {/* Subtle gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-amber-500/5 to-orange-500/5" />

      <div className="relative flex items-center justify-between h-16 px-4 lg:px-6">
        {/* Left side */}
        <div className="flex items-center space-x-4">
          {/* Mobile menu button */}
          <button
            onClick={onMenuClick}
            className="lg:hidden p-2 rounded-xl bg-white/50 hover:bg-white/80 transition-all duration-300 backdrop-blur-sm border border-amber-200/50 shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            <Menu className="h-6 w-6 text-amber-700" />
          </button>

          {/* Search bar */}
          <div className="hidden md:flex items-center">
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-orange-500/20 rounded-xl blur-sm group-focus-within:blur-md group-focus-within:from-amber-400/30 group-focus-within:to-orange-500/30 transition-all duration-300" />
              <div className="relative flex items-center">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-amber-600 group-focus-within:text-orange-600 transition-colors z-10" />
                <input
                  type="text"
                  placeholder="Search orders, customers, menu items..."
                  className="pl-12 pr-4 py-3 w-96 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg focus:shadow-xl"
                />
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <div className="flex items-center space-x-1 text-xs text-amber-600/60">
                    <kbd className="px-2 py-1 bg-amber-100/50 rounded border border-amber-200/50">
                      ⌘
                    </kbd>
                    <kbd className="px-2 py-1 bg-amber-100/50 rounded border border-amber-200/50">
                      K
                    </kbd>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right side */}
        <div className="flex items-center space-x-3">
          {/* Quick actions */}
          <div className="hidden sm:flex items-center space-x-2">
            <button className="p-2.5 text-amber-600 hover:text-orange-600 hover:bg-white/50 rounded-xl transition-all duration-300 backdrop-blur-sm border border-transparent hover:border-amber-200/50 shadow-lg hover:shadow-xl transform hover:scale-105">
              <HelpCircle className="h-5 w-5" />
            </button>
            <button className="p-2.5 text-amber-600 hover:text-orange-600 hover:bg-white/50 rounded-xl transition-all duration-300 backdrop-blur-sm border border-transparent hover:border-amber-200/50 shadow-lg hover:shadow-xl transform hover:scale-105">
              <MessageSquare className="h-5 w-5" />
            </button>
          </div>

          {/* Notifications */}
          <div className="relative">
            <button
              onClick={() => setShowNotifications(!showNotifications)}
              className="relative p-2.5 text-amber-600 hover:text-orange-600 hover:bg-white/50 rounded-xl transition-all duration-300 backdrop-blur-sm border border-transparent hover:border-amber-200/50 shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              <Bell className="h-5 w-5" />
              <span className="absolute -top-1 -right-1 h-5 w-5 bg-gradient-to-br from-red-500 to-red-600 text-white text-xs rounded-full flex items-center justify-center font-bold shadow-lg shadow-red-500/30 animate-pulse border-2 border-white">
                3
              </span>
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-br from-red-400/30 to-red-600/30 rounded-full blur-sm animate-pulse" />
            </button>

            {/* Notifications dropdown */}
            {showNotifications && (
              <div className="absolute right-0 mt-2 w-80 bg-white/95 backdrop-blur-xl rounded-xl shadow-2xl border border-amber-200/50 z-50 animate-in slide-in-from-top-2 duration-300">
                <div className="p-4 border-b border-amber-200/30 bg-gradient-to-r from-amber-50/50 to-orange-50/50">
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 bg-gradient-to-br from-amber-400 to-orange-500 rounded-lg flex items-center justify-center">
                      <Bell className="h-3.5 w-3.5 text-white" />
                    </div>
                    <h3 className="font-bold text-gray-800">Notifications</h3>
                    <div className="ml-auto flex items-center space-x-1">
                      <Star className="h-3 w-3 text-amber-500 animate-pulse" />
                      <span className="text-xs font-semibold text-amber-600">Live</span>
                    </div>
                  </div>
                </div>
                <div className="max-h-96 overflow-y-auto">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className="p-4 border-b border-amber-100/50 hover:bg-gradient-to-r hover:from-amber-50/30 hover:to-orange-50/30 transition-all duration-200 group"
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <div
                              className={`w-2 h-2 rounded-full ${
                                notification.type === 'order'
                                  ? 'bg-gradient-to-r from-blue-500 to-purple-600'
                                  : notification.type === 'warning'
                                    ? 'bg-gradient-to-r from-amber-500 to-orange-600'
                                    : 'bg-gradient-to-r from-emerald-500 to-green-600'
                              } animate-pulse shadow-lg`}
                            />
                            <h4 className="text-sm font-semibold text-gray-800 group-hover:text-gray-900">
                              {notification.title}
                            </h4>
                          </div>
                          <p className="text-sm text-gray-600 mt-1 group-hover:text-gray-700">
                            {notification.message}
                          </p>
                        </div>
                        <span className="text-xs text-amber-600/70 ml-2 font-medium">
                          {notification.time}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="p-3 border-t border-amber-200/30 bg-gradient-to-r from-amber-50/30 to-orange-50/30">
                  <button className="w-full text-center text-sm bg-gradient-to-r from-amber-500 to-orange-600 bg-clip-text text-transparent hover:from-amber-600 hover:to-orange-700 font-bold transition-all duration-200 hover:scale-105">
                    View all notifications
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* User profile */}
          <div className="relative">
            <button
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="flex items-center space-x-3 p-2.5 rounded-xl bg-white/50 hover:bg-white/80 transition-all duration-300 backdrop-blur-sm border border-amber-200/30 shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              <div className="relative w-9 h-9 bg-gradient-to-br from-amber-400 via-orange-500 to-red-600 rounded-full flex items-center justify-center shadow-lg shadow-orange-500/25">
                <span className="text-white text-sm font-bold drop-shadow-lg">JD</span>
                <div className="absolute -top-0.5 -right-0.5 w-3 h-3 bg-gradient-to-br from-emerald-400 to-green-500 rounded-full border-2 border-white">
                  <div className="absolute inset-0.5 bg-white rounded-full animate-pulse" />
                </div>
                {/* Crown badge for premium users */}
                <div className="absolute -top-1 -left-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full flex items-center justify-center">
                  <Crown className="h-2.5 w-2.5 text-white" />
                </div>
              </div>
              <div className="hidden sm:block text-left">
                <div className="flex items-center space-x-1">
                  <p className="text-sm font-bold text-gray-800">John Doe</p>
                  <Shield className="h-3 w-3 text-blue-600" />
                </div>
                <p className="text-xs text-amber-600 font-medium">Restaurant Owner</p>
              </div>
              <ChevronDown className="h-4 w-4 text-amber-600 transition-transform duration-200 group-hover:rotate-180" />
            </button>

            {/* User menu dropdown */}
            {showUserMenu && (
              <div className="absolute right-0 mt-2 w-64 bg-white/95 backdrop-blur-xl rounded-xl shadow-2xl border border-amber-200/50 z-50 animate-in slide-in-from-top-2 duration-300">
                <div className="p-4 border-b border-amber-200/30 bg-gradient-to-r from-amber-50/50 to-orange-50/50">
                  <div className="flex items-center space-x-3">
                    <div className="relative w-12 h-12 bg-gradient-to-br from-amber-400 via-orange-500 to-red-600 rounded-full flex items-center justify-center shadow-lg shadow-orange-500/25">
                      <span className="text-white font-bold text-lg drop-shadow-lg">JD</span>
                      <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full flex items-center justify-center">
                        <Crown className="h-2.5 w-2.5 text-white" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <p className="font-bold text-gray-800">John Doe</p>
                        <Shield className="h-3 w-3 text-blue-600" />
                        <Star className="h-3 w-3 text-amber-500 animate-pulse" />
                      </div>
                      <p className="text-sm text-amber-600 font-medium"><EMAIL></p>
                      <div className="flex items-center mt-1">
                        <div className="w-1.5 h-1.5 bg-emerald-500 rounded-full mr-1.5 animate-pulse" />
                        <span className="text-xs text-emerald-600 font-semibold">
                          Premium Account
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="py-2">
                  <button className="w-full flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-amber-50/30 hover:to-orange-50/30 transition-all duration-200 group">
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                      <User className="h-4 w-4 text-white" />
                    </div>
                    <span className="font-medium">Profile Settings</span>
                  </button>
                  <button className="w-full flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-amber-50/30 hover:to-orange-50/30 transition-all duration-200 group">
                    <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                      <Settings className="h-4 w-4 text-white" />
                    </div>
                    <span className="font-medium">Account Settings</span>
                  </button>
                  <hr className="my-2 border-amber-200/30" />
                  <button className="w-full flex items-center px-4 py-3 text-sm text-red-600 hover:bg-gradient-to-r hover:from-red-50/50 hover:to-red-100/50 transition-all duration-200 group">
                    <div className="w-8 h-8 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                      <LogOut className="h-4 w-4 text-white" />
                    </div>
                    <span className="font-medium">Sign Out</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Mobile search */}
      <div className="md:hidden px-4 pb-3">
        <div className="relative group">
          <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-orange-500/20 rounded-xl blur-sm group-focus-within:blur-md group-focus-within:from-amber-400/30 group-focus-within:to-orange-500/30 transition-all duration-300" />
          <div className="relative flex items-center">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-amber-600 group-focus-within:text-orange-600 transition-colors z-10" />
            <input
              type="text"
              placeholder="Search everything..."
              className="pl-12 pr-4 py-3 w-full bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg focus:shadow-xl"
            />
          </div>
        </div>
      </div>
    </header>
  )
}
