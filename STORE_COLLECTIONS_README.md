# Store Collections Documentation

This document describes the three new collections created for the YoFood platform: Store, StoreCategory, and StoreMenu. These collections are designed to be effective, easy to link, scalable, and high-performing.

## Overview

The system consists of three core collections that work together to create a comprehensive food delivery platform:

- **Store**: Represents individual restaurants, cafes, or food establishments
- **StoreCategory**: Organizes menu items into logical categories for easy navigation
- **StoreMenu**: Represents curated menu offerings with detailed item information

## Collections

### 1. Store Collection (`stores`)

The Store collection represents individual food establishments and includes all essential information for store owners and customers.

#### Key Features:
- **Basic Information**: Name, description, category, images, price range, cuisine types, tags
- **Contact & Location**: Complete address with coordinates, phone, email, website, social media
- **Operations**: Operating hours, delivery options, radius, fees, minimum orders
- **Analytics**: Views, favorites, orders tracking
- **Status Management**: Active/inactive, featured status
- **Ratings**: Average rating and count (calculated automatically)

#### Relationships:
- **Owner**: Links to Users collection
- **Categories**: Has many StoreCategory relationships
- **Menus**: Has many StoreMenu relationships

#### Access Control:
- **Create/Update/Delete**: Authenticated users only
- **Read**: Public access

### 2. StoreCategory Collection (`store-categories`)

Categories help customers navigate store offerings and provide powerful filtering capabilities.

#### Key Features:
- **Basic Information**: Name, description, image, icon, color theming, sort order
- **Search & Discovery**: Tags, keywords for enhanced searchability
- **Customer Filters**: Dietary restrictions, spice levels, allergen information
- **Promotions**: Category-level discounts and featured items with scheduling
- **Analytics**: Views, clicks, orders, revenue tracking
- **Status Management**: Active/inactive, featured status

#### Relationships:
- **Store**: Belongs to a specific store
- **Items**: Referenced by StoreMenu items

#### Access Control:
- **Create/Update/Delete**: Authenticated users only
- **Read**: Public access

### 3. StoreMenu Collection (`store-menus`)

Represents curated menu offerings with detailed item information and customer interaction features.

#### Key Features:
- **Basic Information**: Name, description, images, pricing, tags
- **Menu Items**: Detailed array of items with individual pricing, dietary info, allergens
- **Availability & Scheduling**: Time-limited menus, day-specific availability, time slots
- **Cross-selling**: Related menus and suggested items
- **Customer Features**: Ratings, reviews, favorites functionality
- **Analytics**: Views, orders, revenue, conversion rates

#### Relationships:
- **Store**: Belongs to a specific store
- **Category**: Optional category assignment
- **Related Menus**: Self-referencing for recommendations

#### Access Control:
- **Create/Update/Delete**: Authenticated users only
- **Read**: Public access

## Analytics & Tracking

All collections include comprehensive analytics tracking:

### Store Analytics:
- Page views
- Favorite count
- Total orders
- Performance metrics

### Category Analytics:
- Category views
- Click-through rates
- Orders from category
- Revenue attribution

### Menu Analytics:
- Menu views
- Order conversion
- Revenue tracking
- Customer favorites

## Hooks & Utilities

### Revalidation Hooks:
- Automatic cache invalidation when content changes
- SEO-friendly URL revalidation
- Sitemap updates

### Analytics Hooks:
- Real-time analytics updates
- View tracking utilities
- Favorites management
- Rating calculations

## Scalability Features

### Multi-tenant Support:
- Each store operates independently
- Isolated data and analytics
- Scalable relationship structure

### Performance Optimizations:
- Indexed fields for fast queries
- Efficient relationship loading
- Cached analytics data
- Optimized search capabilities

### Search & Discovery:
- Full-text search across all collections
- Tag-based filtering
- Keyword matching
- Advanced filtering options

## Usage Examples

### Creating a Store:
```typescript
const store = await payload.create({
  collection: 'stores',
  data: {
    name: 'Mario\'s Pizza',
    description: 'Authentic Italian pizza since 1985',
    category: 'restaurant',
    address: {
      street: '123 Main St',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      country: 'United States'
    },
    contact: {
      phone: '******-0123',
      email: '<EMAIL>'
    },
    operatingHours: [
      {
        day: 'monday',
        isOpen: true,
        openTime: '11:00',
        closeTime: '22:00'
      }
    ],
    deliveryOptions: {
      delivery: true,
      pickup: true,
      dineIn: true,
      deliveryRadius: 5,
      deliveryFee: 2.99,
      minimumOrder: 15
    }
  }
})
```

### Creating a Category:
```typescript
const category = await payload.create({
  collection: 'store-categories',
  data: {
    name: 'Pizza',
    description: 'Our signature pizzas made with fresh ingredients',
    store: storeId,
    tags: [
      { tag: 'italian' },
      { tag: 'pizza' },
      { tag: 'cheese' }
    ],
    filters: {
      dietary: [
        { type: 'vegetarian' },
        { type: 'gluten-free' }
      ]
    }
  }
})
```

### Creating a Menu:
```typescript
const menu = await payload.create({
  collection: 'store-menus',
  data: {
    name: 'Margherita Pizza',
    description: 'Classic pizza with tomato, mozzarella, and basil',
    store: storeId,
    category: categoryId,
    price: {
      amount: 18.99,
      currency: 'USD'
    },
    items: [
      {
        name: 'Small (10")',
        price: 14.99,
        dietary: [{ type: 'vegetarian' }],
        isAvailable: true
      },
      {
        name: 'Large (16")',
        price: 18.99,
        dietary: [{ type: 'vegetarian' }],
        isAvailable: true
      }
    ],
    availability: {
      isActive: true,
      daysAvailable: [
        { day: 'monday' },
        { day: 'tuesday' },
        { day: 'wednesday' },
        { day: 'thursday' },
        { day: 'friday' },
        { day: 'saturday' },
        { day: 'sunday' }
      ]
    }
  }
})
```

## Next Steps

1. **Generate Types**: Run `pnpm generate:types` to update TypeScript types
2. **Build Project**: Run `pnpm build` to compile the collections
3. **Test Collections**: Create sample data to test relationships
4. **Frontend Integration**: Implement frontend components to display store data
5. **API Endpoints**: Create custom API endpoints for advanced functionality

## Additional Features (Future Enhancements)

- **Reviews System**: Customer reviews and ratings
- **Order Management**: Integration with order processing
- **Inventory Tracking**: Real-time availability updates
- **Recommendation Engine**: AI-powered menu suggestions
- **Loyalty Programs**: Customer rewards and promotions
- **Multi-language Support**: Internationalization capabilities
