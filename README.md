# YoFood - Restaurant Management Platform

**Transform your restaurant into a digital empire with our all-in-one management platform.**

YoFood is a comprehensive restaurant management system built with Next.js, PayloadCMS, and modern web technologies. It provides both customer-facing features and a powerful merchant dashboard to streamline restaurant operations, boost sales, and create unforgettable customer experiences.

- A personal or enterprise-grade website, blog, or portfolio
- A content publishing platform with a fully featured publication workflow
- Exploring the capabilities of Payload

YoFood is designed for restaurants, cafes, and food service businesses looking to:

- **Digitize Operations**: Move from traditional order-taking to a modern digital platform
- **Increase Efficiency**: Streamline order management, inventory tracking, and customer service
- **Boost Revenue**: Leverage analytics, loyalty programs, and optimized workflows
- **Enhance Customer Experience**: Provide seamless ordering and service experiences

## ✨ Key Features

To spin up this example locally, follow these steps:

### Clone

### 🛠 Technical Features
- **Headless CMS**: Powered by PayloadCMS for flexible content management
- **Authentication**: Secure user authentication and role-based access
- **Live Preview**: Real-time content preview and editing
- **Draft System**: Content versioning and scheduled publishing
- **Media Management**: Optimized image handling and storage
- **Form Builder**: Dynamic form creation and handling

## 🚀 Quick Start

### Prerequisites

- Node.js 18.20.2+ or 20.9.0+
- pnpm 9+ or 10+
- PostgreSQL database (or MongoDB for development)

### Installation

1. **Clone the repository**
   ```bash
   git clone <your-repo-url> yo-food
   cd yo-food
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   ```
   
   Configure your environment variables:
   ```env
   # Database
   POSTGRES_URL=your_postgres_connection_string
   
   # Payload
   PAYLOAD_SECRET=your_secret_key
   
   # Next.js
   NEXT_PUBLIC_SERVER_URL=http://localhost:3000
   ```

4. **Database Setup**
   ```bash
   # Create and run migrations
   pnpm payload migrate:create
   pnpm payload migrate
   ```

5. **Start Development Server**
   ```bash
   pnpm dev
   ```

6. **Access the Application**
   - Frontend: `http://localhost:3000`
   - Admin Panel: `http://localhost:3000/admin`
   - Merchant Dashboard: `http://localhost:3000/merchant`

## 🏗 Project Structure

```
yo-food/
├── src/
│   ├── app/
│   │   ├── (frontend)/          # Customer-facing pages
│   │   │   ├── home/           # Homepage
│   │   │   ├── posts/          # Blog/content pages
│   │   │   ├── discover/       # Discovery features
│   │   │   └── search/         # Search functionality
│   │   ├── (merchant)/         # Restaurant management
│   │   │   └── merchant/       # Merchant dashboard & auth
│   │   └── (payload)/          # CMS admin interface
│   ├── collections/            # PayloadCMS collections
│   │   ├── Users/             # User management
│   │   ├── Posts/             # Blog posts
│   │   ├── Pages/             # Static pages
│   │   ├── Media/             # File uploads
│   │   └── Categories/        # Content categorization
│   ├── components/
│   │   ├── Merchant/          # Merchant dashboard components
│   │   │   └── Layout/        # Dashboard layout (Header, Sidebar, Footer)
│   │   └── ui/                # Shared UI components
│   ├── blocks/                # Content building blocks
│   ├── utilities/             # Helper functions
│   └── payload.config.ts      # PayloadCMS configuration
├── public/                    # Static assets
├── tests/                     # Test files
│   ├── e2e/                  # End-to-end tests
│   └── int/                  # Integration tests
└── docker-compose.yml         # Docker configuration
```

## 🛠 Development

### Available Scripts

```bash
# Development
pnpm dev              # Start development server
pnpm dev:prod         # Production-like development build

# Building
pnpm build            # Build for production
pnpm start            # Start production server

# Database
pnpm payload migrate:create  # Create new migration
pnpm payload migrate        # Run migrations

# Testing
pnpm test             # Run all tests
pnpm test:e2e         # Run end-to-end tests
pnpm test:int         # Run integration tests

# Linting
pnpm lint             # Check code quality
pnpm lint:fix         # Fix linting issues
```

### Database Migrations

When making schema changes:

1. **Development**: The app uses `push: true` for rapid development
2. **Production**: Create and run migrations:
   ```bash
   pnpm payload migrate:create
   pnpm payload migrate
   ```

### Docker Development

For containerized development:

```bash
# Start all services
docker-compose up

# Access the application
open http://localhost:3000
```

## 🏢 Business Model

### Target Users
- **Restaurant Owners**: Small to medium-sized restaurants
- **Cafe Operators**: Coffee shops and casual dining establishments  
- **Food Service Businesses**: Catering and food delivery services

### Subscription Tiers
- **Starter**: Basic order management and analytics
- **Professional**: Advanced features including inventory management
- **Enterprise**: Full-featured platform with custom integrations

### Key Metrics Tracked
- Daily/Monthly orders and revenue
- Customer acquisition and retention
- Menu item performance
- Operational efficiency metrics

## 🔧 Configuration

### PayloadCMS Collections

- **Users**: Authentication and user management
- **Posts**: Blog posts and content marketing
- **Pages**: Static pages and landing pages
- **Media**: Image and file management
- **Categories**: Content organization

### Key Integrations

- **Database**: PostgreSQL (production) / MongoDB (development)
- **Authentication**: Built-in PayloadCMS auth
- **Styling**: TailwindCSS + shadcn/ui
- **Forms**: React Hook Form
- **Icons**: Lucide React

## 🚀 Deployment

### Environment Setup

1. **Production Database**: Set up PostgreSQL
2. **Environment Variables**: Configure production settings
3. **Build Process**: Run `pnpm build`
4. **Migration**: Execute `pnpm payload migrate`
5. **Start**: Run `pnpm start`

### Deployment Options

- **Vercel**: One-click deployment with Vercel PostgreSQL
- **Payload Cloud**: Managed hosting solution
- **Self-hosted**: VPS or cloud platform deployment

## 🧪 Testing

### Test Coverage
- **E2E Tests**: Frontend user workflows
- **Integration Tests**: API and database operations
- **Unit Tests**: Component and utility testing

Run tests:
```bash
pnpm test           # All tests
pnpm test:e2e       # Playwright E2E tests
pnpm test:int       # Vitest integration tests
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the [PayloadCMS docs](https://payloadcms.com/docs)
- **Issues**: Report bugs via GitHub Issues
- **Community**: Join our Discord community

---

**Built with ❤️ for the restaurant industry**

Transform your restaurant operations with YoFood - where technology meets hospitality.