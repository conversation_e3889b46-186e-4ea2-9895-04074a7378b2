# YoFood - Cursor Rules

## 🎨 Design System & Styling Guidelines

### Core Design Principles
- **Modern Glassmorphism**: Use backdrop-blur, transparency, and layered effects
- **Food Discovery Focus**: Clean, modern design optimized for restaurant discovery
- **Premium Feel**: Gradients, shadows, and subtle animations for luxury experience
- **Mobile-First**: Responsive design with smooth breakpoints and touch-friendly interactions
- **Accessibility**: High contrast, proper focus states, and semantic HTML

---

## 🏗️ App Structure

### Frontend App (`app/(frontend)`)
- **Customer-facing food discovery app**
- **Mobile-optimized with bottom navigation**
- **Glassmorphism design with clean, modern aesthetics**
- **Focus on restaurant discovery, search, and user experience**

### Merchant App (`app/(merchant)`)
- **Business dashboard for restaurant owners**
- **Desktop-optimized with sidebar navigation**
- **Restaurant-inspired amber/orange color palette**
- **Focus on business management and analytics**

---

## 🎨 Color Palette

### Primary Colors (Restaurant Theme)
```css
/* Amber/Orange Gradient Family */
- Primary: from-amber-400 to-orange-600
- Secondary: from-orange-500 to-red-600
- Accent: from-yellow-400 to-amber-500

/* Status Colors */
- Success: from-emerald-400 to-green-600
- Warning: from-amber-500 to-orange-600
- Error: from-red-500 to-red-600
- Info: from-blue-400 to-indigo-600
```

### Background Colors
```css
/* Main Backgrounds */
- Page Background: bg-gradient-to-br from-gray-50 via-amber-50/80 to-orange-50/60
- Card Background: bg-white/40 backdrop-blur-xl
- Sidebar: bg-gradient-to-br from-black/90 via-amber-900/95 to-orange-900/90
- Header: bg-gradient-to-r from-white/95 via-amber-50/95 to-orange-50/95
```

---

## 🏗️ Component Structure Rules

### Layout Components
```typescript
// Always use the main Layout wrapper for dashboard pages
import { Layout } from '@/components/Merchant/Layout'

// Page structure should follow this pattern:
const DashboardPage = () => {
  return (
    <Layout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-amber-500/10 via-orange-500/10 to-red-500/10 backdrop-blur-xl rounded-2xl border border-amber-200/30 shadow-xl" />
          <div className="relative p-6 lg:p-8">
            {/* Header content */}
          </div>
        </div>
        
        {/* Page Content */}
        {/* ... */}
      </div>
    </Layout>
  )
}
```

### Card Components
```typescript
// Standard card structure with glassmorphism
<div className="relative group">
  <div className="absolute inset-0 bg-gradient-to-br from-[COLOR]/10 via-[COLOR]/10 to-[COLOR]/10 backdrop-blur-xl rounded-2xl border border-[COLOR]/30 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
  <div className="relative p-6">
    {/* Card content */}
  </div>
</div>
```

---

## 🎯 Styling Patterns

### Headers and Titles
```typescript
// Page titles
<h1 className="text-4xl font-black bg-gradient-to-r from-amber-600 via-orange-600 to-red-600 bg-clip-text text-transparent drop-shadow-sm">
  Page Title
</h1>

// Section titles
<h3 className="text-lg font-bold text-gray-800">Section Title</h3>

// With icon
<div className="flex items-center space-x-3 mb-6">
  <div className="w-10 h-10 bg-gradient-to-br from-[COLOR] to-[COLOR] rounded-xl flex items-center justify-center shadow-lg shadow-[COLOR]/25">
    <Icon className="h-5 w-5 text-white drop-shadow-lg" />
  </div>
  <div>
    <h3 className="text-lg font-bold text-gray-800">Title</h3>
    <div className="flex items-center space-x-1">
      <StatusIcon className="h-3 w-3 text-[COLOR] animate-pulse" />
      <span className="text-xs text-[COLOR] font-medium">Status</span>
    </div>
  </div>
</div>
```

### Buttons
```typescript
// Primary button
<button className="group flex items-center px-6 py-3 bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 text-white rounded-xl hover:from-amber-500 hover:via-orange-600 hover:to-red-600 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-orange-400/50">
  <Icon className="h-4 w-4 mr-2 group-hover:rotate-90 transition-transform duration-300" />
  <span className="font-bold">Button Text</span>
  <Zap className="h-4 w-4 ml-2 animate-pulse" />
</button>

// Secondary button
<button className="group flex items-center px-6 py-3 bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl hover:bg-white/90 hover:border-amber-300/70 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
  <Icon className="h-4 w-4 mr-2 text-amber-600 group-hover:text-orange-600 transition-colors" />
  <span className="font-semibold text-gray-700 group-hover:text-gray-800">Button Text</span>
</button>
```

### Status Badges
```typescript
// Status badge pattern
<span className={`px-3 py-1 text-xs font-bold rounded-full border shadow-lg ${
  status === 'active' 
    ? 'bg-gradient-to-r from-emerald-400/20 to-green-500/20 text-emerald-800 border-emerald-200/50'
    : status === 'pending'
    ? 'bg-gradient-to-r from-yellow-400/20 to-amber-500/20 text-amber-800 border-amber-200/50'
    : 'bg-gradient-to-r from-red-400/20 to-red-500/20 text-red-800 border-red-200/50'
}`}>
  {status.toUpperCase()}
</span>
```

### Metric Cards
```typescript
// Metric card structure
<div className="relative group">
  <div className="absolute inset-0 bg-gradient-to-br from-[COLOR]/20 to-[COLOR]/20 backdrop-blur-xl rounded-2xl border border-[COLOR]/50 shadow-xl group-hover:shadow-2xl transition-all duration-300" />
  <div className="relative p-6">
    <div className="flex items-center justify-between mb-4">
      <div>
        <p className="text-sm font-bold text-[COLOR] uppercase tracking-wide">Metric Label</p>
        <div className="flex items-center mt-1">
          <Star className="h-3 w-3 text-[COLOR] mr-1" />
          <span className="text-xs text-[COLOR] font-medium">Status</span>
        </div>
      </div>
      <div className="relative w-12 h-12 bg-gradient-to-br from-[COLOR] to-[COLOR] rounded-xl flex items-center justify-center shadow-lg shadow-[COLOR]/25 group-hover:scale-110 transition-transform duration-300">
        <Icon className="h-6 w-6 text-white drop-shadow-lg" />
        <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full animate-pulse" />
      </div>
    </div>
    <div className="text-3xl font-black text-[COLOR] mb-2">{value}</div>
    <div className="flex items-center justify-between">
      <div className="flex items-center">
        <TrendingUp className="h-4 w-4 text-[COLOR] mr-2" />
        <span className="text-sm text-[COLOR] font-bold">+{percentage}%</span>
      </div>
      <Sparkles className="h-4 w-4 text-[COLOR] animate-pulse" />
    </div>
  </div>
</div>
```

### Form Inputs
```typescript
// Enhanced input with glassmorphism
<div className="relative group">
  <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-orange-500/20 rounded-xl blur-sm group-focus-within:blur-md group-focus-within:from-amber-400/30 group-focus-within:to-orange-500/30 transition-all duration-300" />
  <div className="relative flex items-center">
    <Icon className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-amber-600 group-focus-within:text-orange-600 transition-colors z-10" />
    <input
      type="text"
      placeholder="Placeholder text..."
      className="pl-12 pr-4 py-3 w-full bg-white/80 backdrop-blur-sm border border-amber-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:border-amber-400/50 focus:bg-white/90 transition-all duration-300 text-gray-700 placeholder:text-amber-600/70 shadow-lg focus:shadow-xl"
    />
  </div>
</div>
```

---

## 🎭 Animation Guidelines

### Standard Animations
```css
/* Hover effects */
- transform hover:scale-105
- hover:shadow-xl
- transition-all duration-300

/* Loading states */
- animate-pulse (for status indicators)
- animate-spin (for loading spinners)

/* Entry animations */
- animate-in slide-in-from-top-2 duration-300 (for dropdowns)
- animate-fade-in-up (for page elements)

/* Icon animations */
- group-hover:rotate-12 (for interactive icons)
- group-hover:translate-x-2 (for arrows)
- group-hover:scale-110 (for buttons and cards)
```

### Stagger Animations
```typescript
// Use staggered animations for lists
{items.map((item, index) => (
  <div 
    key={item.id} 
    className={`animate-fade-in-up delay-${index * 100}`}
    style={{ animationDelay: `${index * 0.1}s` }}
  >
    {/* Item content */}
  </div>
))}
```

---

## 📱 Responsive Design Rules

### Breakpoint Usage
```typescript
// Standard responsive classes
- Mobile: Default (no prefix)
- Tablet: sm: (640px+)
- Desktop: lg: (1024px+)
- Large: xl: (1280px+)

// Grid patterns
- Mobile: grid-cols-1
- Tablet: md:grid-cols-2
- Desktop: lg:grid-cols-3 or lg:grid-cols-4

// Spacing
- Mobile: p-4, gap-4
- Desktop: lg:p-6, lg:gap-6
```

### Mobile-First Approach
```typescript
// Always start with mobile styles, then enhance
<div className="p-4 lg:p-8 space-y-4 lg:space-y-6">
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
    {/* Content */}
  </div>
</div>
```

---

## 🎯 Icon Usage Guidelines

### Icon Styling Patterns
```typescript
// Standard icon in button
<Icon className="h-4 w-4 mr-2 group-hover:rotate-12 transition-transform duration-300" />

// Icon in card header
<Icon className="h-5 w-5 text-white drop-shadow-lg" />

// Status icon
<Icon className="h-3 w-3 text-[COLOR] animate-pulse" />

// Large display icon
<Icon className="h-6 w-6 text-white drop-shadow-lg" />
```

### Icon Color Themes
```typescript
// Match icons to their container's color scheme
- Amber/Orange cards: text-amber-600, text-orange-600
- Success cards: text-emerald-600, text-green-600
- Info cards: text-blue-600, text-indigo-600
- Warning cards: text-amber-600, text-yellow-600
```

---

## 🎪 Special Effects

### Glassmorphism Backgrounds
```typescript
// Page background with particles
<div className="fixed inset-0 pointer-events-none overflow-hidden">
  <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-amber-50/80 to-orange-50/60" />
  {/* Floating particles */}
  {particles.map((particle) => (
    <div
      key={particle.id}
      className="absolute rounded-full bg-gradient-to-br from-amber-300/20 to-orange-400/20 animate-pulse"
      style={{
        left: `${particle.x}%`,
        top: `${particle.y}%`,
        width: `${particle.size}px`,
        height: `${particle.size}px`,
        animationDelay: `${particle.delay}s`,
      }}
    />
  ))}
</div>
```

### Premium Badges and Indicators
```typescript
// Crown/premium indicator
<div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full flex items-center justify-center">
  <Crown className="h-2.5 w-2.5 text-white" />
</div>

// Status dot
<div className="w-2.5 h-2.5 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full mr-2 animate-pulse shadow-lg shadow-green-500/50" />

// Notification badge
<span className="absolute -top-1 -right-1 h-5 w-5 bg-gradient-to-br from-red-500 to-red-600 text-white text-xs rounded-full flex items-center justify-center font-bold shadow-lg shadow-red-500/30 animate-pulse border-2 border-white">
  {count}
</span>
```

---

## 📋 File Naming Conventions

### Component Files
```
- Page components: `page.tsx` and `page.client.tsx`
- Layout components: `Layout/index.tsx`, `Header.tsx`, `Sidebar.tsx`, `Footer.tsx`
- Feature components: `FeatureName/index.tsx`
- UI components: `ComponentName.tsx`
```

### Styling Files
```
- Global styles: `globals.css`
- Component styles: `ComponentName.module.css` (if needed)
- Use Tailwind classes primarily, CSS modules only for complex animations
```

---

## 🎨 Code Quality Rules

### TypeScript Guidelines
```typescript
// Always use proper TypeScript interfaces
interface ComponentProps {
  children: React.ReactNode
  className?: string
  variant?: 'primary' | 'secondary' | 'success' | 'warning'
}

// Use proper event handlers
const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
  // Handle click
}
```

### Performance Optimizations
```typescript
// Use React.memo for components that don't change often
const MemoizedComponent = React.memo(Component)

// Use useCallback for event handlers
const handleSubmit = useCallback((data: FormData) => {
  // Handle submit
}, [dependencies])

// Use useMemo for expensive calculations
const expensiveValue = useMemo(() => {
  return calculateExpensiveValue(data)
}, [data])
```

---

## 🚀 Implementation Checklist

When creating new merchant dashboard pages/components:

- [ ] Use the Layout wrapper
- [ ] Apply glassmorphism backgrounds
- [ ] Include proper responsive breakpoints
- [ ] Add hover and focus states
- [ ] Use consistent color palette
- [ ] Include proper TypeScript types
- [ ] Add loading and error states
- [ ] Test on mobile devices
- [ ] Ensure accessibility compliance
- [ ] Add proper animations
- [ ] Include status indicators where relevant
- [ ] Use consistent spacing (space-y-6, gap-6, p-6)
- [ ] Add proper shadows and borders
- [ ] Include interactive elements (hover effects, animations)

---

## 🎯 Example Implementation

```typescript
'use client'

import React, { useState } from 'react'
import { Layout } from '@/components/Merchant/Layout'
import { 
  Icon1, 
  Icon2, 
  Sparkles, 
  TrendingUp 
} from 'lucide-react'

const NewDashboardPage = () => {
  const [loading, setLoading] = useState(false)

  return (
    <Layout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-amber-500/10 via-orange-500/10 to-red-500/10 backdrop-blur-xl rounded-2xl border border-amber-200/30 shadow-xl" />
          <div className="relative p-6 lg:p-8">
            <div className="flex items-center space-x-3 mb-2">
              <div className="w-12 h-12 bg-gradient-to-br from-amber-400 via-orange-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25">
                <Icon1 className="h-6 w-6 text-white drop-shadow-lg" />
              </div>
              <h1 className="text-4xl font-black bg-gradient-to-r from-amber-600 via-orange-600 to-red-600 bg-clip-text text-transparent drop-shadow-sm">
                Page Title
              </h1>
            </div>
          </div>
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Your content here */}
        </div>
      </div>
    </Layout>
  )
}

export default NewDashboardPage
```

---

## 🎨 Final Notes

- **Consistency is key**: Always follow these patterns for a cohesive experience
- **Performance matters**: Use proper React optimizations
- **Accessibility first**: Ensure all interactive elements are accessible
- **Mobile responsive**: Test on various screen sizes
- **Animation moderation**: Use animations to enhance, not distract
- **Color harmony**: Stick to the established palette for brand consistency

This design system ensures that every merchant dashboard page maintains the same premium, restaurant-inspired aesthetic with glassmorphism effects, warm color schemes, and smooth interactions.

---

# 🍽️ Frontend App Styling Guidelines (`app/(frontend)`)

## 🎨 Frontend Color Palette

### Primary Colors (Clean & Modern)
```css
/* Neutral Base */
- Background: bg-background (white/black based on theme)
- Foreground: text-foreground (high contrast text)
- Muted: text-muted-foreground (secondary text)

/* Accent Colors */
- Primary: text-primary (brand color)
- Success: text-success (open status, positive actions)
- Warning: text-warning (caution states)
- Error: text-error (closed status, errors)

/* Glass Effects */
- Glass: bg-white/10 backdrop-blur-xl (transparent overlays)
- Glass Card: bg-white/20 backdrop-blur-sm (card backgrounds)
- Glass Nav: bg-white/80 backdrop-blur-xl (navigation bars)
```

### Status Colors
```css
/* Business Status */
- Open: bg-success/80 text-success-foreground
- Closed: bg-destructive/80 text-destructive-foreground
- Featured: bg-gradient-primary text-white
- Trending: bg-gradient-amber text-white
```

---

## 🏗️ Frontend Component Structure

### Page Layout Pattern
```typescript
// Standard frontend page structure
const FrontendPage = () => {
  return (
    <div className="min-h-screen bg-background">
      <main className="pb-20 md:pb-0">
        {/* Hero/Header Section */}
        <section className="relative">
          {/* Content */}
        </section>

        {/* Content Sections */}
        <div className="px-4 space-y-8 py-6 relative">
          {/* Background blur overlay */}
          <div className="absolute inset-0 backdrop-blur-sm bg-white/5 rounded-3xl -mx-4 -my-6" />
          
          <div className="relative z-10 space-y-8">
            {/* Page content */}
          </div>
        </div>
      </main>
    </div>
  )
}
```

### Card Components
```typescript
// Standard glass card structure
<div className="glass-card hover-lift hover-glow">
  {/* Card content */}
</div>

// Business card with image
<div className="glass-card hover-lift hover-glow cursor-pointer group">
  <div className="relative overflow-hidden rounded-xl mb-4">
    <img className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105" />
    {/* Overlay effects */}
  </div>
  {/* Content */}
</div>
```

---

## 🎯 Frontend Styling Patterns

### Headers and Titles
```typescript
// Page titles
<h1 className="text-4xl sm:text-5xl lg:text-6xl font-display font-bold text-white drop-shadow-2xl">
  Page Title
</h1>

// Section titles
<h2 className="text-xl font-display font-semibold text-foreground">Section Title</h2>

// Subsection titles
<h3 className="text-lg font-semibold text-foreground">Subsection</h3>

// With gradient text
<span className="bg-gradient-to-r from-white via-amber-100 to-white bg-clip-text text-transparent">
  Gradient Text
</span>
```

### Buttons
```typescript
// Primary button
<button className="bg-primary text-primary-foreground px-6 py-3 rounded-2xl hover:bg-primary/90 transition-colors">
  Button Text
</button>

// Secondary button
<button className="bg-secondary text-secondary-foreground px-6 py-3 rounded-2xl hover:bg-secondary/90 transition-colors">
  Button Text
</button>

// Glass button
<button className="glass hover-lift hover-glow px-6 py-3 rounded-2xl transition-all duration-300">
  Glass Button
</button>

// Icon button
<button className="glass hover-lift hover-glow h-12 w-12 rounded-2xl flex items-center justify-center">
  <Icon className="h-5 w-5" />
</button>

// Rounded pill buttons
<button className="rounded-full px-4 py-2 bg-primary text-primary-foreground">
  Pill Button
</button>
```

### Status Badges
```typescript
// Open/Closed status
<span className="px-2 py-1 rounded-full text-xs font-medium backdrop-blur-sm bg-success/80 text-success-foreground">
  Open
</span>

// Feature badges
<Badge className="bg-gradient-primary text-white border-0 shadow-lg">
  Trending
</Badge>

// Location badge
<Badge variant="secondary" className="bg-primary/10 text-primary">
  <MapPin className="h-3 w-3 mr-1" />
  Live Location
</Badge>
```

### Form Inputs
```typescript
// Search input with glass effect
<div className="relative flex items-center">
  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
  <input className="flex h-12 w-full rounded-2xl glass px-11 py-3 text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary/20" />
</div>

// Enhanced search with backdrop blur
<input className="backdrop-blur-xl bg-white/98 border-white/50 text-gray-800 placeholder:text-gray-500 shadow-2xl hover:shadow-glow transition-all duration-300 h-14 text-base rounded-2xl" />
```

### Navigation Elements
```typescript
// Glass navigation bar
<div className="glass-nav px-4 py-4 border-b border-white/20">
  {/* Navigation content */}
</div>

// Bottom navigation (mobile)
<div className="fixed bottom-0 left-0 right-0 glass-nav border-t border-white/20">
  {/* Navigation items */}
</div>
```

### Business Cards
```typescript
// Standard business card
<div className="glass-card hover-lift hover-glow cursor-pointer group">
  <div className="relative overflow-hidden rounded-xl mb-4">
    <img className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105" />
    
    {/* Favorite button */}
    <button className="absolute top-3 right-3 p-2 rounded-full glass hover-glow">
      <Heart className="h-4 w-4" />
    </button>
    
    {/* Status badge */}
    <div className="absolute top-3 left-3">
      <span className="px-2 py-1 rounded-full text-xs font-medium backdrop-blur-sm bg-success/80">
        Open
      </span>
    </div>
  </div>
  
  <div className="space-y-3">
    <h3 className="font-semibold text-lg text-foreground line-clamp-1">Business Name</h3>
    <p className="text-muted-foreground text-sm">Category • Price Range</p>
    
    {/* Rating & Reviews */}
    <div className="flex items-center justify-between">
      <Rating rating={4.5} showNumber />
      <span className="text-xs text-muted-foreground">(123 reviews)</span>
    </div>
    
    {/* Location & Time */}
    <div className="flex items-center justify-between text-sm text-muted-foreground">
      <div className="flex items-center gap-1">
        <MapPin className="h-3 w-3" />
        <span>0.5 km</span>
      </div>
      <div className="flex items-center gap-1">
        <Clock className="h-3 w-3" />
        <span>15-30 min</span>
      </div>
    </div>
  </div>
</div>
```

### Category Grid
```typescript
// Category selection grid
<div className="grid grid-cols-4 sm:grid-cols-6 lg:grid-cols-8 gap-3">
  {categories.map((category) => (
    <button
      key={category.id}
      className="flex flex-col items-center gap-2 p-3 rounded-2xl transition-all duration-200 glass hover-lift hover-glow"
    >
      <div className="p-3 rounded-xl bg-secondary/50">
        <Icon className="h-5 w-5 text-orange-500" />
      </div>
      <span className="text-xs font-medium text-center text-muted-foreground">
        {category.name}
      </span>
    </button>
  ))}
</div>
```

### List Items
```typescript
// Business list item
<div className="glass-card hover-lift">
  <div className="flex items-start gap-4 p-4">
    <img className="w-20 h-20 rounded-xl object-cover" />
    <div className="flex-1 min-w-0">
      <h3 className="font-semibold text-foreground line-clamp-1">Business Name</h3>
      <p className="text-sm text-muted-foreground">Category</p>
      
      {/* Rating */}
      <div className="flex items-center gap-1 mt-2">
        <span className="text-sm font-medium text-foreground">4.5</span>
        <div className="flex">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-3 w-3 text-amber-400">⭐</div>
          ))}
        </div>
        <span className="text-xs text-muted-foreground">(123)</span>
      </div>
    </div>
    
    {/* Distance & Status */}
    <div className="text-right flex-shrink-0">
      <div className="text-sm font-medium text-foreground">0.5 km</div>
      <div className="text-xs text-muted-foreground">2 min walk</div>
      <Badge className="mt-1 text-xs">Open</Badge>
    </div>
  </div>
  
  {/* Action buttons */}
  <div className="px-4 pb-4 flex gap-2">
    <Button className="flex-1 rounded-full">View Details</Button>
    <Button variant="outline" className="rounded-full">
      <Navigation className="h-4 w-4" />
    </Button>
  </div>
</div>
```

---

## 🎭 Frontend Animation Guidelines

### Standard Animations
```css
/* Hover effects */
- hover-lift: transform hover:scale-105
- hover-glow: hover:shadow-glow
- transition-all duration-300

/* Glass effects */
- glass: backdrop-blur-xl bg-white/10
- glass-card: backdrop-blur-sm bg-white/20
- glass-nav: backdrop-blur-xl bg-white/80

/* Entry animations */
- animate-fade-up: fade in from bottom
- animate-scale-in: scale in from center
- animate-pulse: pulsing effect for status indicators
```

### Stagger Animations
```typescript
// Staggered list animations
{items.map((item, index) => (
  <div 
    key={item.id} 
    className="animate-fade-up"
    style={{ animationDelay: `${index * 50}ms` }}
  >
    {/* Item content */}
  </div>
))}
```

---

## 📱 Frontend Responsive Design

### Breakpoint Usage
```typescript
// Mobile-first approach
- Mobile: Default (no prefix) - 320px+
- Small: sm: (640px+) - tablets
- Medium: md: (768px+) - small laptops
- Large: lg: (1024px+) - desktops
- Extra Large: xl: (1280px+) - large screens

// Grid patterns
- Mobile: grid-cols-1, grid-cols-4 (categories)
- Tablet: sm:grid-cols-6 (categories)
- Desktop: lg:grid-cols-8 (categories), lg:grid-cols-3 (business cards)
```

### Mobile-Specific Patterns
```typescript
// Bottom padding for mobile navigation
<div className="pb-20 md:pb-0">
  {/* Content */}
</div>

// Mobile-optimized spacing
<div className="px-4 space-y-8 py-6">
  {/* Content */}
</div>

// Touch-friendly buttons
<button className="h-12 w-12 rounded-2xl">
  {/* Minimum 44px touch target */}
</button>
```

---

## 🎯 Frontend Icon Usage

### Icon Sizing
```typescript
// Small icons (status, metadata)
<Icon className="h-3 w-3" />

// Standard icons (buttons, navigation)
<Icon className="h-4 w-4" />

// Medium icons (card headers)
<Icon className="h-5 w-5" />

// Large icons (hero sections)
<Icon className="h-6 w-6" />

// Extra large icons (placeholders)
<Icon className="h-12 w-12" />
```

### Icon Colors
```typescript
// Primary actions
<Icon className="text-primary" />

// Secondary text
<Icon className="text-muted-foreground" />

// Status indicators
<Icon className="text-success" />
<Icon className="text-destructive" />

// White icons on dark backgrounds
<Icon className="text-white" />
```

---

## 🎪 Frontend Special Effects

### Glassmorphism Effects
```typescript
// Page background with subtle overlay
<div className="min-h-screen bg-background">
  <div className="absolute inset-0 backdrop-blur-sm bg-white/5 rounded-3xl -mx-4 -my-6" />
</div>

// Hero section with gradient overlays
<div className="absolute inset-0 bg-gradient-to-b from-black/50 via-black/40 to-black/70" />
<div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-transparent to-amber-500/10" />
```

### Premium Badges and Indicators
```typescript
// Feature badges with gradients
<Badge className="bg-gradient-primary text-white border-0 shadow-lg">
  Trending
</Badge>

// Status indicators
<div className="w-2.5 h-2.5 bg-success rounded-full animate-pulse" />

// Premium glow effects
<div className="hover:shadow-glow transition-all duration-300" />
```

---

## 📋 Frontend File Naming Conventions

### Component Files
```
- Page components: `page.tsx` and `page.client.tsx`
- UI components: `ComponentName.tsx`
- Layout components: `Layout/index.tsx`
- Feature components: `FeatureName/index.tsx`
```

### Styling Approach
```
- Use Tailwind classes primarily
- Custom CSS only for complex animations
- Follow mobile-first responsive design
- Maintain consistent spacing and typography
```

---

## 🚀 Frontend Implementation Checklist

When creating new frontend pages/components:

- [ ] Use mobile-first responsive design
- [ ] Apply glassmorphism effects consistently
- [ ] Include proper touch targets (min 44px)
- [ ] Add hover and focus states
- [ ] Use consistent spacing (px-4, space-y-8, py-6)
- [ ] Include proper TypeScript types
- [ ] Add loading and error states
- [ ] Test on mobile devices
- [ ] Ensure accessibility compliance
- [ ] Add smooth animations
- [ ] Include proper shadows and borders
- [ ] Use semantic HTML structure
- [ ] Optimize for performance
- [ ] Include proper image optimization

---

## 🎯 Frontend Example Implementation

```typescript
'use client'

import { useState } from 'react'
import { SearchInput } from '@/components/ui/search-input'
import { BusinessCard } from '@/components/ui/business-card'
import { MapPin, User, Sparkles } from 'lucide-react'

const FrontendPage = () => {
  const [searchQuery, setSearchQuery] = useState('')

  return (
    <div className="min-h-screen bg-background">
      <main className="pb-20 md:pb-0">
        {/* Hero Section */}
        <section className="relative overflow-hidden min-h-[60vh]">
          <div className="absolute inset-0 bg-gradient-to-b from-black/50 via-black/40 to-black/70" />
          <div className="relative z-10 px-4 py-12 space-y-8">
            <div className="flex items-center justify-between">
              <button className="flex items-center gap-3 glass rounded-2xl px-5 py-3 hover-lift hover-glow">
                <MapPin className="h-4 w-4 text-primary" />
                <div className="text-left">
                  <p className="text-xs text-white/70">Deliver to</p>
                  <p className="text-sm font-semibold text-white">Current Location</p>
                </div>
              </button>
              <button className="glass hover-lift hover-glow h-12 w-12 rounded-2xl">
                <User className="h-5 w-5" />
              </button>
            </div>

            <div className="text-center space-y-4">
              <h1 className="text-4xl sm:text-5xl font-display font-bold text-white drop-shadow-2xl">
                Discover Amazing
                <span className="block bg-gradient-to-r from-white via-amber-100 to-white bg-clip-text text-transparent">
                  Local Flavors
                </span>
              </h1>
            </div>

            <div className="max-w-lg mx-auto">
              <SearchInput
                placeholder="Search for restaurants, dishes, or cuisines..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="backdrop-blur-xl bg-white/98 border-white/50 text-gray-800 placeholder:text-gray-500 shadow-2xl hover:shadow-glow h-14 text-base"
              />
            </div>
          </div>
        </section>

        {/* Content Sections */}
        <div className="px-4 space-y-8 py-6 relative">
          <div className="absolute inset-0 backdrop-blur-sm bg-white/5 rounded-3xl -mx-4 -my-6" />
          <div className="relative z-10 space-y-8">
            {/* Your content here */}
          </div>
        </div>
      </main>
    </div>
  )
}

export default FrontendPage
```

---

## 🎨 Frontend Final Notes

- **Mobile-first**: Always design for mobile, then enhance for larger screens
- **Touch-friendly**: Ensure all interactive elements are at least 44px
- **Performance**: Optimize images and use proper loading states
- **Accessibility**: Include proper ARIA labels and keyboard navigation
- **Consistency**: Follow the established patterns for a cohesive experience
- **Glassmorphism**: Use subtle transparency and blur effects
- **Clean aesthetics**: Focus on readability and user experience

This design system ensures that every frontend page maintains a clean, modern, mobile-optimized experience perfect for food discovery and restaurant browsing.
